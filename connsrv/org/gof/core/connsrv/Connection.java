package org.gof.core.connsrv;

import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import io.netty.handler.codec.http.websocketx.BinaryWebSocketFrame;
import org.gof.core.*;
import org.gof.core.connsrv.netty.RC4;
import org.gof.core.gen.proxy.DistrClass;
import org.gof.core.gen.proxy.DistrMethod;
import org.gof.core.scheduler.ScheduleTask;
import org.gof.core.statistics.StatisticsMSG;
import org.gof.core.support.*;
import org.gof.core.support.log.LogCore;
import org.gof.demo.worldsrv.msg.MsgLogin;
import org.gof.demo.worldsrv.support.Log;
import org.slf4j.Logger;

import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

import static org.gof.core.support.Config.DEBUG_PATTERN;

@DistrClass(
        importClass = {Chunk.class, ConnectionStatus.class, List.class}
)
public class Connection extends Service {
    //默认Gate阶段游戏服务器的CallPoint地址
    private static CallPoint pointGate = new CallPoint(Distr.NODE_DEFAULT, Distr.PORT_DEFAULT, Distr.SERV_GATE);
    private RC4 _rc4 = null;
    protected static int connNum = 0; //连接数
    protected long m_id = 0;
    protected ConnectionStatus m_status = new ConnectionStatus();
    protected boolean m_closed = false;
    protected boolean m_delayCloseRequested = false;
    protected boolean m_waitResponse = false;
    protected TickTimer m_waitTimer = new TickTimer();
    protected TickTimer m_closeTimer = new TickTimer();

    protected final Channel channel;
    protected final LinkedBlockingQueue<byte[]> datas = new LinkedBlockingQueue<>();

    private final Logger log = LogCore.conn;

    /**
     * 上次发送登录包的时间戳
     */
    public long lastloginTime = 0;
    /* 连接验证 */
    private static long CONN_CHECK_TIMES = 5;        //连接检查超时次数
    public TickTimer connCheckTickTimer = new TickTimer(15 * Time.SEC);    //连接检查计时器
    public int connCheckIncreaseTimes = 0;                //累计连续未收到返回的次数

    //消息缓存队列
    ConnectionBuf conBuf = new ConnectionBuf();

    //ping的响应
    public static int PING_MSGID;
    //ping的返回状态
    public static int ECHO_PING_MSGID;

    private long lastPingTime = 0;
    private static long pingTimeOut = 30 * Time.SEC;

    static {
        //Window下认为是开发环境 避免Debug造成的超时断开连接 这里加大检查次数
        if (Sys.isWin() || DEBUG_PATTERN) {
            CONN_CHECK_TIMES = Long.MAX_VALUE;
        }
        if(Utils.isDebugMode()){
            pingTimeOut = 10 * Time.MIN;
    }
    }

    /**
     * FIXME 获取连接ID Entty 4.1恢复channel.id()函数前 先用这个暂代
     *
     * @param channel
     * @return
     */
    public static int getId(Channel channel) {
        return channel.hashCode();
    }

    public void initEncryptKey(byte[] key) {
        _rc4 = new RC4(key);
    }

    public void putData(byte[] data) {
        try {
            datas.put(data);
        } catch (InterruptedException e) {
            throw new SysException(e);
        }
    }

    public Connection(Channel channel, Port port) {
//		m_port = ConnStartup.CONN_NODE.getRandomPort();
        super(port);
        m_id = getId(channel);
        this.channel = channel;
    }

    public Connection(Channel channel, Port port, String nodeId, String portId, String servId) {
        super(port);
        m_id = getId(channel);
        this.channel = channel;
        if (pointGate == null) {
            pointGate = new CallPoint(nodeId, portId, servId);
        }
        m_status.clientIP = getClientIP(channel);
    }
    public static String getClientIP(Channel channel) {
        InetSocketAddress insocket = (InetSocketAddress) channel.remoteAddress();
        return  insocket.getAddress().getHostAddress();
    }
    @DistrMethod(argsImmutable = true)
    public void updateStatus(String node, String port) {
        m_status.stageNodeId = node;
        m_status.stagePortId = port;
    }

    @DistrMethod(argsImmutable = true)
    public void updateStatus(ConnectionStatus status) {
        this.handleContinue(status);
    }

    @DistrMethod(argsImmutable = true)
    public void setStatus(int status) {
        m_status.status = status;
        this.handleContinue(m_status);
    }

    @DistrMethod(argsImmutable = true)
    public void close() {
        //延迟100毫秒再真正断开 有些消息可能还没传递完毕
        scheduleOnce(new ScheduleTask() {
            @Override
            public void execute() {
                Log.temp.info("===Channel关闭 account={}, humanId={}, nodeId={}, portId={},chanelId={}, channelIsOpen={}",
                        m_status.account, m_status.humanId, m_status.stageNodeId, m_status.stagePortId, channel.id(), channel.isOpen());
                channel.close();
            }
        }, 100);
    }

    @DistrMethod(argsImmutable = true)
    public void sendMsg(int msgId, Chunk msgbuf) {
        if (!channel.isActive()) return;
        if (!channel.isWritable()) return;

        try {
            // 构造头文件数据
            ByteBuf buf = channel.alloc().buffer(8 + msgbuf.length);
            buf.writeInt(msgbuf.length + 8);
            buf.writeInt(msgId);
            buf.writeBytes(msgbuf.buffer.array(),msgbuf.offset,msgbuf.length);

            if (Config.IS_WEBSOCKET) {
                channel.write(new BinaryWebSocketFrame(buf));
            } else {
                ChannelFuture future = channel.write(buf);
                future.addListener((ChannelFutureListener) channelFuture -> {
                    if (!channelFuture.isSuccess()) {
                        LogCore.conn.error("Failed to write message with ID {}", msgId, channelFuture.cause());
                    }
                });
            }

            // 信息统计
            StatisticsMSG.send(msgId, msgbuf.length);

            channel.flush();
        } catch (Exception e) {
            // 处理可能的异常，如缓冲区分配失败等
            LogCore.conn.error("Error sending message with ID {}", msgId, e);
        } finally {
            msgbuf.release();
            msgbuf = null;
        }
    }


    public void sendBytesWithLength(byte[] buffer) {
        if (!channel.isActive()) return;
        if (!channel.isWritable()){
            return;
        }

        ByteBuf buf = channel.alloc().buffer(4 + buffer.length);
        buf.writeInt(buffer.length + 4);
        buf.writeBytes(buffer);

        if(Config.IS_WEBSOCKET){
            //websocket发送消息需要将原有byte[]转换成BinaryWebSocketFrame对象
            BinaryWebSocketFrame frame = new BinaryWebSocketFrame(buf);
            channel.writeAndFlush(frame);
        } else {
            channel.writeAndFlush(buf);
        }

    }

    @DistrMethod(argsImmutable = true)
    public void sendMsg(List<Integer> idList, List<Chunk> chunkList) {
        if (!channel.isActive() || !channel.isWritable()) {
            return;
        }

        if (idList.size() != chunkList.size()) {
            LogCore.conn.error("ID列表与Chunk列表大小不匹配");
            return;
        }

        try {
            for (int i = 0; i < idList.size(); i++) {
                sendMsg(idList.get(i), chunkList.get(i));
            }
            channel.flush();
        } catch (Exception e) {
            LogCore.conn.error("sent message error {}", e);
        }
    }

    /**
     * 初始化消息缓存 只有在掉线重连的时候才用的上
     *
     * @param connPoint
     */
    @DistrMethod(argsImmutable = true)
    public void initMsgBuf(CallPoint connPoint) {
        ConnectionProxy connPrx = ConnectionProxy.newInstance(connPoint);
        if (connPrx != null) {
            connPrx.getMsgBuf();
            connPrx.listenResult(this::_result_InitMsgBuf);
        } else {
            LogCore.conn.debug("conn point expired [{}]", connPoint);
        }
    }

    public void _result_InitMsgBuf(Param results, Param context) {
        ConnectionBuf conBuf = results.get();
        this.conBuf = conBuf;
    }

    @DistrMethod(argsImmutable = true)
    public void replaceMsgBuf(ConnectionBuf conBuf) {
        this.conBuf = conBuf;
    }

    @DistrMethod(argsImmutable = true)
    public void getMsgBuf() {
        port.returns(conBuf);
    }

    /**
     * 发送消息缓存的数据 只有在消息重新连接的时候才用的上
     *
     * @param
     */
    @DistrMethod(argsImmutable = true)
    public void sendMsgBuf() {
        //获得对应connect的conBuf信息
        conBuf.sendMsg(channel);
    }

    /**
     * 延时关闭
     * 2秒后生效
     */
    public void closeDelay() {
        if (!this.m_closed && !m_delayCloseRequested) {
            m_delayCloseRequested = true;
            m_closeTimer.start(300);
        }
    }

    @Override
    public Object getId() {
        return m_id;
    }

    public void startup() {
        super.startup();
        port.addQueue(new PortPulseQueue(this) {

            @Override
            public void execute(Port port) {
                ConnPort portConn = (ConnPort) port;
                portConn.openConnection(param.<Connection>get());
            }
        });
    }

    @Override
    public void pulseOverride() {
        //如果延时关闭已经到时间 那么就进行关闭
        if (m_delayCloseRequested && m_closeTimer.isOnce(port.getTimeCurrent())) {
            //清理延时关闭状态 避免再次触发
            m_delayCloseRequested = false;
            m_closeTimer.stop();
            log.info("conn_state_trace[{}] conn_step_2000 ===通知处理游戏业务处理玩家离线逻辑 connId={}, account={}, humanId={}, nodeId={}, portId={}",
                    m_id, m_id, m_status.account, m_status.humanId, m_status.stageNodeId, m_status.stagePortId);
            //进行关闭处理
            handleClose();
        }

        //如果计时器超时 那么就进行关闭连接操作
        if (m_waitTimer.isStarted() && m_waitTimer.isOnce(port.getTimeCurrent())) {
            //日志
            log.info("conn_state_trace[{}] conn_step_2001 ===登陆阶段超时，主动关闭连接：connId={}, account={}, humanId={}, connId={}", m_id, m_id, m_status.account, m_status.humanId);
            //关闭连接
            channel.close();
        }

        //如果长时间没有收到ping则关闭(第一次登录除外)
        if (lastPingTime > Time.DAY && lastloginTime > 0 && lastloginTime != lastPingTime && (lastPingTime + pingTimeOut) < port.getTimeCurrent()) {
            //日志
            log.info("conn_state_trace[{}] conn_step_2002 ===长时间没有收到ping消息，主动关闭连接：connId={}, account={}, humanId={}, lastPingTime={}, currTime={}",
                    m_id, m_id, m_status.account, m_status.humanId,lastPingTime, port.getTimeCurrent());

            // 重置lastPintTime 防止在真正关闭之前多次打印
            lastPingTime = port.getTimeCurrent();
            lastloginTime = 0;
            //关闭连接
            channel.close();
        }

        //连接验证
        connCheck();
    }

    /**
     * 连接关闭
     * 同时其他服务器进行信息清理
     */
    public void handleClose() {
        m_closed = true;
        switch (m_status.status) {
            case ConnectionStatus.STATUS_LOGIN:
            case ConnectionStatus.STATUS_GATE: {
                port.call(true, pointGate, CallSeamMethodKey.ACCOUNT_LOST.ordinal(),"CallSeamMethodKey.ACCOUNT_LOST.ordinal()", new Object[]{m_id});
            }
            break;
            case ConnectionStatus.STATUS_PLAYING: {
                CallPoint toPoint = new CallPoint(m_status.stageNodeId, m_status.stagePortId, m_status.humanId);
                port.call(true, toPoint, CallSeamMethodKey.WORLD_LOST.ordinal(), "CallSeamMethodKey.WORLD_LOST.ordinal()",new Object[]{m_id});
            }
            break;
            case ConnectionStatus.STATUS_LOSTED: {
            }
            break;
        }

        port.addQueue(new PortPulseQueue(this) {
            @Override
            public void execute(Port port) {
                ConnPort portConn = (ConnPort) port;
                portConn.closeConnection(param.<Connection>get());
            }
        });
    }

    protected void handleContinue(ConnectionStatus status) {
        if (status.status == ConnectionStatus.STATUS_LOSTED) {
            this.close();
        } else {
            m_status = status;
            if(status.isReconnect){
                log.error("===重新连接，connId={}, accunt={}, humanId={},lastPingTime={}, lastloginTime={}, nowTime={}",
                        m_status.account, m_status.humanId, Connection.getId(channel), lastPingTime, lastloginTime, port.getTimeCurrent());
              
                lastloginTime = 0;
                lastPingTime = port.getTimeCurrent();
            }
        }

        m_waitResponse = false;
        m_waitTimer.stop();
    }

    public void handleInput() {
        while (!datas.isEmpty()) {
            try {
                handleIncoming(datas.poll());
            } catch (Exception e) {
                // 不做任何处理 仅仅抛出异常
                // 避免因为一个任务的出错 造成后续的任务无法继续执行 需要等到下一个心跳
                log.error("", e);
            }
        }
    }

    /**
     * 消息接受
     *
     * @param msgbuf
     */
    private void handleIncoming(byte[] msgbuf) {
        if (msgbuf == null) {
            return;
        }
        // TODO 临时去掉加密，方便客户端测试
        if(!Config.IS_WEBSOCKET){
            if (Config.CONN_ENCRYPT && _rc4 != null) {
                _rc4.crypt(msgbuf, 4, -1);
            }
        }
        int mid = Utils.bytesToInt(msgbuf, 4);
        if (mid == PING_MSGID) {
            if(lastloginTime == 0 && lastPingTime != 0){
                lastloginTime = port.getTimeCurrent();
            }
            lastPingTime = port.getTimeCurrent();
            MsgLogin.heart_beat_s2c.Builder msgPing = MsgLogin.heart_beat_s2c.newBuilder();
            msgPing.setSvrTime(Utils.getTimeSec());
            sendMsg(ECHO_PING_MSGID, new Chunk(msgPing));
            if(S.isTestLog){
                Log.temp.info("===ping={}, m_id={}", msgPing, m_id);
            }
            return;
        }
        if(257==mid){//打印收到的登录消息
            log.info("conn_state_trace[{}] conn_step_1010 收到登录消息", m_id);
        }
        //信息统计
        StatisticsMSG.recevice(msgbuf);

        //关闭或关闭中的连接 不在接收新的客户端请求
        if (m_closed || m_delayCloseRequested) {
            if (log.isDebugEnabled() && mid != 1211 && mid != 1212) {
                log.debug("连接关闭中，忽略收到的客户端消息：id={}", mid);
            }
            return;
        }
        if(S.isTestLog){
//            Log.temp.info("====收到客户端消息， msgid={}", mid);
        }
		if(Utils.isDebugMode()){
            if(!(mid >= 3330 && mid <= 3338)){
                Log.temp.info("====收到客户端消息， msgid={}", mid);
            }
        }


        if (m_status.status == ConnectionStatus.STATUS_PLAYING) {
            CallPoint toPoint = new CallPoint(m_status.stageNodeId, m_status.stagePortId, m_status.humanId);
            port.call(true, toPoint, CallSeamMethodKey.WORLD_MSG.ordinal(),"CallSeamMethodKey.WORLD_MSG.ordinal()", new Object[]{m_id, msgbuf});
            return;
        }
        switch (m_status.status) {
            case ConnectionStatus.STATUS_LOGIN:
            case ConnectionStatus.STATUS_GATE: {
                port.call(true, pointGate, CallSeamMethodKey.ACCOUNT_MSG.ordinal(),"CallSeamMethodKey.ACCOUNT_MSG.ordinal()", new Object[]{m_id, m_status, msgbuf});
            }
            break;
            case ConnectionStatus.STATUS_LOSTED: {
                if (log.isDebugEnabled()) {
                    log.debug("接到客户端信息，但是玩家已是断线状态，忽略此消息。");
                }
                Log.temp.error("===接到客户端信息，但是玩家已是断线状态，忽略此消息。connId={}, status={}, account={}, humanId={}", m_id, m_status.status, m_status.account, m_status.humanId);
            }
            break;
            default: {
                log.warn("转发消息时发现错误的玩家状态：{}", m_status.status);
            }
        }
    }

    /**
     * 连接验证
     */
    private void connCheck() {
        //验证间隔
        if (!connCheckTickTimer.isPeriod(Port.getTime())) {
            return;
        }
        //避免由于Debug断点等情况 造成瞬间发送多个检查请求
        connCheckTickTimer.reStart();

        //清理掉超时的连接
        if (connCheckIncreaseTimes >= CONN_CHECK_TIMES) {
            //日志
            log.info("conn_state_trace[{}] conn_step_2003 清理错误的玩家连接：conn={}, status={}, account={}, humanId={},{}",
                    m_id, m_id, m_status.status, m_status.account, m_status.humanId, connCheckIncreaseTimes );
            //清理
            channel.close();
            return;
        }

        //根据状态进行验证
        switch (m_status.status) {
            case ConnectionStatus.STATUS_LOGIN:
            case ConnectionStatus.STATUS_GATE: {
                port.call(true, pointGate, CallSeamMethodKey.ACCOUNT_CHECK.ordinal(),"CallSeamMethodKey.ACCOUNT_CHECK.ordinal()", new Object[]{m_id});
                port.listenResult(this::_result_pulseConnCheck, new Param().put("mid",m_id).put("humanId", m_status.humanId).put("status", m_status.status));
            }
            break;
            case ConnectionStatus.STATUS_PLAYING: {
                CallPoint toPoint = new CallPoint(m_status.stageNodeId, m_status.stagePortId, m_status.humanId);
                port.call(true, toPoint, CallSeamMethodKey.WORLD_CHECK.ordinal(), "CallSeamMethodKey.WORLD_CHECK.ordinal()",new Object[]{m_id});
                port.listenResult(this::_result_pulseConnCheck, new Param().put("mid",m_id).put("humanId", m_status.humanId).put("status", m_status.status)
                        .put("accoount", m_status.account).put("nodeId", m_status.stageNodeId).put("portId", m_status.stagePortId));
            }
            break;
            default:
                Log.temp.error("===humanId={}, status={}, mid={}", m_status.humanId, m_status.status, m_id);
                break;
        }

        //累加连接检查次数
        connCheckIncreaseTimes++;
    }

    public void _result_pulseConnCheck(boolean timeout, Param results, Param context) {
        if(timeout){
            Log.temp.error("===检测玩家链接timeout={}, results={}, context={}", timeout, results, context);
            return;
        }
        boolean has = results.get();
        //没找到这个连接
        if (!has) return;

        //收到过就清空累计次数
        connCheckIncreaseTimes = 0;
    }

    /**
     * 获取连接状态字符串信息
     *
     * @return
     */
    public String getStatusString() {
        return m_status.toString();
    }

    public ConnectionStatus getStatus() {
        return m_status;
    }

    public static void registerPing(int csId, int scId) {
        PING_MSGID = csId;
        ECHO_PING_MSGID = scId;
    }

    /**
     * 获取IP地址
     */
    @DistrMethod
    public void getIpAddress() {
        if (channel != null && channel.remoteAddress() != null
                && channel.remoteAddress() instanceof InetSocketAddress) {
            InetSocketAddress socketAddress = (InetSocketAddress) channel.remoteAddress();
            InetAddress ia = socketAddress.getAddress();
            port.returns(ia.getHostAddress());
        }
        port.returns("");
    }

    @DistrMethod
    public void checkConn(){
        port.returns("result", true);
    }

}
