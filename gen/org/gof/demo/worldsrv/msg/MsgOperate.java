// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.operate.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgOperate {
  private MsgOperate() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface operate_share_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_share_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_share_info_c2s}
   */
  public static final class operate_share_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_share_info_c2s)
      operate_share_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_share_info_c2s.newBuilder() to construct.
    private operate_share_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_share_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_share_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_share_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_share_info_c2s)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_share_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_share_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_share_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<operate_share_info_c2s>() {
      @java.lang.Override
      public operate_share_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_share_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_share_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_share_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_share_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 opt = 1;</code>
     * @return The opt.
     */
    int getOpt();

    /**
     * <code>uint32 num = 2;</code>
     * @return The num.
     */
    int getNum();

    /**
     * <code>repeated uint32 geted = 3 [packed = false];</code>
     * @return A list containing the geted.
     */
    java.util.List<java.lang.Integer> getGetedList();
    /**
     * <code>repeated uint32 geted = 3 [packed = false];</code>
     * @return The count of geted.
     */
    int getGetedCount();
    /**
     * <code>repeated uint32 geted = 3 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The geted at the given index.
     */
    int getGeted(int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_share_info_s2c}
   */
  public static final class operate_share_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_share_info_s2c)
      operate_share_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_share_info_s2c.newBuilder() to construct.
    private operate_share_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_share_info_s2c() {
      geted_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_share_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c.Builder.class);
    }

    public static final int OPT_FIELD_NUMBER = 1;
    private int opt_ = 0;
    /**
     * <code>uint32 opt = 1;</code>
     * @return The opt.
     */
    @java.lang.Override
    public int getOpt() {
      return opt_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_ = 0;
    /**
     * <code>uint32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    public static final int GETED_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList geted_ =
        emptyIntList();
    /**
     * <code>repeated uint32 geted = 3 [packed = false];</code>
     * @return A list containing the geted.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getGetedList() {
      return geted_;
    }
    /**
     * <code>repeated uint32 geted = 3 [packed = false];</code>
     * @return The count of geted.
     */
    public int getGetedCount() {
      return geted_.size();
    }
    /**
     * <code>repeated uint32 geted = 3 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The geted at the given index.
     */
    public int getGeted(int index) {
      return geted_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (opt_ != 0) {
        output.writeUInt32(1, opt_);
      }
      if (num_ != 0) {
        output.writeUInt32(2, num_);
      }
      for (int i = 0; i < geted_.size(); i++) {
        output.writeUInt32(3, geted_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (opt_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, opt_);
      }
      if (num_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, num_);
      }
      {
        int dataSize = 0;
        for (int i = 0; i < geted_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(geted_.getInt(i));
        }
        size += dataSize;
        size += 1 * getGetedList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c) obj;

      if (getOpt()
          != other.getOpt()) return false;
      if (getNum()
          != other.getNum()) return false;
      if (!getGetedList()
          .equals(other.getGetedList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + OPT_FIELD_NUMBER;
      hash = (53 * hash) + getOpt();
      hash = (37 * hash) + NUM_FIELD_NUMBER;
      hash = (53 * hash) + getNum();
      if (getGetedCount() > 0) {
        hash = (37 * hash) + GETED_FIELD_NUMBER;
        hash = (53 * hash) + getGetedList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_share_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_share_info_s2c)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        opt_ = 0;
        num_ = 0;
        geted_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.opt_ = opt_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          geted_.makeImmutable();
          result.geted_ = geted_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c.getDefaultInstance()) return this;
        if (other.getOpt() != 0) {
          setOpt(other.getOpt());
        }
        if (other.getNum() != 0) {
          setNum(other.getNum());
        }
        if (!other.geted_.isEmpty()) {
          if (geted_.isEmpty()) {
            geted_ = other.geted_;
            geted_.makeImmutable();
            bitField0_ |= 0x00000004;
          } else {
            ensureGetedIsMutable();
            geted_.addAll(other.geted_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                opt_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                num_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                int v = input.readUInt32();
                ensureGetedIsMutable();
                geted_.addInt(v);
                break;
              } // case 24
              case 26: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureGetedIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  geted_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int opt_ ;
      /**
       * <code>uint32 opt = 1;</code>
       * @return The opt.
       */
      @java.lang.Override
      public int getOpt() {
        return opt_;
      }
      /**
       * <code>uint32 opt = 1;</code>
       * @param value The opt to set.
       * @return This builder for chaining.
       */
      public Builder setOpt(int value) {

        opt_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 opt = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpt() {
        bitField0_ = (bitField0_ & ~0x00000001);
        opt_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>uint32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>uint32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {

        num_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList geted_ = emptyIntList();
      private void ensureGetedIsMutable() {
        if (!geted_.isModifiable()) {
          geted_ = makeMutableCopy(geted_);
        }
        bitField0_ |= 0x00000004;
      }
      /**
       * <code>repeated uint32 geted = 3 [packed = false];</code>
       * @return A list containing the geted.
       */
      public java.util.List<java.lang.Integer>
          getGetedList() {
        geted_.makeImmutable();
        return geted_;
      }
      /**
       * <code>repeated uint32 geted = 3 [packed = false];</code>
       * @return The count of geted.
       */
      public int getGetedCount() {
        return geted_.size();
      }
      /**
       * <code>repeated uint32 geted = 3 [packed = false];</code>
       * @param index The index of the element to return.
       * @return The geted at the given index.
       */
      public int getGeted(int index) {
        return geted_.getInt(index);
      }
      /**
       * <code>repeated uint32 geted = 3 [packed = false];</code>
       * @param index The index to set the value at.
       * @param value The geted to set.
       * @return This builder for chaining.
       */
      public Builder setGeted(
          int index, int value) {

        ensureGetedIsMutable();
        geted_.setInt(index, value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 geted = 3 [packed = false];</code>
       * @param value The geted to add.
       * @return This builder for chaining.
       */
      public Builder addGeted(int value) {

        ensureGetedIsMutable();
        geted_.addInt(value);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 geted = 3 [packed = false];</code>
       * @param values The geted to add.
       * @return This builder for chaining.
       */
      public Builder addAllGeted(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureGetedIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, geted_);
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 geted = 3 [packed = false];</code>
       * @return This builder for chaining.
       */
      public Builder clearGeted() {
        geted_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_share_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_share_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_share_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<operate_share_info_s2c>() {
      @java.lang.Override
      public operate_share_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_share_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_share_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_share_reward_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_share_reward_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_share_reward_c2s}
   */
  public static final class operate_share_reward_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_share_reward_c2s)
      operate_share_reward_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_share_reward_c2s.newBuilder() to construct.
    private operate_share_reward_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_share_reward_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_share_reward_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s) obj;

      if (getId()
          != other.getId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_share_reward_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_share_reward_c2s)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_share_reward_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_share_reward_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_share_reward_c2s>
        PARSER = new com.google.protobuf.AbstractParser<operate_share_reward_c2s>() {
      @java.lang.Override
      public operate_share_reward_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_share_reward_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_share_reward_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_share_reward_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_share_reward_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    int getId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_share_reward_s2c}
   */
  public static final class operate_share_reward_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_share_reward_s2c)
      operate_share_reward_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_share_reward_s2c.newBuilder() to construct.
    private operate_share_reward_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_share_reward_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_share_reward_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    private int id_ = 0;
    /**
     * <code>uint32 id = 1;</code>
     * @return The id.
     */
    @java.lang.Override
    public int getId() {
      return id_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (id_ != 0) {
        output.writeUInt32(1, id_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (id_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, id_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c) obj;

      if (getId()
          != other.getId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ID_FIELD_NUMBER;
      hash = (53 * hash) + getId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_share_reward_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_share_reward_s2c)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.id_ = id_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c.getDefaultInstance()) return this;
        if (other.getId() != 0) {
          setId(other.getId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                id_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int id_ ;
      /**
       * <code>uint32 id = 1;</code>
       * @return The id.
       */
      @java.lang.Override
      public int getId() {
        return id_;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(int value) {

        id_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        id_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_share_reward_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_share_reward_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_share_reward_s2c>
        PARSER = new com.google.protobuf.AbstractParser<operate_share_reward_s2c>() {
      @java.lang.Override
      public operate_share_reward_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_share_reward_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_share_reward_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_share_reward_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_subscribe_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_subscribe_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string uid = 1;</code>
     * @return The uid.
     */
    java.lang.String getUid();
    /**
     * <code>string uid = 1;</code>
     * @return The bytes for uid.
     */
    com.google.protobuf.ByteString
        getUidBytes();

    /**
     * <code>string client_id = 2;</code>
     * @return The clientId.
     */
    java.lang.String getClientId();
    /**
     * <code>string client_id = 2;</code>
     * @return The bytes for clientId.
     */
    com.google.protobuf.ByteString
        getClientIdBytes();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_subscribe> 
        getListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_subscribe getList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
     */
    int getListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_subscribeOrBuilder> 
        getListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_subscribeOrBuilder getListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_subscribe_c2s}
   */
  public static final class operate_subscribe_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_subscribe_c2s)
      operate_subscribe_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_subscribe_c2s.newBuilder() to construct.
    private operate_subscribe_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_subscribe_c2s() {
      uid_ = "";
      clientId_ = "";
      list_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_subscribe_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_subscribe_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_subscribe_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s.Builder.class);
    }

    public static final int UID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object uid_ = "";
    /**
     * <code>string uid = 1;</code>
     * @return The uid.
     */
    @java.lang.Override
    public java.lang.String getUid() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        uid_ = s;
        return s;
      }
    }
    /**
     * <code>string uid = 1;</code>
     * @return The bytes for uid.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getUidBytes() {
      java.lang.Object ref = uid_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        uid_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CLIENT_ID_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private volatile java.lang.Object clientId_ = "";
    /**
     * <code>string client_id = 2;</code>
     * @return The clientId.
     */
    @java.lang.Override
    public java.lang.String getClientId() {
      java.lang.Object ref = clientId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        clientId_ = s;
        return s;
      }
    }
    /**
     * <code>string client_id = 2;</code>
     * @return The bytes for clientId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClientIdBytes() {
      java.lang.Object ref = clientId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clientId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LIST_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_subscribe> list_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_subscribe> getListList() {
      return list_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_subscribeOrBuilder> 
        getListOrBuilderList() {
      return list_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
     */
    @java.lang.Override
    public int getListCount() {
      return list_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_subscribe getList(int index) {
      return list_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_subscribeOrBuilder getListOrBuilder(
        int index) {
      return list_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(uid_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, uid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(clientId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, clientId_);
      }
      for (int i = 0; i < list_.size(); i++) {
        output.writeMessage(3, list_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(uid_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, uid_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(clientId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, clientId_);
      }
      for (int i = 0; i < list_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, list_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s) obj;

      if (!getUid()
          .equals(other.getUid())) return false;
      if (!getClientId()
          .equals(other.getClientId())) return false;
      if (!getListList()
          .equals(other.getListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + UID_FIELD_NUMBER;
      hash = (53 * hash) + getUid().hashCode();
      hash = (37 * hash) + CLIENT_ID_FIELD_NUMBER;
      hash = (53 * hash) + getClientId().hashCode();
      if (getListCount() > 0) {
        hash = (37 * hash) + LIST_FIELD_NUMBER;
        hash = (53 * hash) + getListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_subscribe_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_subscribe_c2s)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_subscribe_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_subscribe_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        uid_ = "";
        clientId_ = "";
        if (listBuilder_ == null) {
          list_ = java.util.Collections.emptyList();
        } else {
          list_ = null;
          listBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_subscribe_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s result) {
        if (listBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            list_ = java.util.Collections.unmodifiableList(list_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.list_ = list_;
        } else {
          result.list_ = listBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.uid_ = uid_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.clientId_ = clientId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s.getDefaultInstance()) return this;
        if (!other.getUid().isEmpty()) {
          uid_ = other.uid_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (!other.getClientId().isEmpty()) {
          clientId_ = other.clientId_;
          bitField0_ |= 0x00000002;
          onChanged();
        }
        if (listBuilder_ == null) {
          if (!other.list_.isEmpty()) {
            if (list_.isEmpty()) {
              list_ = other.list_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureListIsMutable();
              list_.addAll(other.list_);
            }
            onChanged();
          }
        } else {
          if (!other.list_.isEmpty()) {
            if (listBuilder_.isEmpty()) {
              listBuilder_.dispose();
              listBuilder_ = null;
              list_ = other.list_;
              bitField0_ = (bitField0_ & ~0x00000004);
              listBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getListFieldBuilder() : null;
            } else {
              listBuilder_.addAllMessages(other.list_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                uid_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                clientId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000002;
                break;
              } // case 18
              case 26: {
                org.gof.demo.worldsrv.msg.Define.p_subscribe m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_subscribe.parser(),
                        extensionRegistry);
                if (listBuilder_ == null) {
                  ensureListIsMutable();
                  list_.add(m);
                } else {
                  listBuilder_.addMessage(m);
                }
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object uid_ = "";
      /**
       * <code>string uid = 1;</code>
       * @return The uid.
       */
      public java.lang.String getUid() {
        java.lang.Object ref = uid_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          uid_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string uid = 1;</code>
       * @return The bytes for uid.
       */
      public com.google.protobuf.ByteString
          getUidBytes() {
        java.lang.Object ref = uid_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          uid_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string uid = 1;</code>
       * @param value The uid to set.
       * @return This builder for chaining.
       */
      public Builder setUid(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        uid_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string uid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearUid() {
        uid_ = getDefaultInstance().getUid();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string uid = 1;</code>
       * @param value The bytes for uid to set.
       * @return This builder for chaining.
       */
      public Builder setUidBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        uid_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.lang.Object clientId_ = "";
      /**
       * <code>string client_id = 2;</code>
       * @return The clientId.
       */
      public java.lang.String getClientId() {
        java.lang.Object ref = clientId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          clientId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string client_id = 2;</code>
       * @return The bytes for clientId.
       */
      public com.google.protobuf.ByteString
          getClientIdBytes() {
        java.lang.Object ref = clientId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clientId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string client_id = 2;</code>
       * @param value The clientId to set.
       * @return This builder for chaining.
       */
      public Builder setClientId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        clientId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>string client_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearClientId() {
        clientId_ = getDefaultInstance().getClientId();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>string client_id = 2;</code>
       * @param value The bytes for clientId to set.
       * @return This builder for chaining.
       */
      public Builder setClientIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        clientId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_subscribe> list_ =
        java.util.Collections.emptyList();
      private void ensureListIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          list_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_subscribe>(list_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_subscribe, org.gof.demo.worldsrv.msg.Define.p_subscribe.Builder, org.gof.demo.worldsrv.msg.Define.p_subscribeOrBuilder> listBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_subscribe> getListList() {
        if (listBuilder_ == null) {
          return java.util.Collections.unmodifiableList(list_);
        } else {
          return listBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public int getListCount() {
        if (listBuilder_ == null) {
          return list_.size();
        } else {
          return listBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_subscribe getList(int index) {
        if (listBuilder_ == null) {
          return list_.get(index);
        } else {
          return listBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public Builder setList(
          int index, org.gof.demo.worldsrv.msg.Define.p_subscribe value) {
        if (listBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureListIsMutable();
          list_.set(index, value);
          onChanged();
        } else {
          listBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public Builder setList(
          int index, org.gof.demo.worldsrv.msg.Define.p_subscribe.Builder builderForValue) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.set(index, builderForValue.build());
          onChanged();
        } else {
          listBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public Builder addList(org.gof.demo.worldsrv.msg.Define.p_subscribe value) {
        if (listBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureListIsMutable();
          list_.add(value);
          onChanged();
        } else {
          listBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public Builder addList(
          int index, org.gof.demo.worldsrv.msg.Define.p_subscribe value) {
        if (listBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureListIsMutable();
          list_.add(index, value);
          onChanged();
        } else {
          listBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public Builder addList(
          org.gof.demo.worldsrv.msg.Define.p_subscribe.Builder builderForValue) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.add(builderForValue.build());
          onChanged();
        } else {
          listBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public Builder addList(
          int index, org.gof.demo.worldsrv.msg.Define.p_subscribe.Builder builderForValue) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.add(index, builderForValue.build());
          onChanged();
        } else {
          listBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public Builder addAllList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_subscribe> values) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, list_);
          onChanged();
        } else {
          listBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public Builder clearList() {
        if (listBuilder_ == null) {
          list_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          listBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public Builder removeList(int index) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.remove(index);
          onChanged();
        } else {
          listBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_subscribe.Builder getListBuilder(
          int index) {
        return getListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_subscribeOrBuilder getListOrBuilder(
          int index) {
        if (listBuilder_ == null) {
          return list_.get(index);  } else {
          return listBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_subscribeOrBuilder> 
           getListOrBuilderList() {
        if (listBuilder_ != null) {
          return listBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(list_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_subscribe.Builder addListBuilder() {
        return getListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_subscribe.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_subscribe.Builder addListBuilder(
          int index) {
        return getListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_subscribe.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_subscribe list = 3;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_subscribe.Builder> 
           getListBuilderList() {
        return getListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_subscribe, org.gof.demo.worldsrv.msg.Define.p_subscribe.Builder, org.gof.demo.worldsrv.msg.Define.p_subscribeOrBuilder> 
          getListFieldBuilder() {
        if (listBuilder_ == null) {
          listBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_subscribe, org.gof.demo.worldsrv.msg.Define.p_subscribe.Builder, org.gof.demo.worldsrv.msg.Define.p_subscribeOrBuilder>(
                  list_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          list_ = null;
        }
        return listBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_subscribe_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_subscribe_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_subscribe_c2s>
        PARSER = new com.google.protobuf.AbstractParser<operate_subscribe_c2s>() {
      @java.lang.Override
      public operate_subscribe_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_subscribe_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_subscribe_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_subscribe_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_forum_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_forum_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_info_c2s}
   */
  public static final class operate_forum_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_forum_info_c2s)
      operate_forum_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_forum_info_c2s.newBuilder() to construct.
    private operate_forum_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_forum_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_forum_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_forum_info_c2s)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_forum_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_forum_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_forum_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<operate_forum_info_c2s>() {
      @java.lang.Override
      public operate_forum_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_forum_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_forum_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_forum_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_forum_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_reward> 
        getRewardStatusList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_forum_reward getRewardStatus(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
     */
    int getRewardStatusCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder> 
        getRewardStatusOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder getRewardStatusOrBuilder(
        int index);

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getTaskProcessList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getTaskProcess(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
     */
    int getTaskProcessCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getTaskProcessOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getTaskProcessOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_info_s2c}
   */
  public static final class operate_forum_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_forum_info_s2c)
      operate_forum_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_forum_info_s2c.newBuilder() to construct.
    private operate_forum_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_forum_info_s2c() {
      rewardStatus_ = java.util.Collections.emptyList();
      taskProcess_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_forum_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c.Builder.class);
    }

    public static final int REWARD_STATUS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_reward> rewardStatus_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_reward> getRewardStatusList() {
      return rewardStatus_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder> 
        getRewardStatusOrBuilderList() {
      return rewardStatus_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
     */
    @java.lang.Override
    public int getRewardStatusCount() {
      return rewardStatus_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_forum_reward getRewardStatus(int index) {
      return rewardStatus_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder getRewardStatusOrBuilder(
        int index) {
      return rewardStatus_.get(index);
    }

    public static final int TASK_PROCESS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> taskProcess_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getTaskProcessList() {
      return taskProcess_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getTaskProcessOrBuilderList() {
      return taskProcess_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
     */
    @java.lang.Override
    public int getTaskProcessCount() {
      return taskProcess_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getTaskProcess(int index) {
      return taskProcess_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getTaskProcessOrBuilder(
        int index) {
      return taskProcess_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < rewardStatus_.size(); i++) {
        output.writeMessage(1, rewardStatus_.get(i));
      }
      for (int i = 0; i < taskProcess_.size(); i++) {
        output.writeMessage(2, taskProcess_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < rewardStatus_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, rewardStatus_.get(i));
      }
      for (int i = 0; i < taskProcess_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, taskProcess_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c) obj;

      if (!getRewardStatusList()
          .equals(other.getRewardStatusList())) return false;
      if (!getTaskProcessList()
          .equals(other.getTaskProcessList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRewardStatusCount() > 0) {
        hash = (37 * hash) + REWARD_STATUS_FIELD_NUMBER;
        hash = (53 * hash) + getRewardStatusList().hashCode();
      }
      if (getTaskProcessCount() > 0) {
        hash = (37 * hash) + TASK_PROCESS_FIELD_NUMBER;
        hash = (53 * hash) + getTaskProcessList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_forum_info_s2c)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (rewardStatusBuilder_ == null) {
          rewardStatus_ = java.util.Collections.emptyList();
        } else {
          rewardStatus_ = null;
          rewardStatusBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (taskProcessBuilder_ == null) {
          taskProcess_ = java.util.Collections.emptyList();
        } else {
          taskProcess_ = null;
          taskProcessBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c result) {
        if (rewardStatusBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rewardStatus_ = java.util.Collections.unmodifiableList(rewardStatus_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rewardStatus_ = rewardStatus_;
        } else {
          result.rewardStatus_ = rewardStatusBuilder_.build();
        }
        if (taskProcessBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            taskProcess_ = java.util.Collections.unmodifiableList(taskProcess_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.taskProcess_ = taskProcess_;
        } else {
          result.taskProcess_ = taskProcessBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c.getDefaultInstance()) return this;
        if (rewardStatusBuilder_ == null) {
          if (!other.rewardStatus_.isEmpty()) {
            if (rewardStatus_.isEmpty()) {
              rewardStatus_ = other.rewardStatus_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRewardStatusIsMutable();
              rewardStatus_.addAll(other.rewardStatus_);
            }
            onChanged();
          }
        } else {
          if (!other.rewardStatus_.isEmpty()) {
            if (rewardStatusBuilder_.isEmpty()) {
              rewardStatusBuilder_.dispose();
              rewardStatusBuilder_ = null;
              rewardStatus_ = other.rewardStatus_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rewardStatusBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRewardStatusFieldBuilder() : null;
            } else {
              rewardStatusBuilder_.addAllMessages(other.rewardStatus_);
            }
          }
        }
        if (taskProcessBuilder_ == null) {
          if (!other.taskProcess_.isEmpty()) {
            if (taskProcess_.isEmpty()) {
              taskProcess_ = other.taskProcess_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureTaskProcessIsMutable();
              taskProcess_.addAll(other.taskProcess_);
            }
            onChanged();
          }
        } else {
          if (!other.taskProcess_.isEmpty()) {
            if (taskProcessBuilder_.isEmpty()) {
              taskProcessBuilder_.dispose();
              taskProcessBuilder_ = null;
              taskProcess_ = other.taskProcess_;
              bitField0_ = (bitField0_ & ~0x00000002);
              taskProcessBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTaskProcessFieldBuilder() : null;
            } else {
              taskProcessBuilder_.addAllMessages(other.taskProcess_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_forum_reward m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_forum_reward.parser(),
                        extensionRegistry);
                if (rewardStatusBuilder_ == null) {
                  ensureRewardStatusIsMutable();
                  rewardStatus_.add(m);
                } else {
                  rewardStatusBuilder_.addMessage(m);
                }
                break;
              } // case 10
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (taskProcessBuilder_ == null) {
                  ensureTaskProcessIsMutable();
                  taskProcess_.add(m);
                } else {
                  taskProcessBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_reward> rewardStatus_ =
        java.util.Collections.emptyList();
      private void ensureRewardStatusIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rewardStatus_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_forum_reward>(rewardStatus_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_forum_reward, org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder> rewardStatusBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_reward> getRewardStatusList() {
        if (rewardStatusBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewardStatus_);
        } else {
          return rewardStatusBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public int getRewardStatusCount() {
        if (rewardStatusBuilder_ == null) {
          return rewardStatus_.size();
        } else {
          return rewardStatusBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_reward getRewardStatus(int index) {
        if (rewardStatusBuilder_ == null) {
          return rewardStatus_.get(index);
        } else {
          return rewardStatusBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public Builder setRewardStatus(
          int index, org.gof.demo.worldsrv.msg.Define.p_forum_reward value) {
        if (rewardStatusBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardStatusIsMutable();
          rewardStatus_.set(index, value);
          onChanged();
        } else {
          rewardStatusBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public Builder setRewardStatus(
          int index, org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder builderForValue) {
        if (rewardStatusBuilder_ == null) {
          ensureRewardStatusIsMutable();
          rewardStatus_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardStatusBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public Builder addRewardStatus(org.gof.demo.worldsrv.msg.Define.p_forum_reward value) {
        if (rewardStatusBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardStatusIsMutable();
          rewardStatus_.add(value);
          onChanged();
        } else {
          rewardStatusBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public Builder addRewardStatus(
          int index, org.gof.demo.worldsrv.msg.Define.p_forum_reward value) {
        if (rewardStatusBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardStatusIsMutable();
          rewardStatus_.add(index, value);
          onChanged();
        } else {
          rewardStatusBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public Builder addRewardStatus(
          org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder builderForValue) {
        if (rewardStatusBuilder_ == null) {
          ensureRewardStatusIsMutable();
          rewardStatus_.add(builderForValue.build());
          onChanged();
        } else {
          rewardStatusBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public Builder addRewardStatus(
          int index, org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder builderForValue) {
        if (rewardStatusBuilder_ == null) {
          ensureRewardStatusIsMutable();
          rewardStatus_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardStatusBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public Builder addAllRewardStatus(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_forum_reward> values) {
        if (rewardStatusBuilder_ == null) {
          ensureRewardStatusIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewardStatus_);
          onChanged();
        } else {
          rewardStatusBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public Builder clearRewardStatus() {
        if (rewardStatusBuilder_ == null) {
          rewardStatus_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rewardStatusBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public Builder removeRewardStatus(int index) {
        if (rewardStatusBuilder_ == null) {
          ensureRewardStatusIsMutable();
          rewardStatus_.remove(index);
          onChanged();
        } else {
          rewardStatusBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder getRewardStatusBuilder(
          int index) {
        return getRewardStatusFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder getRewardStatusOrBuilder(
          int index) {
        if (rewardStatusBuilder_ == null) {
          return rewardStatus_.get(index);  } else {
          return rewardStatusBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder> 
           getRewardStatusOrBuilderList() {
        if (rewardStatusBuilder_ != null) {
          return rewardStatusBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewardStatus_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder addRewardStatusBuilder() {
        return getRewardStatusFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_forum_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder addRewardStatusBuilder(
          int index) {
        return getRewardStatusFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_forum_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_status = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder> 
           getRewardStatusBuilderList() {
        return getRewardStatusFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_forum_reward, org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder> 
          getRewardStatusFieldBuilder() {
        if (rewardStatusBuilder_ == null) {
          rewardStatusBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_forum_reward, org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder>(
                  rewardStatus_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rewardStatus_ = null;
        }
        return rewardStatusBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> taskProcess_ =
        java.util.Collections.emptyList();
      private void ensureTaskProcessIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          taskProcess_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(taskProcess_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> taskProcessBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getTaskProcessList() {
        if (taskProcessBuilder_ == null) {
          return java.util.Collections.unmodifiableList(taskProcess_);
        } else {
          return taskProcessBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public int getTaskProcessCount() {
        if (taskProcessBuilder_ == null) {
          return taskProcess_.size();
        } else {
          return taskProcessBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getTaskProcess(int index) {
        if (taskProcessBuilder_ == null) {
          return taskProcess_.get(index);
        } else {
          return taskProcessBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public Builder setTaskProcess(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (taskProcessBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskProcessIsMutable();
          taskProcess_.set(index, value);
          onChanged();
        } else {
          taskProcessBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public Builder setTaskProcess(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (taskProcessBuilder_ == null) {
          ensureTaskProcessIsMutable();
          taskProcess_.set(index, builderForValue.build());
          onChanged();
        } else {
          taskProcessBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public Builder addTaskProcess(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (taskProcessBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskProcessIsMutable();
          taskProcess_.add(value);
          onChanged();
        } else {
          taskProcessBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public Builder addTaskProcess(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (taskProcessBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskProcessIsMutable();
          taskProcess_.add(index, value);
          onChanged();
        } else {
          taskProcessBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public Builder addTaskProcess(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (taskProcessBuilder_ == null) {
          ensureTaskProcessIsMutable();
          taskProcess_.add(builderForValue.build());
          onChanged();
        } else {
          taskProcessBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public Builder addTaskProcess(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (taskProcessBuilder_ == null) {
          ensureTaskProcessIsMutable();
          taskProcess_.add(index, builderForValue.build());
          onChanged();
        } else {
          taskProcessBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public Builder addAllTaskProcess(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (taskProcessBuilder_ == null) {
          ensureTaskProcessIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, taskProcess_);
          onChanged();
        } else {
          taskProcessBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public Builder clearTaskProcess() {
        if (taskProcessBuilder_ == null) {
          taskProcess_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          taskProcessBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public Builder removeTaskProcess(int index) {
        if (taskProcessBuilder_ == null) {
          ensureTaskProcessIsMutable();
          taskProcess_.remove(index);
          onChanged();
        } else {
          taskProcessBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getTaskProcessBuilder(
          int index) {
        return getTaskProcessFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getTaskProcessOrBuilder(
          int index) {
        if (taskProcessBuilder_ == null) {
          return taskProcess_.get(index);  } else {
          return taskProcessBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getTaskProcessOrBuilderList() {
        if (taskProcessBuilder_ != null) {
          return taskProcessBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(taskProcess_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addTaskProcessBuilder() {
        return getTaskProcessFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addTaskProcessBuilder(
          int index) {
        return getTaskProcessFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_process = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getTaskProcessBuilderList() {
        return getTaskProcessFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getTaskProcessFieldBuilder() {
        if (taskProcessBuilder_ == null) {
          taskProcessBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  taskProcess_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          taskProcess_ = null;
        }
        return taskProcessBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_forum_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_forum_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_forum_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<operate_forum_info_s2c>() {
      @java.lang.Override
      public operate_forum_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_forum_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_forum_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_forum_trigger_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_forum_trigger_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getTriggerListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getTriggerList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
     */
    int getTriggerListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getTriggerListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getTriggerListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_trigger_c2s}
   */
  public static final class operate_forum_trigger_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_forum_trigger_c2s)
      operate_forum_trigger_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_forum_trigger_c2s.newBuilder() to construct.
    private operate_forum_trigger_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_forum_trigger_c2s() {
      triggerList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_forum_trigger_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_trigger_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_trigger_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s.Builder.class);
    }

    public static final int TRIGGER_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> triggerList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getTriggerListList() {
      return triggerList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getTriggerListOrBuilderList() {
      return triggerList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
     */
    @java.lang.Override
    public int getTriggerListCount() {
      return triggerList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getTriggerList(int index) {
      return triggerList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getTriggerListOrBuilder(
        int index) {
      return triggerList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < triggerList_.size(); i++) {
        output.writeMessage(1, triggerList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < triggerList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, triggerList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s) obj;

      if (!getTriggerListList()
          .equals(other.getTriggerListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getTriggerListCount() > 0) {
        hash = (37 * hash) + TRIGGER_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getTriggerListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_trigger_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_forum_trigger_c2s)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_trigger_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_trigger_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (triggerListBuilder_ == null) {
          triggerList_ = java.util.Collections.emptyList();
        } else {
          triggerList_ = null;
          triggerListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_trigger_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s result) {
        if (triggerListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            triggerList_ = java.util.Collections.unmodifiableList(triggerList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.triggerList_ = triggerList_;
        } else {
          result.triggerList_ = triggerListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s.getDefaultInstance()) return this;
        if (triggerListBuilder_ == null) {
          if (!other.triggerList_.isEmpty()) {
            if (triggerList_.isEmpty()) {
              triggerList_ = other.triggerList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureTriggerListIsMutable();
              triggerList_.addAll(other.triggerList_);
            }
            onChanged();
          }
        } else {
          if (!other.triggerList_.isEmpty()) {
            if (triggerListBuilder_.isEmpty()) {
              triggerListBuilder_.dispose();
              triggerListBuilder_ = null;
              triggerList_ = other.triggerList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              triggerListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTriggerListFieldBuilder() : null;
            } else {
              triggerListBuilder_.addAllMessages(other.triggerList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (triggerListBuilder_ == null) {
                  ensureTriggerListIsMutable();
                  triggerList_.add(m);
                } else {
                  triggerListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> triggerList_ =
        java.util.Collections.emptyList();
      private void ensureTriggerListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          triggerList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(triggerList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> triggerListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getTriggerListList() {
        if (triggerListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(triggerList_);
        } else {
          return triggerListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public int getTriggerListCount() {
        if (triggerListBuilder_ == null) {
          return triggerList_.size();
        } else {
          return triggerListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getTriggerList(int index) {
        if (triggerListBuilder_ == null) {
          return triggerList_.get(index);
        } else {
          return triggerListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public Builder setTriggerList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (triggerListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTriggerListIsMutable();
          triggerList_.set(index, value);
          onChanged();
        } else {
          triggerListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public Builder setTriggerList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (triggerListBuilder_ == null) {
          ensureTriggerListIsMutable();
          triggerList_.set(index, builderForValue.build());
          onChanged();
        } else {
          triggerListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public Builder addTriggerList(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (triggerListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTriggerListIsMutable();
          triggerList_.add(value);
          onChanged();
        } else {
          triggerListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public Builder addTriggerList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (triggerListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTriggerListIsMutable();
          triggerList_.add(index, value);
          onChanged();
        } else {
          triggerListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public Builder addTriggerList(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (triggerListBuilder_ == null) {
          ensureTriggerListIsMutable();
          triggerList_.add(builderForValue.build());
          onChanged();
        } else {
          triggerListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public Builder addTriggerList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (triggerListBuilder_ == null) {
          ensureTriggerListIsMutable();
          triggerList_.add(index, builderForValue.build());
          onChanged();
        } else {
          triggerListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public Builder addAllTriggerList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (triggerListBuilder_ == null) {
          ensureTriggerListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, triggerList_);
          onChanged();
        } else {
          triggerListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public Builder clearTriggerList() {
        if (triggerListBuilder_ == null) {
          triggerList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          triggerListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public Builder removeTriggerList(int index) {
        if (triggerListBuilder_ == null) {
          ensureTriggerListIsMutable();
          triggerList_.remove(index);
          onChanged();
        } else {
          triggerListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getTriggerListBuilder(
          int index) {
        return getTriggerListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getTriggerListOrBuilder(
          int index) {
        if (triggerListBuilder_ == null) {
          return triggerList_.get(index);  } else {
          return triggerListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getTriggerListOrBuilderList() {
        if (triggerListBuilder_ != null) {
          return triggerListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(triggerList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addTriggerListBuilder() {
        return getTriggerListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addTriggerListBuilder(
          int index) {
        return getTriggerListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value trigger_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getTriggerListBuilderList() {
        return getTriggerListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getTriggerListFieldBuilder() {
        if (triggerListBuilder_ == null) {
          triggerListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  triggerList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          triggerList_ = null;
        }
        return triggerListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_forum_trigger_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_forum_trigger_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_forum_trigger_c2s>
        PARSER = new com.google.protobuf.AbstractParser<operate_forum_trigger_c2s>() {
      @java.lang.Override
      public operate_forum_trigger_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_forum_trigger_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_forum_trigger_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_trigger_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_forum_get_reward_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_forum_get_reward_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <code>uint32 config_id = 2;</code>
     * @return The configId.
     */
    int getConfigId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_get_reward_c2s}
   */
  public static final class operate_forum_get_reward_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_forum_get_reward_c2s)
      operate_forum_get_reward_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_forum_get_reward_c2s.newBuilder() to construct.
    private operate_forum_get_reward_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_forum_get_reward_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_forum_get_reward_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_get_reward_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_get_reward_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int CONFIG_ID_FIELD_NUMBER = 2;
    private int configId_ = 0;
    /**
     * <code>uint32 config_id = 2;</code>
     * @return The configId.
     */
    @java.lang.Override
    public int getConfigId() {
      return configId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeUInt32(1, type_);
      }
      if (configId_ != 0) {
        output.writeUInt32(2, configId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, type_);
      }
      if (configId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, configId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s) obj;

      if (getType()
          != other.getType()) return false;
      if (getConfigId()
          != other.getConfigId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + CONFIG_ID_FIELD_NUMBER;
      hash = (53 * hash) + getConfigId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_get_reward_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_forum_get_reward_c2s)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_get_reward_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_get_reward_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        configId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_get_reward_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.configId_ = configId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getConfigId() != 0) {
          setConfigId(other.getConfigId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                configId_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>uint32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int configId_ ;
      /**
       * <code>uint32 config_id = 2;</code>
       * @return The configId.
       */
      @java.lang.Override
      public int getConfigId() {
        return configId_;
      }
      /**
       * <code>uint32 config_id = 2;</code>
       * @param value The configId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigId(int value) {

        configId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 config_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        configId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_forum_get_reward_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_forum_get_reward_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_forum_get_reward_c2s>
        PARSER = new com.google.protobuf.AbstractParser<operate_forum_get_reward_c2s>() {
      @java.lang.Override
      public operate_forum_get_reward_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_forum_get_reward_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_forum_get_reward_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_get_reward_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_forum_update_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_forum_update_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_reward> 
        getRewardUpdateListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_forum_reward getRewardUpdateList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
     */
    int getRewardUpdateListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder> 
        getRewardUpdateListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder getRewardUpdateListOrBuilder(
        int index);

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getTaskUpdateListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getTaskUpdateList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
     */
    int getTaskUpdateListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getTaskUpdateListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getTaskUpdateListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_update_s2c}
   */
  public static final class operate_forum_update_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_forum_update_s2c)
      operate_forum_update_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_forum_update_s2c.newBuilder() to construct.
    private operate_forum_update_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_forum_update_s2c() {
      rewardUpdateList_ = java.util.Collections.emptyList();
      taskUpdateList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_forum_update_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_update_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_update_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c.Builder.class);
    }

    public static final int REWARD_UPDATE_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_reward> rewardUpdateList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_reward> getRewardUpdateListList() {
      return rewardUpdateList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder> 
        getRewardUpdateListOrBuilderList() {
      return rewardUpdateList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
     */
    @java.lang.Override
    public int getRewardUpdateListCount() {
      return rewardUpdateList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_forum_reward getRewardUpdateList(int index) {
      return rewardUpdateList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder getRewardUpdateListOrBuilder(
        int index) {
      return rewardUpdateList_.get(index);
    }

    public static final int TASK_UPDATE_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> taskUpdateList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getTaskUpdateListList() {
      return taskUpdateList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getTaskUpdateListOrBuilderList() {
      return taskUpdateList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
     */
    @java.lang.Override
    public int getTaskUpdateListCount() {
      return taskUpdateList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getTaskUpdateList(int index) {
      return taskUpdateList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getTaskUpdateListOrBuilder(
        int index) {
      return taskUpdateList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < rewardUpdateList_.size(); i++) {
        output.writeMessage(1, rewardUpdateList_.get(i));
      }
      for (int i = 0; i < taskUpdateList_.size(); i++) {
        output.writeMessage(2, taskUpdateList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < rewardUpdateList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, rewardUpdateList_.get(i));
      }
      for (int i = 0; i < taskUpdateList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, taskUpdateList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c) obj;

      if (!getRewardUpdateListList()
          .equals(other.getRewardUpdateListList())) return false;
      if (!getTaskUpdateListList()
          .equals(other.getTaskUpdateListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRewardUpdateListCount() > 0) {
        hash = (37 * hash) + REWARD_UPDATE_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getRewardUpdateListList().hashCode();
      }
      if (getTaskUpdateListCount() > 0) {
        hash = (37 * hash) + TASK_UPDATE_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getTaskUpdateListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_update_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_forum_update_s2c)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_update_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_update_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (rewardUpdateListBuilder_ == null) {
          rewardUpdateList_ = java.util.Collections.emptyList();
        } else {
          rewardUpdateList_ = null;
          rewardUpdateListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        if (taskUpdateListBuilder_ == null) {
          taskUpdateList_ = java.util.Collections.emptyList();
        } else {
          taskUpdateList_ = null;
          taskUpdateListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_update_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c result) {
        if (rewardUpdateListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rewardUpdateList_ = java.util.Collections.unmodifiableList(rewardUpdateList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rewardUpdateList_ = rewardUpdateList_;
        } else {
          result.rewardUpdateList_ = rewardUpdateListBuilder_.build();
        }
        if (taskUpdateListBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            taskUpdateList_ = java.util.Collections.unmodifiableList(taskUpdateList_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.taskUpdateList_ = taskUpdateList_;
        } else {
          result.taskUpdateList_ = taskUpdateListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c.getDefaultInstance()) return this;
        if (rewardUpdateListBuilder_ == null) {
          if (!other.rewardUpdateList_.isEmpty()) {
            if (rewardUpdateList_.isEmpty()) {
              rewardUpdateList_ = other.rewardUpdateList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRewardUpdateListIsMutable();
              rewardUpdateList_.addAll(other.rewardUpdateList_);
            }
            onChanged();
          }
        } else {
          if (!other.rewardUpdateList_.isEmpty()) {
            if (rewardUpdateListBuilder_.isEmpty()) {
              rewardUpdateListBuilder_.dispose();
              rewardUpdateListBuilder_ = null;
              rewardUpdateList_ = other.rewardUpdateList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rewardUpdateListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRewardUpdateListFieldBuilder() : null;
            } else {
              rewardUpdateListBuilder_.addAllMessages(other.rewardUpdateList_);
            }
          }
        }
        if (taskUpdateListBuilder_ == null) {
          if (!other.taskUpdateList_.isEmpty()) {
            if (taskUpdateList_.isEmpty()) {
              taskUpdateList_ = other.taskUpdateList_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureTaskUpdateListIsMutable();
              taskUpdateList_.addAll(other.taskUpdateList_);
            }
            onChanged();
          }
        } else {
          if (!other.taskUpdateList_.isEmpty()) {
            if (taskUpdateListBuilder_.isEmpty()) {
              taskUpdateListBuilder_.dispose();
              taskUpdateListBuilder_ = null;
              taskUpdateList_ = other.taskUpdateList_;
              bitField0_ = (bitField0_ & ~0x00000002);
              taskUpdateListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTaskUpdateListFieldBuilder() : null;
            } else {
              taskUpdateListBuilder_.addAllMessages(other.taskUpdateList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_forum_reward m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_forum_reward.parser(),
                        extensionRegistry);
                if (rewardUpdateListBuilder_ == null) {
                  ensureRewardUpdateListIsMutable();
                  rewardUpdateList_.add(m);
                } else {
                  rewardUpdateListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (taskUpdateListBuilder_ == null) {
                  ensureTaskUpdateListIsMutable();
                  taskUpdateList_.add(m);
                } else {
                  taskUpdateListBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_reward> rewardUpdateList_ =
        java.util.Collections.emptyList();
      private void ensureRewardUpdateListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rewardUpdateList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_forum_reward>(rewardUpdateList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_forum_reward, org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder> rewardUpdateListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_reward> getRewardUpdateListList() {
        if (rewardUpdateListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewardUpdateList_);
        } else {
          return rewardUpdateListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public int getRewardUpdateListCount() {
        if (rewardUpdateListBuilder_ == null) {
          return rewardUpdateList_.size();
        } else {
          return rewardUpdateListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_reward getRewardUpdateList(int index) {
        if (rewardUpdateListBuilder_ == null) {
          return rewardUpdateList_.get(index);
        } else {
          return rewardUpdateListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public Builder setRewardUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_forum_reward value) {
        if (rewardUpdateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardUpdateListIsMutable();
          rewardUpdateList_.set(index, value);
          onChanged();
        } else {
          rewardUpdateListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public Builder setRewardUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder builderForValue) {
        if (rewardUpdateListBuilder_ == null) {
          ensureRewardUpdateListIsMutable();
          rewardUpdateList_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardUpdateListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public Builder addRewardUpdateList(org.gof.demo.worldsrv.msg.Define.p_forum_reward value) {
        if (rewardUpdateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardUpdateListIsMutable();
          rewardUpdateList_.add(value);
          onChanged();
        } else {
          rewardUpdateListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public Builder addRewardUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_forum_reward value) {
        if (rewardUpdateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardUpdateListIsMutable();
          rewardUpdateList_.add(index, value);
          onChanged();
        } else {
          rewardUpdateListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public Builder addRewardUpdateList(
          org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder builderForValue) {
        if (rewardUpdateListBuilder_ == null) {
          ensureRewardUpdateListIsMutable();
          rewardUpdateList_.add(builderForValue.build());
          onChanged();
        } else {
          rewardUpdateListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public Builder addRewardUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder builderForValue) {
        if (rewardUpdateListBuilder_ == null) {
          ensureRewardUpdateListIsMutable();
          rewardUpdateList_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardUpdateListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public Builder addAllRewardUpdateList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_forum_reward> values) {
        if (rewardUpdateListBuilder_ == null) {
          ensureRewardUpdateListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewardUpdateList_);
          onChanged();
        } else {
          rewardUpdateListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public Builder clearRewardUpdateList() {
        if (rewardUpdateListBuilder_ == null) {
          rewardUpdateList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rewardUpdateListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public Builder removeRewardUpdateList(int index) {
        if (rewardUpdateListBuilder_ == null) {
          ensureRewardUpdateListIsMutable();
          rewardUpdateList_.remove(index);
          onChanged();
        } else {
          rewardUpdateListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder getRewardUpdateListBuilder(
          int index) {
        return getRewardUpdateListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder getRewardUpdateListOrBuilder(
          int index) {
        if (rewardUpdateListBuilder_ == null) {
          return rewardUpdateList_.get(index);  } else {
          return rewardUpdateListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder> 
           getRewardUpdateListOrBuilderList() {
        if (rewardUpdateListBuilder_ != null) {
          return rewardUpdateListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewardUpdateList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder addRewardUpdateListBuilder() {
        return getRewardUpdateListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_forum_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder addRewardUpdateListBuilder(
          int index) {
        return getRewardUpdateListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_forum_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_reward reward_update_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder> 
           getRewardUpdateListBuilderList() {
        return getRewardUpdateListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_forum_reward, org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder> 
          getRewardUpdateListFieldBuilder() {
        if (rewardUpdateListBuilder_ == null) {
          rewardUpdateListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_forum_reward, org.gof.demo.worldsrv.msg.Define.p_forum_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_forum_rewardOrBuilder>(
                  rewardUpdateList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rewardUpdateList_ = null;
        }
        return rewardUpdateListBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> taskUpdateList_ =
        java.util.Collections.emptyList();
      private void ensureTaskUpdateListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          taskUpdateList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(taskUpdateList_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> taskUpdateListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getTaskUpdateListList() {
        if (taskUpdateListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(taskUpdateList_);
        } else {
          return taskUpdateListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public int getTaskUpdateListCount() {
        if (taskUpdateListBuilder_ == null) {
          return taskUpdateList_.size();
        } else {
          return taskUpdateListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getTaskUpdateList(int index) {
        if (taskUpdateListBuilder_ == null) {
          return taskUpdateList_.get(index);
        } else {
          return taskUpdateListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public Builder setTaskUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (taskUpdateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskUpdateListIsMutable();
          taskUpdateList_.set(index, value);
          onChanged();
        } else {
          taskUpdateListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public Builder setTaskUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (taskUpdateListBuilder_ == null) {
          ensureTaskUpdateListIsMutable();
          taskUpdateList_.set(index, builderForValue.build());
          onChanged();
        } else {
          taskUpdateListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public Builder addTaskUpdateList(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (taskUpdateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskUpdateListIsMutable();
          taskUpdateList_.add(value);
          onChanged();
        } else {
          taskUpdateListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public Builder addTaskUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (taskUpdateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTaskUpdateListIsMutable();
          taskUpdateList_.add(index, value);
          onChanged();
        } else {
          taskUpdateListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public Builder addTaskUpdateList(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (taskUpdateListBuilder_ == null) {
          ensureTaskUpdateListIsMutable();
          taskUpdateList_.add(builderForValue.build());
          onChanged();
        } else {
          taskUpdateListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public Builder addTaskUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (taskUpdateListBuilder_ == null) {
          ensureTaskUpdateListIsMutable();
          taskUpdateList_.add(index, builderForValue.build());
          onChanged();
        } else {
          taskUpdateListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public Builder addAllTaskUpdateList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (taskUpdateListBuilder_ == null) {
          ensureTaskUpdateListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, taskUpdateList_);
          onChanged();
        } else {
          taskUpdateListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public Builder clearTaskUpdateList() {
        if (taskUpdateListBuilder_ == null) {
          taskUpdateList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          taskUpdateListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public Builder removeTaskUpdateList(int index) {
        if (taskUpdateListBuilder_ == null) {
          ensureTaskUpdateListIsMutable();
          taskUpdateList_.remove(index);
          onChanged();
        } else {
          taskUpdateListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getTaskUpdateListBuilder(
          int index) {
        return getTaskUpdateListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getTaskUpdateListOrBuilder(
          int index) {
        if (taskUpdateListBuilder_ == null) {
          return taskUpdateList_.get(index);  } else {
          return taskUpdateListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getTaskUpdateListOrBuilderList() {
        if (taskUpdateListBuilder_ != null) {
          return taskUpdateListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(taskUpdateList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addTaskUpdateListBuilder() {
        return getTaskUpdateListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addTaskUpdateListBuilder(
          int index) {
        return getTaskUpdateListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value task_update_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getTaskUpdateListBuilderList() {
        return getTaskUpdateListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getTaskUpdateListFieldBuilder() {
        if (taskUpdateListBuilder_ == null) {
          taskUpdateListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  taskUpdateList_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          taskUpdateList_ = null;
        }
        return taskUpdateListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_forum_update_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_forum_update_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_forum_update_s2c>
        PARSER = new com.google.protobuf.AbstractParser<operate_forum_update_s2c>() {
      @java.lang.Override
      public operate_forum_update_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_forum_update_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_forum_update_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_update_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_forum_red_point_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_forum_red_point_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_red_point_c2s}
   */
  public static final class operate_forum_red_point_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_forum_red_point_c2s)
      operate_forum_red_point_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_forum_red_point_c2s.newBuilder() to construct.
    private operate_forum_red_point_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_forum_red_point_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_forum_red_point_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_red_point_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_forum_red_point_c2s)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_forum_red_point_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_forum_red_point_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_forum_red_point_c2s>
        PARSER = new com.google.protobuf.AbstractParser<operate_forum_red_point_c2s>() {
      @java.lang.Override
      public operate_forum_red_point_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_forum_red_point_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_forum_red_point_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_forum_red_point_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_forum_red_point_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getCountListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getCountList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
     */
    int getCountListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getCountListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getCountListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_red_point_s2c}
   */
  public static final class operate_forum_red_point_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_forum_red_point_s2c)
      operate_forum_red_point_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_forum_red_point_s2c.newBuilder() to construct.
    private operate_forum_red_point_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_forum_red_point_s2c() {
      countList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_forum_red_point_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c.Builder.class);
    }

    public static final int COUNT_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> countList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getCountListList() {
      return countList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getCountListOrBuilderList() {
      return countList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
     */
    @java.lang.Override
    public int getCountListCount() {
      return countList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getCountList(int index) {
      return countList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getCountListOrBuilder(
        int index) {
      return countList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < countList_.size(); i++) {
        output.writeMessage(1, countList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < countList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, countList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c) obj;

      if (!getCountListList()
          .equals(other.getCountListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getCountListCount() > 0) {
        hash = (37 * hash) + COUNT_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getCountListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_forum_red_point_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_forum_red_point_s2c)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (countListBuilder_ == null) {
          countList_ = java.util.Collections.emptyList();
        } else {
          countList_ = null;
          countListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c result) {
        if (countListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            countList_ = java.util.Collections.unmodifiableList(countList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.countList_ = countList_;
        } else {
          result.countList_ = countListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c.getDefaultInstance()) return this;
        if (countListBuilder_ == null) {
          if (!other.countList_.isEmpty()) {
            if (countList_.isEmpty()) {
              countList_ = other.countList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureCountListIsMutable();
              countList_.addAll(other.countList_);
            }
            onChanged();
          }
        } else {
          if (!other.countList_.isEmpty()) {
            if (countListBuilder_.isEmpty()) {
              countListBuilder_.dispose();
              countListBuilder_ = null;
              countList_ = other.countList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              countListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getCountListFieldBuilder() : null;
            } else {
              countListBuilder_.addAllMessages(other.countList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (countListBuilder_ == null) {
                  ensureCountListIsMutable();
                  countList_.add(m);
                } else {
                  countListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> countList_ =
        java.util.Collections.emptyList();
      private void ensureCountListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          countList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(countList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> countListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getCountListList() {
        if (countListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(countList_);
        } else {
          return countListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public int getCountListCount() {
        if (countListBuilder_ == null) {
          return countList_.size();
        } else {
          return countListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getCountList(int index) {
        if (countListBuilder_ == null) {
          return countList_.get(index);
        } else {
          return countListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public Builder setCountList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (countListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCountListIsMutable();
          countList_.set(index, value);
          onChanged();
        } else {
          countListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public Builder setCountList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (countListBuilder_ == null) {
          ensureCountListIsMutable();
          countList_.set(index, builderForValue.build());
          onChanged();
        } else {
          countListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public Builder addCountList(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (countListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCountListIsMutable();
          countList_.add(value);
          onChanged();
        } else {
          countListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public Builder addCountList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (countListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCountListIsMutable();
          countList_.add(index, value);
          onChanged();
        } else {
          countListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public Builder addCountList(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (countListBuilder_ == null) {
          ensureCountListIsMutable();
          countList_.add(builderForValue.build());
          onChanged();
        } else {
          countListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public Builder addCountList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (countListBuilder_ == null) {
          ensureCountListIsMutable();
          countList_.add(index, builderForValue.build());
          onChanged();
        } else {
          countListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public Builder addAllCountList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (countListBuilder_ == null) {
          ensureCountListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, countList_);
          onChanged();
        } else {
          countListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public Builder clearCountList() {
        if (countListBuilder_ == null) {
          countList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          countListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public Builder removeCountList(int index) {
        if (countListBuilder_ == null) {
          ensureCountListIsMutable();
          countList_.remove(index);
          onChanged();
        } else {
          countListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getCountListBuilder(
          int index) {
        return getCountListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getCountListOrBuilder(
          int index) {
        if (countListBuilder_ == null) {
          return countList_.get(index);  } else {
          return countListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getCountListOrBuilderList() {
        if (countListBuilder_ != null) {
          return countListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(countList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addCountListBuilder() {
        return getCountListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addCountListBuilder(
          int index) {
        return getCountListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value count_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getCountListBuilderList() {
        return getCountListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getCountListFieldBuilder() {
        if (countListBuilder_ == null) {
          countListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  countList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          countList_ = null;
        }
        return countListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_forum_red_point_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_forum_red_point_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_forum_red_point_s2c>
        PARSER = new com.google.protobuf.AbstractParser<operate_forum_red_point_s2c>() {
      @java.lang.Override
      public operate_forum_red_point_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_forum_red_point_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_forum_red_point_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_forum_red_point_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface forum_news_config_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.forum_news_config_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.forum_news_config_c2s}
   */
  public static final class forum_news_config_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.forum_news_config_c2s)
      forum_news_config_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use forum_news_config_c2s.newBuilder() to construct.
    private forum_news_config_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private forum_news_config_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new forum_news_config_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_forum_news_config_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_forum_news_config_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s other = (org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.forum_news_config_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.forum_news_config_c2s)
        org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_forum_news_config_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_forum_news_config_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_forum_news_config_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s build() {
        org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s result = new org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.forum_news_config_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.forum_news_config_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<forum_news_config_c2s>
        PARSER = new com.google.protobuf.AbstractParser<forum_news_config_c2s>() {
      @java.lang.Override
      public forum_news_config_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<forum_news_config_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<forum_news_config_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface forum_news_config_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.forum_news_config_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string default_id = 1;</code>
     * @return The defaultId.
     */
    java.lang.String getDefaultId();
    /**
     * <code>string default_id = 1;</code>
     * @return The bytes for defaultId.
     */
    com.google.protobuf.ByteString
        getDefaultIdBytes();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_news> 
        getListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_forum_news getList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
     */
    int getListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_forum_newsOrBuilder> 
        getListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_forum_newsOrBuilder getListOrBuilder(
        int index);

    /**
     * <code>string more = 3;</code>
     * @return The more.
     */
    java.lang.String getMore();
    /**
     * <code>string more = 3;</code>
     * @return The bytes for more.
     */
    com.google.protobuf.ByteString
        getMoreBytes();

    /**
     * <code>string goto = 4;</code>
     * @return The goto.
     */
    java.lang.String getGoto();
    /**
     * <code>string goto = 4;</code>
     * @return The bytes for goto.
     */
    com.google.protobuf.ByteString
        getGotoBytes();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.forum_news_config_s2c}
   */
  public static final class forum_news_config_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.forum_news_config_s2c)
      forum_news_config_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use forum_news_config_s2c.newBuilder() to construct.
    private forum_news_config_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private forum_news_config_s2c() {
      defaultId_ = "";
      list_ = java.util.Collections.emptyList();
      more_ = "";
      goto_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new forum_news_config_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_forum_news_config_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_forum_news_config_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c.Builder.class);
    }

    public static final int DEFAULT_ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object defaultId_ = "";
    /**
     * <code>string default_id = 1;</code>
     * @return The defaultId.
     */
    @java.lang.Override
    public java.lang.String getDefaultId() {
      java.lang.Object ref = defaultId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        defaultId_ = s;
        return s;
      }
    }
    /**
     * <code>string default_id = 1;</code>
     * @return The bytes for defaultId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDefaultIdBytes() {
      java.lang.Object ref = defaultId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        defaultId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_news> list_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_news> getListList() {
      return list_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_forum_newsOrBuilder> 
        getListOrBuilderList() {
      return list_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
     */
    @java.lang.Override
    public int getListCount() {
      return list_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_forum_news getList(int index) {
      return list_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_forum_newsOrBuilder getListOrBuilder(
        int index) {
      return list_.get(index);
    }

    public static final int MORE_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private volatile java.lang.Object more_ = "";
    /**
     * <code>string more = 3;</code>
     * @return The more.
     */
    @java.lang.Override
    public java.lang.String getMore() {
      java.lang.Object ref = more_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        more_ = s;
        return s;
      }
    }
    /**
     * <code>string more = 3;</code>
     * @return The bytes for more.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getMoreBytes() {
      java.lang.Object ref = more_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        more_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int GOTO_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object goto_ = "";
    /**
     * <code>string goto = 4;</code>
     * @return The goto.
     */
    @java.lang.Override
    public java.lang.String getGoto() {
      java.lang.Object ref = goto_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        goto_ = s;
        return s;
      }
    }
    /**
     * <code>string goto = 4;</code>
     * @return The bytes for goto.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getGotoBytes() {
      java.lang.Object ref = goto_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        goto_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(defaultId_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, defaultId_);
      }
      for (int i = 0; i < list_.size(); i++) {
        output.writeMessage(2, list_.get(i));
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(more_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, more_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(goto_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, goto_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(defaultId_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, defaultId_);
      }
      for (int i = 0; i < list_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, list_.get(i));
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(more_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, more_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(goto_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, goto_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c other = (org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c) obj;

      if (!getDefaultId()
          .equals(other.getDefaultId())) return false;
      if (!getListList()
          .equals(other.getListList())) return false;
      if (!getMore()
          .equals(other.getMore())) return false;
      if (!getGoto()
          .equals(other.getGoto())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + DEFAULT_ID_FIELD_NUMBER;
      hash = (53 * hash) + getDefaultId().hashCode();
      if (getListCount() > 0) {
        hash = (37 * hash) + LIST_FIELD_NUMBER;
        hash = (53 * hash) + getListList().hashCode();
      }
      hash = (37 * hash) + MORE_FIELD_NUMBER;
      hash = (53 * hash) + getMore().hashCode();
      hash = (37 * hash) + GOTO_FIELD_NUMBER;
      hash = (53 * hash) + getGoto().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.forum_news_config_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.forum_news_config_s2c)
        org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_forum_news_config_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_forum_news_config_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        defaultId_ = "";
        if (listBuilder_ == null) {
          list_ = java.util.Collections.emptyList();
        } else {
          list_ = null;
          listBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        more_ = "";
        goto_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_forum_news_config_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c build() {
        org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c result = new org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c result) {
        if (listBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            list_ = java.util.Collections.unmodifiableList(list_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.list_ = list_;
        } else {
          result.list_ = listBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.defaultId_ = defaultId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.more_ = more_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.goto_ = goto_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c.getDefaultInstance()) return this;
        if (!other.getDefaultId().isEmpty()) {
          defaultId_ = other.defaultId_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        if (listBuilder_ == null) {
          if (!other.list_.isEmpty()) {
            if (list_.isEmpty()) {
              list_ = other.list_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureListIsMutable();
              list_.addAll(other.list_);
            }
            onChanged();
          }
        } else {
          if (!other.list_.isEmpty()) {
            if (listBuilder_.isEmpty()) {
              listBuilder_.dispose();
              listBuilder_ = null;
              list_ = other.list_;
              bitField0_ = (bitField0_ & ~0x00000002);
              listBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getListFieldBuilder() : null;
            } else {
              listBuilder_.addAllMessages(other.list_);
            }
          }
        }
        if (!other.getMore().isEmpty()) {
          more_ = other.more_;
          bitField0_ |= 0x00000004;
          onChanged();
        }
        if (!other.getGoto().isEmpty()) {
          goto_ = other.goto_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                defaultId_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_forum_news m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_forum_news.parser(),
                        extensionRegistry);
                if (listBuilder_ == null) {
                  ensureListIsMutable();
                  list_.add(m);
                } else {
                  listBuilder_.addMessage(m);
                }
                break;
              } // case 18
              case 26: {
                more_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              case 34: {
                goto_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object defaultId_ = "";
      /**
       * <code>string default_id = 1;</code>
       * @return The defaultId.
       */
      public java.lang.String getDefaultId() {
        java.lang.Object ref = defaultId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          defaultId_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string default_id = 1;</code>
       * @return The bytes for defaultId.
       */
      public com.google.protobuf.ByteString
          getDefaultIdBytes() {
        java.lang.Object ref = defaultId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          defaultId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string default_id = 1;</code>
       * @param value The defaultId to set.
       * @return This builder for chaining.
       */
      public Builder setDefaultId(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        defaultId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string default_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDefaultId() {
        defaultId_ = getDefaultInstance().getDefaultId();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string default_id = 1;</code>
       * @param value The bytes for defaultId to set.
       * @return This builder for chaining.
       */
      public Builder setDefaultIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        defaultId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_news> list_ =
        java.util.Collections.emptyList();
      private void ensureListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          list_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_forum_news>(list_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_forum_news, org.gof.demo.worldsrv.msg.Define.p_forum_news.Builder, org.gof.demo.worldsrv.msg.Define.p_forum_newsOrBuilder> listBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_news> getListList() {
        if (listBuilder_ == null) {
          return java.util.Collections.unmodifiableList(list_);
        } else {
          return listBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public int getListCount() {
        if (listBuilder_ == null) {
          return list_.size();
        } else {
          return listBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_news getList(int index) {
        if (listBuilder_ == null) {
          return list_.get(index);
        } else {
          return listBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public Builder setList(
          int index, org.gof.demo.worldsrv.msg.Define.p_forum_news value) {
        if (listBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureListIsMutable();
          list_.set(index, value);
          onChanged();
        } else {
          listBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public Builder setList(
          int index, org.gof.demo.worldsrv.msg.Define.p_forum_news.Builder builderForValue) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.set(index, builderForValue.build());
          onChanged();
        } else {
          listBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public Builder addList(org.gof.demo.worldsrv.msg.Define.p_forum_news value) {
        if (listBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureListIsMutable();
          list_.add(value);
          onChanged();
        } else {
          listBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public Builder addList(
          int index, org.gof.demo.worldsrv.msg.Define.p_forum_news value) {
        if (listBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureListIsMutable();
          list_.add(index, value);
          onChanged();
        } else {
          listBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public Builder addList(
          org.gof.demo.worldsrv.msg.Define.p_forum_news.Builder builderForValue) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.add(builderForValue.build());
          onChanged();
        } else {
          listBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public Builder addList(
          int index, org.gof.demo.worldsrv.msg.Define.p_forum_news.Builder builderForValue) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.add(index, builderForValue.build());
          onChanged();
        } else {
          listBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public Builder addAllList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_forum_news> values) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, list_);
          onChanged();
        } else {
          listBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public Builder clearList() {
        if (listBuilder_ == null) {
          list_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          listBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public Builder removeList(int index) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.remove(index);
          onChanged();
        } else {
          listBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_news.Builder getListBuilder(
          int index) {
        return getListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_newsOrBuilder getListOrBuilder(
          int index) {
        if (listBuilder_ == null) {
          return list_.get(index);  } else {
          return listBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_forum_newsOrBuilder> 
           getListOrBuilderList() {
        if (listBuilder_ != null) {
          return listBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(list_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_news.Builder addListBuilder() {
        return getListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_forum_news.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_forum_news.Builder addListBuilder(
          int index) {
        return getListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_forum_news.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_forum_news list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_forum_news.Builder> 
           getListBuilderList() {
        return getListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_forum_news, org.gof.demo.worldsrv.msg.Define.p_forum_news.Builder, org.gof.demo.worldsrv.msg.Define.p_forum_newsOrBuilder> 
          getListFieldBuilder() {
        if (listBuilder_ == null) {
          listBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_forum_news, org.gof.demo.worldsrv.msg.Define.p_forum_news.Builder, org.gof.demo.worldsrv.msg.Define.p_forum_newsOrBuilder>(
                  list_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          list_ = null;
        }
        return listBuilder_;
      }

      private java.lang.Object more_ = "";
      /**
       * <code>string more = 3;</code>
       * @return The more.
       */
      public java.lang.String getMore() {
        java.lang.Object ref = more_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          more_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string more = 3;</code>
       * @return The bytes for more.
       */
      public com.google.protobuf.ByteString
          getMoreBytes() {
        java.lang.Object ref = more_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          more_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string more = 3;</code>
       * @param value The more to set.
       * @return This builder for chaining.
       */
      public Builder setMore(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        more_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>string more = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMore() {
        more_ = getDefaultInstance().getMore();
        bitField0_ = (bitField0_ & ~0x00000004);
        onChanged();
        return this;
      }
      /**
       * <code>string more = 3;</code>
       * @param value The bytes for more to set.
       * @return This builder for chaining.
       */
      public Builder setMoreBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        more_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }

      private java.lang.Object goto_ = "";
      /**
       * <code>string goto = 4;</code>
       * @return The goto.
       */
      public java.lang.String getGoto() {
        java.lang.Object ref = goto_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          goto_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string goto = 4;</code>
       * @return The bytes for goto.
       */
      public com.google.protobuf.ByteString
          getGotoBytes() {
        java.lang.Object ref = goto_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          goto_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string goto = 4;</code>
       * @param value The goto to set.
       * @return This builder for chaining.
       */
      public Builder setGoto(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        goto_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>string goto = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearGoto() {
        goto_ = getDefaultInstance().getGoto();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <code>string goto = 4;</code>
       * @param value The bytes for goto to set.
       * @return This builder for chaining.
       */
      public Builder setGotoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        goto_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.forum_news_config_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.forum_news_config_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<forum_news_config_s2c>
        PARSER = new com.google.protobuf.AbstractParser<forum_news_config_s2c>() {
      @java.lang.Override
      public forum_news_config_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<forum_news_config_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<forum_news_config_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.forum_news_config_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_reward_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_reward_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_reward_info_c2s}
   */
  public static final class operate_reward_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_reward_info_c2s)
      operate_reward_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_reward_info_c2s.newBuilder() to construct.
    private operate_reward_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_reward_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_reward_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_reward_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_reward_info_c2s)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_reward_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_reward_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_reward_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<operate_reward_info_c2s>() {
      @java.lang.Override
      public operate_reward_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_reward_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_reward_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_reward_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_reward_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getRewardInfoList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getRewardInfo(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
     */
    int getRewardInfoCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getRewardInfoOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getRewardInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_reward_info_s2c}
   */
  public static final class operate_reward_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_reward_info_s2c)
      operate_reward_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_reward_info_s2c.newBuilder() to construct.
    private operate_reward_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_reward_info_s2c() {
      rewardInfo_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_reward_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c.Builder.class);
    }

    public static final int REWARD_INFO_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> rewardInfo_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getRewardInfoList() {
      return rewardInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getRewardInfoOrBuilderList() {
      return rewardInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
     */
    @java.lang.Override
    public int getRewardInfoCount() {
      return rewardInfo_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getRewardInfo(int index) {
      return rewardInfo_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getRewardInfoOrBuilder(
        int index) {
      return rewardInfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < rewardInfo_.size(); i++) {
        output.writeMessage(1, rewardInfo_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < rewardInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, rewardInfo_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c) obj;

      if (!getRewardInfoList()
          .equals(other.getRewardInfoList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRewardInfoCount() > 0) {
        hash = (37 * hash) + REWARD_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getRewardInfoList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_reward_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_reward_info_s2c)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (rewardInfoBuilder_ == null) {
          rewardInfo_ = java.util.Collections.emptyList();
        } else {
          rewardInfo_ = null;
          rewardInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c result) {
        if (rewardInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rewardInfo_ = java.util.Collections.unmodifiableList(rewardInfo_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rewardInfo_ = rewardInfo_;
        } else {
          result.rewardInfo_ = rewardInfoBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c.getDefaultInstance()) return this;
        if (rewardInfoBuilder_ == null) {
          if (!other.rewardInfo_.isEmpty()) {
            if (rewardInfo_.isEmpty()) {
              rewardInfo_ = other.rewardInfo_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRewardInfoIsMutable();
              rewardInfo_.addAll(other.rewardInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.rewardInfo_.isEmpty()) {
            if (rewardInfoBuilder_.isEmpty()) {
              rewardInfoBuilder_.dispose();
              rewardInfoBuilder_ = null;
              rewardInfo_ = other.rewardInfo_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rewardInfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRewardInfoFieldBuilder() : null;
            } else {
              rewardInfoBuilder_.addAllMessages(other.rewardInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (rewardInfoBuilder_ == null) {
                  ensureRewardInfoIsMutable();
                  rewardInfo_.add(m);
                } else {
                  rewardInfoBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> rewardInfo_ =
        java.util.Collections.emptyList();
      private void ensureRewardInfoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rewardInfo_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(rewardInfo_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> rewardInfoBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getRewardInfoList() {
        if (rewardInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewardInfo_);
        } else {
          return rewardInfoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public int getRewardInfoCount() {
        if (rewardInfoBuilder_ == null) {
          return rewardInfo_.size();
        } else {
          return rewardInfoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getRewardInfo(int index) {
        if (rewardInfoBuilder_ == null) {
          return rewardInfo_.get(index);
        } else {
          return rewardInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public Builder setRewardInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (rewardInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardInfoIsMutable();
          rewardInfo_.set(index, value);
          onChanged();
        } else {
          rewardInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public Builder setRewardInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (rewardInfoBuilder_ == null) {
          ensureRewardInfoIsMutable();
          rewardInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public Builder addRewardInfo(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (rewardInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardInfoIsMutable();
          rewardInfo_.add(value);
          onChanged();
        } else {
          rewardInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public Builder addRewardInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (rewardInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardInfoIsMutable();
          rewardInfo_.add(index, value);
          onChanged();
        } else {
          rewardInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public Builder addRewardInfo(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (rewardInfoBuilder_ == null) {
          ensureRewardInfoIsMutable();
          rewardInfo_.add(builderForValue.build());
          onChanged();
        } else {
          rewardInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public Builder addRewardInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (rewardInfoBuilder_ == null) {
          ensureRewardInfoIsMutable();
          rewardInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public Builder addAllRewardInfo(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (rewardInfoBuilder_ == null) {
          ensureRewardInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewardInfo_);
          onChanged();
        } else {
          rewardInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public Builder clearRewardInfo() {
        if (rewardInfoBuilder_ == null) {
          rewardInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rewardInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public Builder removeRewardInfo(int index) {
        if (rewardInfoBuilder_ == null) {
          ensureRewardInfoIsMutable();
          rewardInfo_.remove(index);
          onChanged();
        } else {
          rewardInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getRewardInfoBuilder(
          int index) {
        return getRewardInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getRewardInfoOrBuilder(
          int index) {
        if (rewardInfoBuilder_ == null) {
          return rewardInfo_.get(index);  } else {
          return rewardInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getRewardInfoOrBuilderList() {
        if (rewardInfoBuilder_ != null) {
          return rewardInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewardInfo_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addRewardInfoBuilder() {
        return getRewardInfoFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addRewardInfoBuilder(
          int index) {
        return getRewardInfoFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value reward_info = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getRewardInfoBuilderList() {
        return getRewardInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getRewardInfoFieldBuilder() {
        if (rewardInfoBuilder_ == null) {
          rewardInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  rewardInfo_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rewardInfo_ = null;
        }
        return rewardInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_reward_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_reward_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_reward_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<operate_reward_info_s2c>() {
      @java.lang.Override
      public operate_reward_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_reward_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_reward_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_reward_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_get_reward_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_get_reward_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <code>uint32 args1 = 2;</code>
     * @return The args1.
     */
    int getArgs1();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_get_reward_c2s}
   */
  public static final class operate_get_reward_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_get_reward_c2s)
      operate_get_reward_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_get_reward_c2s.newBuilder() to construct.
    private operate_get_reward_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_get_reward_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_get_reward_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int ARGS1_FIELD_NUMBER = 2;
    private int args1_ = 0;
    /**
     * <code>uint32 args1 = 2;</code>
     * @return The args1.
     */
    @java.lang.Override
    public int getArgs1() {
      return args1_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeUInt32(1, type_);
      }
      if (args1_ != 0) {
        output.writeUInt32(2, args1_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, type_);
      }
      if (args1_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, args1_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s) obj;

      if (getType()
          != other.getType()) return false;
      if (getArgs1()
          != other.getArgs1()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + ARGS1_FIELD_NUMBER;
      hash = (53 * hash) + getArgs1();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_get_reward_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_get_reward_c2s)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        args1_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.args1_ = args1_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getArgs1() != 0) {
          setArgs1(other.getArgs1());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                args1_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>uint32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int args1_ ;
      /**
       * <code>uint32 args1 = 2;</code>
       * @return The args1.
       */
      @java.lang.Override
      public int getArgs1() {
        return args1_;
      }
      /**
       * <code>uint32 args1 = 2;</code>
       * @param value The args1 to set.
       * @return This builder for chaining.
       */
      public Builder setArgs1(int value) {

        args1_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 args1 = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearArgs1() {
        bitField0_ = (bitField0_ & ~0x00000002);
        args1_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_get_reward_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_get_reward_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_get_reward_c2s>
        PARSER = new com.google.protobuf.AbstractParser<operate_get_reward_c2s>() {
      @java.lang.Override
      public operate_get_reward_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_get_reward_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_get_reward_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface operate_get_reward_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.operate_get_reward_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <code>uint32 status = 2;</code>
     * @return The status.
     */
    int getStatus();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_get_reward_s2c}
   */
  public static final class operate_get_reward_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.operate_get_reward_s2c)
      operate_get_reward_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use operate_get_reward_s2c.newBuilder() to construct.
    private operate_get_reward_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private operate_get_reward_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new operate_get_reward_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int STATUS_FIELD_NUMBER = 2;
    private int status_ = 0;
    /**
     * <code>uint32 status = 2;</code>
     * @return The status.
     */
    @java.lang.Override
    public int getStatus() {
      return status_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeUInt32(1, type_);
      }
      if (status_ != 0) {
        output.writeUInt32(2, status_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, type_);
      }
      if (status_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, status_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c other = (org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c) obj;

      if (getType()
          != other.getType()) return false;
      if (getStatus()
          != other.getStatus()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + STATUS_FIELD_NUMBER;
      hash = (53 * hash) + getStatus();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.operate_get_reward_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.operate_get_reward_s2c)
        org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        status_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c build() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c result = new org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.status_ = status_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getStatus() != 0) {
          setStatus(other.getStatus());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                status_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>uint32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int status_ ;
      /**
       * <code>uint32 status = 2;</code>
       * @return The status.
       */
      @java.lang.Override
      public int getStatus() {
        return status_;
      }
      /**
       * <code>uint32 status = 2;</code>
       * @param value The status to set.
       * @return This builder for chaining.
       */
      public Builder setStatus(int value) {

        status_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 status = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearStatus() {
        bitField0_ = (bitField0_ & ~0x00000002);
        status_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.operate_get_reward_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.operate_get_reward_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<operate_get_reward_s2c>
        PARSER = new com.google.protobuf.AbstractParser<operate_get_reward_s2c>() {
      @java.lang.Override
      public operate_get_reward_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<operate_get_reward_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<operate_get_reward_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgOperate.operate_get_reward_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_share_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_share_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_share_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_share_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_subscribe_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_subscribe_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_trigger_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_forum_trigger_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_get_reward_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_forum_get_reward_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_update_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_forum_update_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_forum_news_config_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_forum_news_config_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_forum_news_config_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_forum_news_config_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021msg.operate.proto\022\031org.gof.demo.worlds" +
      "rv.msg\032\roptions.proto\032\014define.proto\"\037\n\026o" +
      "perate_share_info_c2s:\005\210\303\032\2014\"L\n\026operate_" +
      "share_info_s2c\022\013\n\003opt\030\001 \001(\r\022\013\n\003num\030\002 \001(\r" +
      "\022\021\n\005geted\030\003 \003(\rB\002\020\000:\005\210\303\032\2014\"-\n\030operate_sh" +
      "are_reward_c2s\022\n\n\002id\030\001 \001(\r:\005\210\303\032\2024\"-\n\030ope" +
      "rate_share_reward_s2c\022\n\n\002id\030\001 \001(\r:\005\210\303\032\2024" +
      "\"t\n\025operate_subscribe_c2s\022\013\n\003uid\030\001 \001(\t\022\021" +
      "\n\tclient_id\030\002 \001(\t\0224\n\004list\030\003 \003(\0132&.org.go" +
      "f.demo.worldsrv.msg.p_subscribe:\005\210\303\032\2034\"\037" +
      "\n\026operate_forum_info_c2s:\005\210\303\032\2044\"\237\001\n\026oper" +
      "ate_forum_info_s2c\022@\n\rreward_status\030\001 \003(" +
      "\0132).org.gof.demo.worldsrv.msg.p_forum_re" +
      "ward\022<\n\014task_process\030\002 \003(\0132&.org.gof.dem" +
      "o.worldsrv.msg.p_key_value:\005\210\303\032\2044\"`\n\031ope" +
      "rate_forum_trigger_c2s\022<\n\014trigger_list\030\001" +
      " \003(\0132&.org.gof.demo.worldsrv.msg.p_key_v" +
      "alue:\005\210\303\032\2054\"F\n\034operate_forum_get_reward_" +
      "c2s\022\014\n\004type\030\001 \001(\r\022\021\n\tconfig_id\030\002 \001(\r:\005\210\303" +
      "\032\2064\"\252\001\n\030operate_forum_update_s2c\022E\n\022rewa" +
      "rd_update_list\030\001 \003(\0132).org.gof.demo.worl" +
      "dsrv.msg.p_forum_reward\022@\n\020task_update_l" +
      "ist\030\002 \003(\0132&.org.gof.demo.worldsrv.msg.p_" +
      "key_value:\005\210\303\032\2074\"$\n\033operate_forum_red_po" +
      "int_c2s:\005\210\303\032\2104\"`\n\033operate_forum_red_poin" +
      "t_s2c\022:\n\ncount_list\030\001 \003(\0132&.org.gof.demo" +
      ".worldsrv.msg.p_key_value:\005\210\303\032\2104\"\036\n\025foru" +
      "m_news_config_c2s:\005\210\303\032\2114\"\205\001\n\025forum_news_" +
      "config_s2c\022\022\n\ndefault_id\030\001 \001(\t\0225\n\004list\030\002" +
      " \003(\0132\'.org.gof.demo.worldsrv.msg.p_forum" +
      "_news\022\014\n\004more\030\003 \001(\t\022\014\n\004goto\030\004 \001(\t:\005\210\303\032\2114" +
      "\" \n\027operate_reward_info_c2s:\005\210\303\032\2124\"]\n\027op" +
      "erate_reward_info_s2c\022;\n\013reward_info\030\001 \003" +
      "(\0132&.org.gof.demo.worldsrv.msg.p_key_val" +
      "ue:\005\210\303\032\2124\"<\n\026operate_get_reward_c2s\022\014\n\004t" +
      "ype\030\001 \001(\r\022\r\n\005args1\030\002 \001(\r:\005\210\303\032\2134\"=\n\026opera" +
      "te_get_reward_s2c\022\014\n\004type\030\001 \001(\r\022\016\n\006statu" +
      "s\030\002 \001(\r:\005\210\303\032\2134b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_operate_share_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_operate_share_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_share_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_operate_share_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_operate_share_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_share_info_s2c_descriptor,
        new java.lang.String[] { "Opt", "Num", "Geted", });
    internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_c2s_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_c2s_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_share_reward_s2c_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_org_gof_demo_worldsrv_msg_operate_subscribe_c2s_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_operate_subscribe_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_subscribe_c2s_descriptor,
        new java.lang.String[] { "Uid", "ClientId", "List", });
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_forum_info_s2c_descriptor,
        new java.lang.String[] { "RewardStatus", "TaskProcess", });
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_trigger_c2s_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_trigger_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_forum_trigger_c2s_descriptor,
        new java.lang.String[] { "TriggerList", });
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_get_reward_c2s_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_get_reward_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_forum_get_reward_c2s_descriptor,
        new java.lang.String[] { "Type", "ConfigId", });
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_update_s2c_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_update_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_forum_update_s2c_descriptor,
        new java.lang.String[] { "RewardUpdateList", "TaskUpdateList", });
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_c2s_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_s2c_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_forum_red_point_s2c_descriptor,
        new java.lang.String[] { "CountList", });
    internal_static_org_gof_demo_worldsrv_msg_forum_news_config_c2s_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_org_gof_demo_worldsrv_msg_forum_news_config_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_forum_news_config_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_forum_news_config_s2c_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_org_gof_demo_worldsrv_msg_forum_news_config_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_forum_news_config_s2c_descriptor,
        new java.lang.String[] { "DefaultId", "List", "More", "Goto", });
    internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_reward_info_s2c_descriptor,
        new java.lang.String[] { "RewardInfo", });
    internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_c2s_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_c2s_descriptor,
        new java.lang.String[] { "Type", "Args1", });
    internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_s2c_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_operate_get_reward_s2c_descriptor,
        new java.lang.String[] { "Type", "Status", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
