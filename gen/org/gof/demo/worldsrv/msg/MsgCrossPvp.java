// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.crossPvp.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgCrossPvp {
  private MsgCrossPvp() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface cross_pvp_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_info_c2s}
   */
  public static final class cross_pvp_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_info_c2s)
      cross_pvp_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_info_c2s.newBuilder() to construct.
    private cross_pvp_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_info_c2s)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_info_c2s>() {
      @java.lang.Override
      public cross_pvp_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <code>uint32 end_time = 2;</code>
     * @return The endTime.
     */
    int getEndTime();

    /**
     * <code>uint32 dan = 3;</code>
     * @return The dan.
     */
    int getDan();

    /**
     * <code>uint32 win_num = 4;</code>
     * @return The winNum.
     */
    int getWinNum();

    /**
     * <code>uint32 combat_num = 5;</code>
     * @return The combatNum.
     */
    int getCombatNum();

    /**
     * <code>uint32 my_score = 6;</code>
     * @return The myScore.
     */
    int getMyScore();

    /**
     * <code>uint32 my_rank = 7;</code>
     * @return The myRank.
     */
    int getMyRank();

    /**
     * <code>uint32 is_sign = 8;</code>
     * @return The isSign.
     */
    int getIsSign();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> 
        getEnemyListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_common_role getEnemyList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
     */
    int getEnemyListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
        getEnemyListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder getEnemyListOrBuilder(
        int index);

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> 
        getRankListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_common_role getRankList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
     */
    int getRankListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
        getRankListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder getRankListOrBuilder(
        int index);

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getExtList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getExt(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
     */
    int getExtCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getExtOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getExtOrBuilder(
        int index);

    /**
     * <code>int32 buy_times = 12;</code>
     * @return The buyTimes.
     */
    int getBuyTimes();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_info_s2c}
   */
  public static final class cross_pvp_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_info_s2c)
      cross_pvp_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_info_s2c.newBuilder() to construct.
    private cross_pvp_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_info_s2c() {
      enemyList_ = java.util.Collections.emptyList();
      rankList_ = java.util.Collections.emptyList();
      ext_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int END_TIME_FIELD_NUMBER = 2;
    private int endTime_ = 0;
    /**
     * <code>uint32 end_time = 2;</code>
     * @return The endTime.
     */
    @java.lang.Override
    public int getEndTime() {
      return endTime_;
    }

    public static final int DAN_FIELD_NUMBER = 3;
    private int dan_ = 0;
    /**
     * <code>uint32 dan = 3;</code>
     * @return The dan.
     */
    @java.lang.Override
    public int getDan() {
      return dan_;
    }

    public static final int WIN_NUM_FIELD_NUMBER = 4;
    private int winNum_ = 0;
    /**
     * <code>uint32 win_num = 4;</code>
     * @return The winNum.
     */
    @java.lang.Override
    public int getWinNum() {
      return winNum_;
    }

    public static final int COMBAT_NUM_FIELD_NUMBER = 5;
    private int combatNum_ = 0;
    /**
     * <code>uint32 combat_num = 5;</code>
     * @return The combatNum.
     */
    @java.lang.Override
    public int getCombatNum() {
      return combatNum_;
    }

    public static final int MY_SCORE_FIELD_NUMBER = 6;
    private int myScore_ = 0;
    /**
     * <code>uint32 my_score = 6;</code>
     * @return The myScore.
     */
    @java.lang.Override
    public int getMyScore() {
      return myScore_;
    }

    public static final int MY_RANK_FIELD_NUMBER = 7;
    private int myRank_ = 0;
    /**
     * <code>uint32 my_rank = 7;</code>
     * @return The myRank.
     */
    @java.lang.Override
    public int getMyRank() {
      return myRank_;
    }

    public static final int IS_SIGN_FIELD_NUMBER = 8;
    private int isSign_ = 0;
    /**
     * <code>uint32 is_sign = 8;</code>
     * @return The isSign.
     */
    @java.lang.Override
    public int getIsSign() {
      return isSign_;
    }

    public static final int ENEMY_LIST_FIELD_NUMBER = 9;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> enemyList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> getEnemyListList() {
      return enemyList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
        getEnemyListOrBuilderList() {
      return enemyList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
     */
    @java.lang.Override
    public int getEnemyListCount() {
      return enemyList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_common_role getEnemyList(int index) {
      return enemyList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder getEnemyListOrBuilder(
        int index) {
      return enemyList_.get(index);
    }

    public static final int RANK_LIST_FIELD_NUMBER = 10;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> rankList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> getRankListList() {
      return rankList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
        getRankListOrBuilderList() {
      return rankList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
     */
    @java.lang.Override
    public int getRankListCount() {
      return rankList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_common_role getRankList(int index) {
      return rankList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder getRankListOrBuilder(
        int index) {
      return rankList_.get(index);
    }

    public static final int EXT_FIELD_NUMBER = 11;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> ext_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getExtList() {
      return ext_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getExtOrBuilderList() {
      return ext_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
     */
    @java.lang.Override
    public int getExtCount() {
      return ext_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getExt(int index) {
      return ext_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getExtOrBuilder(
        int index) {
      return ext_.get(index);
    }

    public static final int BUY_TIMES_FIELD_NUMBER = 12;
    private int buyTimes_ = 0;
    /**
     * <code>int32 buy_times = 12;</code>
     * @return The buyTimes.
     */
    @java.lang.Override
    public int getBuyTimes() {
      return buyTimes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      if (endTime_ != 0) {
        output.writeUInt32(2, endTime_);
      }
      if (dan_ != 0) {
        output.writeUInt32(3, dan_);
      }
      if (winNum_ != 0) {
        output.writeUInt32(4, winNum_);
      }
      if (combatNum_ != 0) {
        output.writeUInt32(5, combatNum_);
      }
      if (myScore_ != 0) {
        output.writeUInt32(6, myScore_);
      }
      if (myRank_ != 0) {
        output.writeUInt32(7, myRank_);
      }
      if (isSign_ != 0) {
        output.writeUInt32(8, isSign_);
      }
      for (int i = 0; i < enemyList_.size(); i++) {
        output.writeMessage(9, enemyList_.get(i));
      }
      for (int i = 0; i < rankList_.size(); i++) {
        output.writeMessage(10, rankList_.get(i));
      }
      for (int i = 0; i < ext_.size(); i++) {
        output.writeMessage(11, ext_.get(i));
      }
      if (buyTimes_ != 0) {
        output.writeInt32(12, buyTimes_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      if (endTime_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, endTime_);
      }
      if (dan_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, dan_);
      }
      if (winNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, winNum_);
      }
      if (combatNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(5, combatNum_);
      }
      if (myScore_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(6, myScore_);
      }
      if (myRank_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, myRank_);
      }
      if (isSign_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(8, isSign_);
      }
      for (int i = 0; i < enemyList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, enemyList_.get(i));
      }
      for (int i = 0; i < rankList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(10, rankList_.get(i));
      }
      for (int i = 0; i < ext_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, ext_.get(i));
      }
      if (buyTimes_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(12, buyTimes_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c) obj;

      if (getType()
          != other.getType()) return false;
      if (getEndTime()
          != other.getEndTime()) return false;
      if (getDan()
          != other.getDan()) return false;
      if (getWinNum()
          != other.getWinNum()) return false;
      if (getCombatNum()
          != other.getCombatNum()) return false;
      if (getMyScore()
          != other.getMyScore()) return false;
      if (getMyRank()
          != other.getMyRank()) return false;
      if (getIsSign()
          != other.getIsSign()) return false;
      if (!getEnemyListList()
          .equals(other.getEnemyListList())) return false;
      if (!getRankListList()
          .equals(other.getRankListList())) return false;
      if (!getExtList()
          .equals(other.getExtList())) return false;
      if (getBuyTimes()
          != other.getBuyTimes()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + END_TIME_FIELD_NUMBER;
      hash = (53 * hash) + getEndTime();
      hash = (37 * hash) + DAN_FIELD_NUMBER;
      hash = (53 * hash) + getDan();
      hash = (37 * hash) + WIN_NUM_FIELD_NUMBER;
      hash = (53 * hash) + getWinNum();
      hash = (37 * hash) + COMBAT_NUM_FIELD_NUMBER;
      hash = (53 * hash) + getCombatNum();
      hash = (37 * hash) + MY_SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getMyScore();
      hash = (37 * hash) + MY_RANK_FIELD_NUMBER;
      hash = (53 * hash) + getMyRank();
      hash = (37 * hash) + IS_SIGN_FIELD_NUMBER;
      hash = (53 * hash) + getIsSign();
      if (getEnemyListCount() > 0) {
        hash = (37 * hash) + ENEMY_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getEnemyListList().hashCode();
      }
      if (getRankListCount() > 0) {
        hash = (37 * hash) + RANK_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getRankListList().hashCode();
      }
      if (getExtCount() > 0) {
        hash = (37 * hash) + EXT_FIELD_NUMBER;
        hash = (53 * hash) + getExtList().hashCode();
      }
      hash = (37 * hash) + BUY_TIMES_FIELD_NUMBER;
      hash = (53 * hash) + getBuyTimes();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_info_s2c)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        endTime_ = 0;
        dan_ = 0;
        winNum_ = 0;
        combatNum_ = 0;
        myScore_ = 0;
        myRank_ = 0;
        isSign_ = 0;
        if (enemyListBuilder_ == null) {
          enemyList_ = java.util.Collections.emptyList();
        } else {
          enemyList_ = null;
          enemyListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        if (rankListBuilder_ == null) {
          rankList_ = java.util.Collections.emptyList();
        } else {
          rankList_ = null;
          rankListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000200);
        if (extBuilder_ == null) {
          ext_ = java.util.Collections.emptyList();
        } else {
          ext_ = null;
          extBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000400);
        buyTimes_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c result) {
        if (enemyListBuilder_ == null) {
          if (((bitField0_ & 0x00000100) != 0)) {
            enemyList_ = java.util.Collections.unmodifiableList(enemyList_);
            bitField0_ = (bitField0_ & ~0x00000100);
          }
          result.enemyList_ = enemyList_;
        } else {
          result.enemyList_ = enemyListBuilder_.build();
        }
        if (rankListBuilder_ == null) {
          if (((bitField0_ & 0x00000200) != 0)) {
            rankList_ = java.util.Collections.unmodifiableList(rankList_);
            bitField0_ = (bitField0_ & ~0x00000200);
          }
          result.rankList_ = rankList_;
        } else {
          result.rankList_ = rankListBuilder_.build();
        }
        if (extBuilder_ == null) {
          if (((bitField0_ & 0x00000400) != 0)) {
            ext_ = java.util.Collections.unmodifiableList(ext_);
            bitField0_ = (bitField0_ & ~0x00000400);
          }
          result.ext_ = ext_;
        } else {
          result.ext_ = extBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.endTime_ = endTime_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.dan_ = dan_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.winNum_ = winNum_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.combatNum_ = combatNum_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.myScore_ = myScore_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.myRank_ = myRank_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.isSign_ = isSign_;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.buyTimes_ = buyTimes_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getEndTime() != 0) {
          setEndTime(other.getEndTime());
        }
        if (other.getDan() != 0) {
          setDan(other.getDan());
        }
        if (other.getWinNum() != 0) {
          setWinNum(other.getWinNum());
        }
        if (other.getCombatNum() != 0) {
          setCombatNum(other.getCombatNum());
        }
        if (other.getMyScore() != 0) {
          setMyScore(other.getMyScore());
        }
        if (other.getMyRank() != 0) {
          setMyRank(other.getMyRank());
        }
        if (other.getIsSign() != 0) {
          setIsSign(other.getIsSign());
        }
        if (enemyListBuilder_ == null) {
          if (!other.enemyList_.isEmpty()) {
            if (enemyList_.isEmpty()) {
              enemyList_ = other.enemyList_;
              bitField0_ = (bitField0_ & ~0x00000100);
            } else {
              ensureEnemyListIsMutable();
              enemyList_.addAll(other.enemyList_);
            }
            onChanged();
          }
        } else {
          if (!other.enemyList_.isEmpty()) {
            if (enemyListBuilder_.isEmpty()) {
              enemyListBuilder_.dispose();
              enemyListBuilder_ = null;
              enemyList_ = other.enemyList_;
              bitField0_ = (bitField0_ & ~0x00000100);
              enemyListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getEnemyListFieldBuilder() : null;
            } else {
              enemyListBuilder_.addAllMessages(other.enemyList_);
            }
          }
        }
        if (rankListBuilder_ == null) {
          if (!other.rankList_.isEmpty()) {
            if (rankList_.isEmpty()) {
              rankList_ = other.rankList_;
              bitField0_ = (bitField0_ & ~0x00000200);
            } else {
              ensureRankListIsMutable();
              rankList_.addAll(other.rankList_);
            }
            onChanged();
          }
        } else {
          if (!other.rankList_.isEmpty()) {
            if (rankListBuilder_.isEmpty()) {
              rankListBuilder_.dispose();
              rankListBuilder_ = null;
              rankList_ = other.rankList_;
              bitField0_ = (bitField0_ & ~0x00000200);
              rankListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRankListFieldBuilder() : null;
            } else {
              rankListBuilder_.addAllMessages(other.rankList_);
            }
          }
        }
        if (extBuilder_ == null) {
          if (!other.ext_.isEmpty()) {
            if (ext_.isEmpty()) {
              ext_ = other.ext_;
              bitField0_ = (bitField0_ & ~0x00000400);
            } else {
              ensureExtIsMutable();
              ext_.addAll(other.ext_);
            }
            onChanged();
          }
        } else {
          if (!other.ext_.isEmpty()) {
            if (extBuilder_.isEmpty()) {
              extBuilder_.dispose();
              extBuilder_ = null;
              ext_ = other.ext_;
              bitField0_ = (bitField0_ & ~0x00000400);
              extBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getExtFieldBuilder() : null;
            } else {
              extBuilder_.addAllMessages(other.ext_);
            }
          }
        }
        if (other.getBuyTimes() != 0) {
          setBuyTimes(other.getBuyTimes());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                endTime_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                dan_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                winNum_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 40: {
                combatNum_ = input.readUInt32();
                bitField0_ |= 0x00000010;
                break;
              } // case 40
              case 48: {
                myScore_ = input.readUInt32();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 56: {
                myRank_ = input.readUInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 64: {
                isSign_ = input.readUInt32();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              case 74: {
                org.gof.demo.worldsrv.msg.Define.p_common_role m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_common_role.parser(),
                        extensionRegistry);
                if (enemyListBuilder_ == null) {
                  ensureEnemyListIsMutable();
                  enemyList_.add(m);
                } else {
                  enemyListBuilder_.addMessage(m);
                }
                break;
              } // case 74
              case 82: {
                org.gof.demo.worldsrv.msg.Define.p_common_role m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_common_role.parser(),
                        extensionRegistry);
                if (rankListBuilder_ == null) {
                  ensureRankListIsMutable();
                  rankList_.add(m);
                } else {
                  rankListBuilder_.addMessage(m);
                }
                break;
              } // case 82
              case 90: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (extBuilder_ == null) {
                  ensureExtIsMutable();
                  ext_.add(m);
                } else {
                  extBuilder_.addMessage(m);
                }
                break;
              } // case 90
              case 96: {
                buyTimes_ = input.readInt32();
                bitField0_ |= 0x00000800;
                break;
              } // case 96
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int endTime_ ;
      /**
       * <code>uint32 end_time = 2;</code>
       * @return The endTime.
       */
      @java.lang.Override
      public int getEndTime() {
        return endTime_;
      }
      /**
       * <code>uint32 end_time = 2;</code>
       * @param value The endTime to set.
       * @return This builder for chaining.
       */
      public Builder setEndTime(int value) {

        endTime_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 end_time = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearEndTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        endTime_ = 0;
        onChanged();
        return this;
      }

      private int dan_ ;
      /**
       * <code>uint32 dan = 3;</code>
       * @return The dan.
       */
      @java.lang.Override
      public int getDan() {
        return dan_;
      }
      /**
       * <code>uint32 dan = 3;</code>
       * @param value The dan to set.
       * @return This builder for chaining.
       */
      public Builder setDan(int value) {

        dan_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 dan = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearDan() {
        bitField0_ = (bitField0_ & ~0x00000004);
        dan_ = 0;
        onChanged();
        return this;
      }

      private int winNum_ ;
      /**
       * <code>uint32 win_num = 4;</code>
       * @return The winNum.
       */
      @java.lang.Override
      public int getWinNum() {
        return winNum_;
      }
      /**
       * <code>uint32 win_num = 4;</code>
       * @param value The winNum to set.
       * @return This builder for chaining.
       */
      public Builder setWinNum(int value) {

        winNum_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 win_num = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearWinNum() {
        bitField0_ = (bitField0_ & ~0x00000008);
        winNum_ = 0;
        onChanged();
        return this;
      }

      private int combatNum_ ;
      /**
       * <code>uint32 combat_num = 5;</code>
       * @return The combatNum.
       */
      @java.lang.Override
      public int getCombatNum() {
        return combatNum_;
      }
      /**
       * <code>uint32 combat_num = 5;</code>
       * @param value The combatNum to set.
       * @return This builder for chaining.
       */
      public Builder setCombatNum(int value) {

        combatNum_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 combat_num = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearCombatNum() {
        bitField0_ = (bitField0_ & ~0x00000010);
        combatNum_ = 0;
        onChanged();
        return this;
      }

      private int myScore_ ;
      /**
       * <code>uint32 my_score = 6;</code>
       * @return The myScore.
       */
      @java.lang.Override
      public int getMyScore() {
        return myScore_;
      }
      /**
       * <code>uint32 my_score = 6;</code>
       * @param value The myScore to set.
       * @return This builder for chaining.
       */
      public Builder setMyScore(int value) {

        myScore_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 my_score = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearMyScore() {
        bitField0_ = (bitField0_ & ~0x00000020);
        myScore_ = 0;
        onChanged();
        return this;
      }

      private int myRank_ ;
      /**
       * <code>uint32 my_rank = 7;</code>
       * @return The myRank.
       */
      @java.lang.Override
      public int getMyRank() {
        return myRank_;
      }
      /**
       * <code>uint32 my_rank = 7;</code>
       * @param value The myRank to set.
       * @return This builder for chaining.
       */
      public Builder setMyRank(int value) {

        myRank_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 my_rank = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearMyRank() {
        bitField0_ = (bitField0_ & ~0x00000040);
        myRank_ = 0;
        onChanged();
        return this;
      }

      private int isSign_ ;
      /**
       * <code>uint32 is_sign = 8;</code>
       * @return The isSign.
       */
      @java.lang.Override
      public int getIsSign() {
        return isSign_;
      }
      /**
       * <code>uint32 is_sign = 8;</code>
       * @param value The isSign to set.
       * @return This builder for chaining.
       */
      public Builder setIsSign(int value) {

        isSign_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 is_sign = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsSign() {
        bitField0_ = (bitField0_ & ~0x00000080);
        isSign_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> enemyList_ =
        java.util.Collections.emptyList();
      private void ensureEnemyListIsMutable() {
        if (!((bitField0_ & 0x00000100) != 0)) {
          enemyList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_common_role>(enemyList_);
          bitField0_ |= 0x00000100;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_common_role, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder, org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> enemyListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> getEnemyListList() {
        if (enemyListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(enemyList_);
        } else {
          return enemyListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public int getEnemyListCount() {
        if (enemyListBuilder_ == null) {
          return enemyList_.size();
        } else {
          return enemyListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role getEnemyList(int index) {
        if (enemyListBuilder_ == null) {
          return enemyList_.get(index);
        } else {
          return enemyListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public Builder setEnemyList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role value) {
        if (enemyListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnemyListIsMutable();
          enemyList_.set(index, value);
          onChanged();
        } else {
          enemyListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public Builder setEnemyList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder builderForValue) {
        if (enemyListBuilder_ == null) {
          ensureEnemyListIsMutable();
          enemyList_.set(index, builderForValue.build());
          onChanged();
        } else {
          enemyListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public Builder addEnemyList(org.gof.demo.worldsrv.msg.Define.p_common_role value) {
        if (enemyListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnemyListIsMutable();
          enemyList_.add(value);
          onChanged();
        } else {
          enemyListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public Builder addEnemyList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role value) {
        if (enemyListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEnemyListIsMutable();
          enemyList_.add(index, value);
          onChanged();
        } else {
          enemyListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public Builder addEnemyList(
          org.gof.demo.worldsrv.msg.Define.p_common_role.Builder builderForValue) {
        if (enemyListBuilder_ == null) {
          ensureEnemyListIsMutable();
          enemyList_.add(builderForValue.build());
          onChanged();
        } else {
          enemyListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public Builder addEnemyList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder builderForValue) {
        if (enemyListBuilder_ == null) {
          ensureEnemyListIsMutable();
          enemyList_.add(index, builderForValue.build());
          onChanged();
        } else {
          enemyListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public Builder addAllEnemyList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_common_role> values) {
        if (enemyListBuilder_ == null) {
          ensureEnemyListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, enemyList_);
          onChanged();
        } else {
          enemyListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public Builder clearEnemyList() {
        if (enemyListBuilder_ == null) {
          enemyList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000100);
          onChanged();
        } else {
          enemyListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public Builder removeEnemyList(int index) {
        if (enemyListBuilder_ == null) {
          ensureEnemyListIsMutable();
          enemyList_.remove(index);
          onChanged();
        } else {
          enemyListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role.Builder getEnemyListBuilder(
          int index) {
        return getEnemyListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder getEnemyListOrBuilder(
          int index) {
        if (enemyListBuilder_ == null) {
          return enemyList_.get(index);  } else {
          return enemyListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
           getEnemyListOrBuilderList() {
        if (enemyListBuilder_ != null) {
          return enemyListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(enemyList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role.Builder addEnemyListBuilder() {
        return getEnemyListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_common_role.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role.Builder addEnemyListBuilder(
          int index) {
        return getEnemyListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_common_role.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role enemy_list = 9;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role.Builder> 
           getEnemyListBuilderList() {
        return getEnemyListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_common_role, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder, org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
          getEnemyListFieldBuilder() {
        if (enemyListBuilder_ == null) {
          enemyListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_common_role, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder, org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder>(
                  enemyList_,
                  ((bitField0_ & 0x00000100) != 0),
                  getParentForChildren(),
                  isClean());
          enemyList_ = null;
        }
        return enemyListBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> rankList_ =
        java.util.Collections.emptyList();
      private void ensureRankListIsMutable() {
        if (!((bitField0_ & 0x00000200) != 0)) {
          rankList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_common_role>(rankList_);
          bitField0_ |= 0x00000200;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_common_role, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder, org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> rankListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> getRankListList() {
        if (rankListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rankList_);
        } else {
          return rankListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public int getRankListCount() {
        if (rankListBuilder_ == null) {
          return rankList_.size();
        } else {
          return rankListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role getRankList(int index) {
        if (rankListBuilder_ == null) {
          return rankList_.get(index);
        } else {
          return rankListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public Builder setRankList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.set(index, value);
          onChanged();
        } else {
          rankListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public Builder setRankList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.set(index, builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public Builder addRankList(org.gof.demo.worldsrv.msg.Define.p_common_role value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.add(value);
          onChanged();
        } else {
          rankListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public Builder addRankList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.add(index, value);
          onChanged();
        } else {
          rankListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public Builder addRankList(
          org.gof.demo.worldsrv.msg.Define.p_common_role.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.add(builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public Builder addRankList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.add(index, builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public Builder addAllRankList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_common_role> values) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rankList_);
          onChanged();
        } else {
          rankListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public Builder clearRankList() {
        if (rankListBuilder_ == null) {
          rankList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000200);
          onChanged();
        } else {
          rankListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public Builder removeRankList(int index) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.remove(index);
          onChanged();
        } else {
          rankListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role.Builder getRankListBuilder(
          int index) {
        return getRankListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder getRankListOrBuilder(
          int index) {
        if (rankListBuilder_ == null) {
          return rankList_.get(index);  } else {
          return rankListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
           getRankListOrBuilderList() {
        if (rankListBuilder_ != null) {
          return rankListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rankList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role.Builder addRankListBuilder() {
        return getRankListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_common_role.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role.Builder addRankListBuilder(
          int index) {
        return getRankListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_common_role.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 10;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role.Builder> 
           getRankListBuilderList() {
        return getRankListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_common_role, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder, org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
          getRankListFieldBuilder() {
        if (rankListBuilder_ == null) {
          rankListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_common_role, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder, org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder>(
                  rankList_,
                  ((bitField0_ & 0x00000200) != 0),
                  getParentForChildren(),
                  isClean());
          rankList_ = null;
        }
        return rankListBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> ext_ =
        java.util.Collections.emptyList();
      private void ensureExtIsMutable() {
        if (!((bitField0_ & 0x00000400) != 0)) {
          ext_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(ext_);
          bitField0_ |= 0x00000400;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> extBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getExtList() {
        if (extBuilder_ == null) {
          return java.util.Collections.unmodifiableList(ext_);
        } else {
          return extBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public int getExtCount() {
        if (extBuilder_ == null) {
          return ext_.size();
        } else {
          return extBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getExt(int index) {
        if (extBuilder_ == null) {
          return ext_.get(index);
        } else {
          return extBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public Builder setExt(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (extBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExtIsMutable();
          ext_.set(index, value);
          onChanged();
        } else {
          extBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public Builder setExt(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (extBuilder_ == null) {
          ensureExtIsMutable();
          ext_.set(index, builderForValue.build());
          onChanged();
        } else {
          extBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public Builder addExt(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (extBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExtIsMutable();
          ext_.add(value);
          onChanged();
        } else {
          extBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public Builder addExt(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (extBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExtIsMutable();
          ext_.add(index, value);
          onChanged();
        } else {
          extBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public Builder addExt(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (extBuilder_ == null) {
          ensureExtIsMutable();
          ext_.add(builderForValue.build());
          onChanged();
        } else {
          extBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public Builder addExt(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (extBuilder_ == null) {
          ensureExtIsMutable();
          ext_.add(index, builderForValue.build());
          onChanged();
        } else {
          extBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public Builder addAllExt(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (extBuilder_ == null) {
          ensureExtIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, ext_);
          onChanged();
        } else {
          extBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public Builder clearExt() {
        if (extBuilder_ == null) {
          ext_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000400);
          onChanged();
        } else {
          extBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public Builder removeExt(int index) {
        if (extBuilder_ == null) {
          ensureExtIsMutable();
          ext_.remove(index);
          onChanged();
        } else {
          extBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getExtBuilder(
          int index) {
        return getExtFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getExtOrBuilder(
          int index) {
        if (extBuilder_ == null) {
          return ext_.get(index);  } else {
          return extBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getExtOrBuilderList() {
        if (extBuilder_ != null) {
          return extBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(ext_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addExtBuilder() {
        return getExtFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addExtBuilder(
          int index) {
        return getExtFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext = 11;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getExtBuilderList() {
        return getExtFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getExtFieldBuilder() {
        if (extBuilder_ == null) {
          extBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  ext_,
                  ((bitField0_ & 0x00000400) != 0),
                  getParentForChildren(),
                  isClean());
          ext_ = null;
        }
        return extBuilder_;
      }

      private int buyTimes_ ;
      /**
       * <code>int32 buy_times = 12;</code>
       * @return The buyTimes.
       */
      @java.lang.Override
      public int getBuyTimes() {
        return buyTimes_;
      }
      /**
       * <code>int32 buy_times = 12;</code>
       * @param value The buyTimes to set.
       * @return This builder for chaining.
       */
      public Builder setBuyTimes(int value) {

        buyTimes_ = value;
        bitField0_ |= 0x00000800;
        onChanged();
        return this;
      }
      /**
       * <code>int32 buy_times = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearBuyTimes() {
        bitField0_ = (bitField0_ & ~0x00000800);
        buyTimes_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_info_s2c>() {
      @java.lang.Override
      public cross_pvp_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_combat_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_combat_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int64 eid = 1;</code>
     * @return The eid.
     */
    long getEid();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_combat_c2s}
   */
  public static final class cross_pvp_combat_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_combat_c2s)
      cross_pvp_combat_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_combat_c2s.newBuilder() to construct.
    private cross_pvp_combat_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_combat_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_combat_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s.Builder.class);
    }

    public static final int EID_FIELD_NUMBER = 1;
    private long eid_ = 0L;
    /**
     * <code>int64 eid = 1;</code>
     * @return The eid.
     */
    @java.lang.Override
    public long getEid() {
      return eid_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (eid_ != 0L) {
        output.writeInt64(1, eid_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (eid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, eid_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s) obj;

      if (getEid()
          != other.getEid()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + EID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEid());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_combat_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_combat_c2s)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        eid_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.eid_ = eid_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s.getDefaultInstance()) return this;
        if (other.getEid() != 0L) {
          setEid(other.getEid());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                eid_ = input.readInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long eid_ ;
      /**
       * <code>int64 eid = 1;</code>
       * @return The eid.
       */
      @java.lang.Override
      public long getEid() {
        return eid_;
      }
      /**
       * <code>int64 eid = 1;</code>
       * @param value The eid to set.
       * @return This builder for chaining.
       */
      public Builder setEid(long value) {

        eid_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int64 eid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        eid_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_combat_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_combat_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_combat_c2s>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_combat_c2s>() {
      @java.lang.Override
      public cross_pvp_combat_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_combat_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_combat_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_combat_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_combat_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>uint64 eid = 2;</code>
     * @return The eid.
     */
    long getEid();

    /**
     * <code>uint64 seed = 3;</code>
     * @return The seed.
     */
    long getSeed();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
     * @return Whether the atkData field is set.
     */
    boolean hasAtkData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
     * @return The atkData.
     */
    org.gof.demo.worldsrv.msg.Define.p_battle_role getAtkData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getAtkDataOrBuilder();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
     * @return Whether the defData field is set.
     */
    boolean hasDefData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
     * @return The defData.
     */
    org.gof.demo.worldsrv.msg.Define.p_battle_role getDefData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getDefDataOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_combat_s2c}
   */
  public static final class cross_pvp_combat_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_combat_s2c)
      cross_pvp_combat_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_combat_s2c.newBuilder() to construct.
    private cross_pvp_combat_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_combat_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_combat_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>uint32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int EID_FIELD_NUMBER = 2;
    private long eid_ = 0L;
    /**
     * <code>uint64 eid = 2;</code>
     * @return The eid.
     */
    @java.lang.Override
    public long getEid() {
      return eid_;
    }

    public static final int SEED_FIELD_NUMBER = 3;
    private long seed_ = 0L;
    /**
     * <code>uint64 seed = 3;</code>
     * @return The seed.
     */
    @java.lang.Override
    public long getSeed() {
      return seed_;
    }

    public static final int ATK_DATA_FIELD_NUMBER = 4;
    private org.gof.demo.worldsrv.msg.Define.p_battle_role atkData_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
     * @return Whether the atkData field is set.
     */
    @java.lang.Override
    public boolean hasAtkData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
     * @return The atkData.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_battle_role getAtkData() {
      return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : atkData_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getAtkDataOrBuilder() {
      return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : atkData_;
    }

    public static final int DEF_DATA_FIELD_NUMBER = 5;
    private org.gof.demo.worldsrv.msg.Define.p_battle_role defData_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
     * @return Whether the defData field is set.
     */
    @java.lang.Override
    public boolean hasDefData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
     * @return The defData.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_battle_role getDefData() {
      return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : defData_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getDefDataOrBuilder() {
      return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : defData_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeUInt32(1, code_);
      }
      if (eid_ != 0L) {
        output.writeUInt64(2, eid_);
      }
      if (seed_ != 0L) {
        output.writeUInt64(3, seed_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(4, getAtkData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(5, getDefData());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, code_);
      }
      if (eid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, eid_);
      }
      if (seed_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, seed_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getAtkData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getDefData());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getEid()
          != other.getEid()) return false;
      if (getSeed()
          != other.getSeed()) return false;
      if (hasAtkData() != other.hasAtkData()) return false;
      if (hasAtkData()) {
        if (!getAtkData()
            .equals(other.getAtkData())) return false;
      }
      if (hasDefData() != other.hasDefData()) return false;
      if (hasDefData()) {
        if (!getDefData()
            .equals(other.getDefData())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + EID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getEid());
      hash = (37 * hash) + SEED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSeed());
      if (hasAtkData()) {
        hash = (37 * hash) + ATK_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getAtkData().hashCode();
      }
      if (hasDefData()) {
        hash = (37 * hash) + DEF_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getDefData().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_combat_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_combat_s2c)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getAtkDataFieldBuilder();
          getDefDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        eid_ = 0L;
        seed_ = 0L;
        atkData_ = null;
        if (atkDataBuilder_ != null) {
          atkDataBuilder_.dispose();
          atkDataBuilder_ = null;
        }
        defData_ = null;
        if (defDataBuilder_ != null) {
          defDataBuilder_.dispose();
          defDataBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.eid_ = eid_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.seed_ = seed_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.atkData_ = atkDataBuilder_ == null
              ? atkData_
              : atkDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.defData_ = defDataBuilder_ == null
              ? defData_
              : defDataBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getEid() != 0L) {
          setEid(other.getEid());
        }
        if (other.getSeed() != 0L) {
          setSeed(other.getSeed());
        }
        if (other.hasAtkData()) {
          mergeAtkData(other.getAtkData());
        }
        if (other.hasDefData()) {
          mergeDefData(other.getDefData());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                eid_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                seed_ = input.readUInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                input.readMessage(
                    getAtkDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                input.readMessage(
                    getDefDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>uint32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>uint32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private long eid_ ;
      /**
       * <code>uint64 eid = 2;</code>
       * @return The eid.
       */
      @java.lang.Override
      public long getEid() {
        return eid_;
      }
      /**
       * <code>uint64 eid = 2;</code>
       * @param value The eid to set.
       * @return This builder for chaining.
       */
      public Builder setEid(long value) {

        eid_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 eid = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearEid() {
        bitField0_ = (bitField0_ & ~0x00000002);
        eid_ = 0L;
        onChanged();
        return this;
      }

      private long seed_ ;
      /**
       * <code>uint64 seed = 3;</code>
       * @return The seed.
       */
      @java.lang.Override
      public long getSeed() {
        return seed_;
      }
      /**
       * <code>uint64 seed = 3;</code>
       * @param value The seed to set.
       * @return This builder for chaining.
       */
      public Builder setSeed(long value) {

        seed_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 seed = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeed() {
        bitField0_ = (bitField0_ & ~0x00000004);
        seed_ = 0L;
        onChanged();
        return this;
      }

      private org.gof.demo.worldsrv.msg.Define.p_battle_role atkData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder> atkDataBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       * @return Whether the atkData field is set.
       */
      public boolean hasAtkData() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       * @return The atkData.
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_role getAtkData() {
        if (atkDataBuilder_ == null) {
          return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : atkData_;
        } else {
          return atkDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      public Builder setAtkData(org.gof.demo.worldsrv.msg.Define.p_battle_role value) {
        if (atkDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          atkData_ = value;
        } else {
          atkDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      public Builder setAtkData(
          org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder builderForValue) {
        if (atkDataBuilder_ == null) {
          atkData_ = builderForValue.build();
        } else {
          atkDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      public Builder mergeAtkData(org.gof.demo.worldsrv.msg.Define.p_battle_role value) {
        if (atkDataBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            atkData_ != null &&
            atkData_ != org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance()) {
            getAtkDataBuilder().mergeFrom(value);
          } else {
            atkData_ = value;
          }
        } else {
          atkDataBuilder_.mergeFrom(value);
        }
        if (atkData_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      public Builder clearAtkData() {
        bitField0_ = (bitField0_ & ~0x00000008);
        atkData_ = null;
        if (atkDataBuilder_ != null) {
          atkDataBuilder_.dispose();
          atkDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder getAtkDataBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getAtkDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getAtkDataOrBuilder() {
        if (atkDataBuilder_ != null) {
          return atkDataBuilder_.getMessageOrBuilder();
        } else {
          return atkData_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : atkData_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder> 
          getAtkDataFieldBuilder() {
        if (atkDataBuilder_ == null) {
          atkDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder>(
                  getAtkData(),
                  getParentForChildren(),
                  isClean());
          atkData_ = null;
        }
        return atkDataBuilder_;
      }

      private org.gof.demo.worldsrv.msg.Define.p_battle_role defData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder> defDataBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       * @return Whether the defData field is set.
       */
      public boolean hasDefData() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       * @return The defData.
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_role getDefData() {
        if (defDataBuilder_ == null) {
          return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : defData_;
        } else {
          return defDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      public Builder setDefData(org.gof.demo.worldsrv.msg.Define.p_battle_role value) {
        if (defDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          defData_ = value;
        } else {
          defDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      public Builder setDefData(
          org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder builderForValue) {
        if (defDataBuilder_ == null) {
          defData_ = builderForValue.build();
        } else {
          defDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      public Builder mergeDefData(org.gof.demo.worldsrv.msg.Define.p_battle_role value) {
        if (defDataBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
            defData_ != null &&
            defData_ != org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance()) {
            getDefDataBuilder().mergeFrom(value);
          } else {
            defData_ = value;
          }
        } else {
          defDataBuilder_.mergeFrom(value);
        }
        if (defData_ != null) {
          bitField0_ |= 0x00000010;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      public Builder clearDefData() {
        bitField0_ = (bitField0_ & ~0x00000010);
        defData_ = null;
        if (defDataBuilder_ != null) {
          defDataBuilder_.dispose();
          defDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder getDefDataBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getDefDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getDefDataOrBuilder() {
        if (defDataBuilder_ != null) {
          return defDataBuilder_.getMessageOrBuilder();
        } else {
          return defData_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : defData_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder> 
          getDefDataFieldBuilder() {
        if (defDataBuilder_ == null) {
          defDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder>(
                  getDefData(),
                  getParentForChildren(),
                  isClean());
          defData_ = null;
        }
        return defDataBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_combat_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_combat_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_combat_s2c>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_combat_s2c>() {
      @java.lang.Override
      public cross_pvp_combat_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_combat_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_combat_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_combat_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_result_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_result_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 wid = 1;</code>
     * @return The wid.
     */
    long getWid();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_result_c2s}
   */
  public static final class cross_pvp_result_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_result_c2s)
      cross_pvp_result_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_result_c2s.newBuilder() to construct.
    private cross_pvp_result_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_result_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_result_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s.Builder.class);
    }

    public static final int WID_FIELD_NUMBER = 1;
    private long wid_ = 0L;
    /**
     * <code>uint64 wid = 1;</code>
     * @return The wid.
     */
    @java.lang.Override
    public long getWid() {
      return wid_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (wid_ != 0L) {
        output.writeUInt64(1, wid_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (wid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, wid_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s) obj;

      if (getWid()
          != other.getWid()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + WID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getWid());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_result_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_result_c2s)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        wid_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.wid_ = wid_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s.getDefaultInstance()) return this;
        if (other.getWid() != 0L) {
          setWid(other.getWid());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                wid_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long wid_ ;
      /**
       * <code>uint64 wid = 1;</code>
       * @return The wid.
       */
      @java.lang.Override
      public long getWid() {
        return wid_;
      }
      /**
       * <code>uint64 wid = 1;</code>
       * @param value The wid to set.
       * @return This builder for chaining.
       */
      public Builder setWid(long value) {

        wid_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 wid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearWid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        wid_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_result_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_result_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_result_c2s>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_result_c2s>() {
      @java.lang.Override
      public cross_pvp_result_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_result_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_result_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_result_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_result_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 is_win = 1;</code>
     * @return The isWin.
     */
    int getIsWin();

    /**
     * <code>int32 my_score = 2;</code>
     * @return The myScore.
     */
    int getMyScore();

    /**
     * <code>uint32 my_rank = 3;</code>
     * @return The myRank.
     */
    int getMyRank();

    /**
     * <code>int32 my_score_change = 4;</code>
     * @return The myScoreChange.
     */
    int getMyScoreChange();

    /**
     * <code>string e_name = 5;</code>
     * @return The eName.
     */
    java.lang.String getEName();
    /**
     * <code>string e_name = 5;</code>
     * @return The bytes for eName.
     */
    com.google.protobuf.ByteString
        getENameBytes();

    /**
     * <code>uint32 e_rank = 6;</code>
     * @return The eRank.
     */
    int getERank();

    /**
     * <code>uint32 e_score = 7;</code>
     * @return The eScore.
     */
    int getEScore();

    /**
     * <code>int32 e_score_change = 8;</code>
     * @return The eScoreChange.
     */
    int getEScoreChange();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
     * @return Whether the eHead field is set.
     */
    boolean hasEHead();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
     * @return The eHead.
     */
    org.gof.demo.worldsrv.msg.Define.p_head getEHead();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_headOrBuilder getEHeadOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_result_s2c}
   */
  public static final class cross_pvp_result_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_result_s2c)
      cross_pvp_result_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_result_s2c.newBuilder() to construct.
    private cross_pvp_result_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_result_s2c() {
      eName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_result_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int IS_WIN_FIELD_NUMBER = 1;
    private int isWin_ = 0;
    /**
     * <code>int32 is_win = 1;</code>
     * @return The isWin.
     */
    @java.lang.Override
    public int getIsWin() {
      return isWin_;
    }

    public static final int MY_SCORE_FIELD_NUMBER = 2;
    private int myScore_ = 0;
    /**
     * <code>int32 my_score = 2;</code>
     * @return The myScore.
     */
    @java.lang.Override
    public int getMyScore() {
      return myScore_;
    }

    public static final int MY_RANK_FIELD_NUMBER = 3;
    private int myRank_ = 0;
    /**
     * <code>uint32 my_rank = 3;</code>
     * @return The myRank.
     */
    @java.lang.Override
    public int getMyRank() {
      return myRank_;
    }

    public static final int MY_SCORE_CHANGE_FIELD_NUMBER = 4;
    private int myScoreChange_ = 0;
    /**
     * <code>int32 my_score_change = 4;</code>
     * @return The myScoreChange.
     */
    @java.lang.Override
    public int getMyScoreChange() {
      return myScoreChange_;
    }

    public static final int E_NAME_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private volatile java.lang.Object eName_ = "";
    /**
     * <code>string e_name = 5;</code>
     * @return The eName.
     */
    @java.lang.Override
    public java.lang.String getEName() {
      java.lang.Object ref = eName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        eName_ = s;
        return s;
      }
    }
    /**
     * <code>string e_name = 5;</code>
     * @return The bytes for eName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getENameBytes() {
      java.lang.Object ref = eName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        eName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int E_RANK_FIELD_NUMBER = 6;
    private int eRank_ = 0;
    /**
     * <code>uint32 e_rank = 6;</code>
     * @return The eRank.
     */
    @java.lang.Override
    public int getERank() {
      return eRank_;
    }

    public static final int E_SCORE_FIELD_NUMBER = 7;
    private int eScore_ = 0;
    /**
     * <code>uint32 e_score = 7;</code>
     * @return The eScore.
     */
    @java.lang.Override
    public int getEScore() {
      return eScore_;
    }

    public static final int E_SCORE_CHANGE_FIELD_NUMBER = 8;
    private int eScoreChange_ = 0;
    /**
     * <code>int32 e_score_change = 8;</code>
     * @return The eScoreChange.
     */
    @java.lang.Override
    public int getEScoreChange() {
      return eScoreChange_;
    }

    public static final int E_HEAD_FIELD_NUMBER = 9;
    private org.gof.demo.worldsrv.msg.Define.p_head eHead_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
     * @return Whether the eHead field is set.
     */
    @java.lang.Override
    public boolean hasEHead() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
     * @return The eHead.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_head getEHead() {
      return eHead_ == null ? org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance() : eHead_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_headOrBuilder getEHeadOrBuilder() {
      return eHead_ == null ? org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance() : eHead_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (isWin_ != 0) {
        output.writeInt32(1, isWin_);
      }
      if (myScore_ != 0) {
        output.writeInt32(2, myScore_);
      }
      if (myRank_ != 0) {
        output.writeUInt32(3, myRank_);
      }
      if (myScoreChange_ != 0) {
        output.writeInt32(4, myScoreChange_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(eName_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, eName_);
      }
      if (eRank_ != 0) {
        output.writeUInt32(6, eRank_);
      }
      if (eScore_ != 0) {
        output.writeUInt32(7, eScore_);
      }
      if (eScoreChange_ != 0) {
        output.writeInt32(8, eScoreChange_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(9, getEHead());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (isWin_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, isWin_);
      }
      if (myScore_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, myScore_);
      }
      if (myRank_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, myRank_);
      }
      if (myScoreChange_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, myScoreChange_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(eName_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, eName_);
      }
      if (eRank_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(6, eRank_);
      }
      if (eScore_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(7, eScore_);
      }
      if (eScoreChange_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, eScoreChange_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, getEHead());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c) obj;

      if (getIsWin()
          != other.getIsWin()) return false;
      if (getMyScore()
          != other.getMyScore()) return false;
      if (getMyRank()
          != other.getMyRank()) return false;
      if (getMyScoreChange()
          != other.getMyScoreChange()) return false;
      if (!getEName()
          .equals(other.getEName())) return false;
      if (getERank()
          != other.getERank()) return false;
      if (getEScore()
          != other.getEScore()) return false;
      if (getEScoreChange()
          != other.getEScoreChange()) return false;
      if (hasEHead() != other.hasEHead()) return false;
      if (hasEHead()) {
        if (!getEHead()
            .equals(other.getEHead())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + IS_WIN_FIELD_NUMBER;
      hash = (53 * hash) + getIsWin();
      hash = (37 * hash) + MY_SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getMyScore();
      hash = (37 * hash) + MY_RANK_FIELD_NUMBER;
      hash = (53 * hash) + getMyRank();
      hash = (37 * hash) + MY_SCORE_CHANGE_FIELD_NUMBER;
      hash = (53 * hash) + getMyScoreChange();
      hash = (37 * hash) + E_NAME_FIELD_NUMBER;
      hash = (53 * hash) + getEName().hashCode();
      hash = (37 * hash) + E_RANK_FIELD_NUMBER;
      hash = (53 * hash) + getERank();
      hash = (37 * hash) + E_SCORE_FIELD_NUMBER;
      hash = (53 * hash) + getEScore();
      hash = (37 * hash) + E_SCORE_CHANGE_FIELD_NUMBER;
      hash = (53 * hash) + getEScoreChange();
      if (hasEHead()) {
        hash = (37 * hash) + E_HEAD_FIELD_NUMBER;
        hash = (53 * hash) + getEHead().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_result_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_result_s2c)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getEHeadFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        isWin_ = 0;
        myScore_ = 0;
        myRank_ = 0;
        myScoreChange_ = 0;
        eName_ = "";
        eRank_ = 0;
        eScore_ = 0;
        eScoreChange_ = 0;
        eHead_ = null;
        if (eHeadBuilder_ != null) {
          eHeadBuilder_.dispose();
          eHeadBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.isWin_ = isWin_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.myScore_ = myScore_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.myRank_ = myRank_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.myScoreChange_ = myScoreChange_;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.eName_ = eName_;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.eRank_ = eRank_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.eScore_ = eScore_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.eScoreChange_ = eScoreChange_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.eHead_ = eHeadBuilder_ == null
              ? eHead_
              : eHeadBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c.getDefaultInstance()) return this;
        if (other.getIsWin() != 0) {
          setIsWin(other.getIsWin());
        }
        if (other.getMyScore() != 0) {
          setMyScore(other.getMyScore());
        }
        if (other.getMyRank() != 0) {
          setMyRank(other.getMyRank());
        }
        if (other.getMyScoreChange() != 0) {
          setMyScoreChange(other.getMyScoreChange());
        }
        if (!other.getEName().isEmpty()) {
          eName_ = other.eName_;
          bitField0_ |= 0x00000010;
          onChanged();
        }
        if (other.getERank() != 0) {
          setERank(other.getERank());
        }
        if (other.getEScore() != 0) {
          setEScore(other.getEScore());
        }
        if (other.getEScoreChange() != 0) {
          setEScoreChange(other.getEScoreChange());
        }
        if (other.hasEHead()) {
          mergeEHead(other.getEHead());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                isWin_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                myScore_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                myRank_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                myScoreChange_ = input.readInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 42: {
                eName_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 48: {
                eRank_ = input.readUInt32();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 56: {
                eScore_ = input.readUInt32();
                bitField0_ |= 0x00000040;
                break;
              } // case 56
              case 64: {
                eScoreChange_ = input.readInt32();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              case 74: {
                input.readMessage(
                    getEHeadFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000100;
                break;
              } // case 74
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int isWin_ ;
      /**
       * <code>int32 is_win = 1;</code>
       * @return The isWin.
       */
      @java.lang.Override
      public int getIsWin() {
        return isWin_;
      }
      /**
       * <code>int32 is_win = 1;</code>
       * @param value The isWin to set.
       * @return This builder for chaining.
       */
      public Builder setIsWin(int value) {

        isWin_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 is_win = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsWin() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isWin_ = 0;
        onChanged();
        return this;
      }

      private int myScore_ ;
      /**
       * <code>int32 my_score = 2;</code>
       * @return The myScore.
       */
      @java.lang.Override
      public int getMyScore() {
        return myScore_;
      }
      /**
       * <code>int32 my_score = 2;</code>
       * @param value The myScore to set.
       * @return This builder for chaining.
       */
      public Builder setMyScore(int value) {

        myScore_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int32 my_score = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMyScore() {
        bitField0_ = (bitField0_ & ~0x00000002);
        myScore_ = 0;
        onChanged();
        return this;
      }

      private int myRank_ ;
      /**
       * <code>uint32 my_rank = 3;</code>
       * @return The myRank.
       */
      @java.lang.Override
      public int getMyRank() {
        return myRank_;
      }
      /**
       * <code>uint32 my_rank = 3;</code>
       * @param value The myRank to set.
       * @return This builder for chaining.
       */
      public Builder setMyRank(int value) {

        myRank_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 my_rank = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMyRank() {
        bitField0_ = (bitField0_ & ~0x00000004);
        myRank_ = 0;
        onChanged();
        return this;
      }

      private int myScoreChange_ ;
      /**
       * <code>int32 my_score_change = 4;</code>
       * @return The myScoreChange.
       */
      @java.lang.Override
      public int getMyScoreChange() {
        return myScoreChange_;
      }
      /**
       * <code>int32 my_score_change = 4;</code>
       * @param value The myScoreChange to set.
       * @return This builder for chaining.
       */
      public Builder setMyScoreChange(int value) {

        myScoreChange_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>int32 my_score_change = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearMyScoreChange() {
        bitField0_ = (bitField0_ & ~0x00000008);
        myScoreChange_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object eName_ = "";
      /**
       * <code>string e_name = 5;</code>
       * @return The eName.
       */
      public java.lang.String getEName() {
        java.lang.Object ref = eName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          eName_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string e_name = 5;</code>
       * @return The bytes for eName.
       */
      public com.google.protobuf.ByteString
          getENameBytes() {
        java.lang.Object ref = eName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          eName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string e_name = 5;</code>
       * @param value The eName to set.
       * @return This builder for chaining.
       */
      public Builder setEName(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        eName_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>string e_name = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearEName() {
        eName_ = getDefaultInstance().getEName();
        bitField0_ = (bitField0_ & ~0x00000010);
        onChanged();
        return this;
      }
      /**
       * <code>string e_name = 5;</code>
       * @param value The bytes for eName to set.
       * @return This builder for chaining.
       */
      public Builder setENameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        eName_ = value;
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }

      private int eRank_ ;
      /**
       * <code>uint32 e_rank = 6;</code>
       * @return The eRank.
       */
      @java.lang.Override
      public int getERank() {
        return eRank_;
      }
      /**
       * <code>uint32 e_rank = 6;</code>
       * @param value The eRank to set.
       * @return This builder for chaining.
       */
      public Builder setERank(int value) {

        eRank_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 e_rank = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearERank() {
        bitField0_ = (bitField0_ & ~0x00000020);
        eRank_ = 0;
        onChanged();
        return this;
      }

      private int eScore_ ;
      /**
       * <code>uint32 e_score = 7;</code>
       * @return The eScore.
       */
      @java.lang.Override
      public int getEScore() {
        return eScore_;
      }
      /**
       * <code>uint32 e_score = 7;</code>
       * @param value The eScore to set.
       * @return This builder for chaining.
       */
      public Builder setEScore(int value) {

        eScore_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 e_score = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearEScore() {
        bitField0_ = (bitField0_ & ~0x00000040);
        eScore_ = 0;
        onChanged();
        return this;
      }

      private int eScoreChange_ ;
      /**
       * <code>int32 e_score_change = 8;</code>
       * @return The eScoreChange.
       */
      @java.lang.Override
      public int getEScoreChange() {
        return eScoreChange_;
      }
      /**
       * <code>int32 e_score_change = 8;</code>
       * @param value The eScoreChange to set.
       * @return This builder for chaining.
       */
      public Builder setEScoreChange(int value) {

        eScoreChange_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>int32 e_score_change = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearEScoreChange() {
        bitField0_ = (bitField0_ & ~0x00000080);
        eScoreChange_ = 0;
        onChanged();
        return this;
      }

      private org.gof.demo.worldsrv.msg.Define.p_head eHead_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_head, org.gof.demo.worldsrv.msg.Define.p_head.Builder, org.gof.demo.worldsrv.msg.Define.p_headOrBuilder> eHeadBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
       * @return Whether the eHead field is set.
       */
      public boolean hasEHead() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
       * @return The eHead.
       */
      public org.gof.demo.worldsrv.msg.Define.p_head getEHead() {
        if (eHeadBuilder_ == null) {
          return eHead_ == null ? org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance() : eHead_;
        } else {
          return eHeadBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
       */
      public Builder setEHead(org.gof.demo.worldsrv.msg.Define.p_head value) {
        if (eHeadBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          eHead_ = value;
        } else {
          eHeadBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
       */
      public Builder setEHead(
          org.gof.demo.worldsrv.msg.Define.p_head.Builder builderForValue) {
        if (eHeadBuilder_ == null) {
          eHead_ = builderForValue.build();
        } else {
          eHeadBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000100;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
       */
      public Builder mergeEHead(org.gof.demo.worldsrv.msg.Define.p_head value) {
        if (eHeadBuilder_ == null) {
          if (((bitField0_ & 0x00000100) != 0) &&
            eHead_ != null &&
            eHead_ != org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance()) {
            getEHeadBuilder().mergeFrom(value);
          } else {
            eHead_ = value;
          }
        } else {
          eHeadBuilder_.mergeFrom(value);
        }
        if (eHead_ != null) {
          bitField0_ |= 0x00000100;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
       */
      public Builder clearEHead() {
        bitField0_ = (bitField0_ & ~0x00000100);
        eHead_ = null;
        if (eHeadBuilder_ != null) {
          eHeadBuilder_.dispose();
          eHeadBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_head.Builder getEHeadBuilder() {
        bitField0_ |= 0x00000100;
        onChanged();
        return getEHeadFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_headOrBuilder getEHeadOrBuilder() {
        if (eHeadBuilder_ != null) {
          return eHeadBuilder_.getMessageOrBuilder();
        } else {
          return eHead_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_head.getDefaultInstance() : eHead_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_head e_head = 9;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_head, org.gof.demo.worldsrv.msg.Define.p_head.Builder, org.gof.demo.worldsrv.msg.Define.p_headOrBuilder> 
          getEHeadFieldBuilder() {
        if (eHeadBuilder_ == null) {
          eHeadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_head, org.gof.demo.worldsrv.msg.Define.p_head.Builder, org.gof.demo.worldsrv.msg.Define.p_headOrBuilder>(
                  getEHead(),
                  getParentForChildren(),
                  isClean());
          eHead_ = null;
        }
        return eHeadBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_result_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_result_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_result_s2c>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_result_s2c>() {
      @java.lang.Override
      public cross_pvp_result_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_result_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_result_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_result_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_history_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_history_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_history_c2s}
   */
  public static final class cross_pvp_history_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_history_c2s)
      cross_pvp_history_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_history_c2s.newBuilder() to construct.
    private cross_pvp_history_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_history_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_history_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_history_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_history_c2s)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_history_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_history_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_history_c2s>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_history_c2s>() {
      @java.lang.Override
      public cross_pvp_history_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_history_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_history_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_history_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_history_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_arena_history> 
        getHistoryListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_arena_history getHistoryList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
     */
    int getHistoryListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_arena_historyOrBuilder> 
        getHistoryListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_arena_historyOrBuilder getHistoryListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_history_s2c}
   */
  public static final class cross_pvp_history_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_history_s2c)
      cross_pvp_history_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_history_s2c.newBuilder() to construct.
    private cross_pvp_history_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_history_s2c() {
      historyList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_history_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c.Builder.class);
    }

    public static final int HISTORY_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_arena_history> historyList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_arena_history> getHistoryListList() {
      return historyList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_arena_historyOrBuilder> 
        getHistoryListOrBuilderList() {
      return historyList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
     */
    @java.lang.Override
    public int getHistoryListCount() {
      return historyList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_arena_history getHistoryList(int index) {
      return historyList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_arena_historyOrBuilder getHistoryListOrBuilder(
        int index) {
      return historyList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < historyList_.size(); i++) {
        output.writeMessage(1, historyList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < historyList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, historyList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c) obj;

      if (!getHistoryListList()
          .equals(other.getHistoryListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getHistoryListCount() > 0) {
        hash = (37 * hash) + HISTORY_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getHistoryListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_history_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_history_s2c)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (historyListBuilder_ == null) {
          historyList_ = java.util.Collections.emptyList();
        } else {
          historyList_ = null;
          historyListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c result) {
        if (historyListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            historyList_ = java.util.Collections.unmodifiableList(historyList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.historyList_ = historyList_;
        } else {
          result.historyList_ = historyListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c.getDefaultInstance()) return this;
        if (historyListBuilder_ == null) {
          if (!other.historyList_.isEmpty()) {
            if (historyList_.isEmpty()) {
              historyList_ = other.historyList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureHistoryListIsMutable();
              historyList_.addAll(other.historyList_);
            }
            onChanged();
          }
        } else {
          if (!other.historyList_.isEmpty()) {
            if (historyListBuilder_.isEmpty()) {
              historyListBuilder_.dispose();
              historyListBuilder_ = null;
              historyList_ = other.historyList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              historyListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getHistoryListFieldBuilder() : null;
            } else {
              historyListBuilder_.addAllMessages(other.historyList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_arena_history m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_arena_history.parser(),
                        extensionRegistry);
                if (historyListBuilder_ == null) {
                  ensureHistoryListIsMutable();
                  historyList_.add(m);
                } else {
                  historyListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_arena_history> historyList_ =
        java.util.Collections.emptyList();
      private void ensureHistoryListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          historyList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_arena_history>(historyList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_arena_history, org.gof.demo.worldsrv.msg.Define.p_arena_history.Builder, org.gof.demo.worldsrv.msg.Define.p_arena_historyOrBuilder> historyListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_arena_history> getHistoryListList() {
        if (historyListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(historyList_);
        } else {
          return historyListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public int getHistoryListCount() {
        if (historyListBuilder_ == null) {
          return historyList_.size();
        } else {
          return historyListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_arena_history getHistoryList(int index) {
        if (historyListBuilder_ == null) {
          return historyList_.get(index);
        } else {
          return historyListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public Builder setHistoryList(
          int index, org.gof.demo.worldsrv.msg.Define.p_arena_history value) {
        if (historyListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHistoryListIsMutable();
          historyList_.set(index, value);
          onChanged();
        } else {
          historyListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public Builder setHistoryList(
          int index, org.gof.demo.worldsrv.msg.Define.p_arena_history.Builder builderForValue) {
        if (historyListBuilder_ == null) {
          ensureHistoryListIsMutable();
          historyList_.set(index, builderForValue.build());
          onChanged();
        } else {
          historyListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public Builder addHistoryList(org.gof.demo.worldsrv.msg.Define.p_arena_history value) {
        if (historyListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHistoryListIsMutable();
          historyList_.add(value);
          onChanged();
        } else {
          historyListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public Builder addHistoryList(
          int index, org.gof.demo.worldsrv.msg.Define.p_arena_history value) {
        if (historyListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureHistoryListIsMutable();
          historyList_.add(index, value);
          onChanged();
        } else {
          historyListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public Builder addHistoryList(
          org.gof.demo.worldsrv.msg.Define.p_arena_history.Builder builderForValue) {
        if (historyListBuilder_ == null) {
          ensureHistoryListIsMutable();
          historyList_.add(builderForValue.build());
          onChanged();
        } else {
          historyListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public Builder addHistoryList(
          int index, org.gof.demo.worldsrv.msg.Define.p_arena_history.Builder builderForValue) {
        if (historyListBuilder_ == null) {
          ensureHistoryListIsMutable();
          historyList_.add(index, builderForValue.build());
          onChanged();
        } else {
          historyListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public Builder addAllHistoryList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_arena_history> values) {
        if (historyListBuilder_ == null) {
          ensureHistoryListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, historyList_);
          onChanged();
        } else {
          historyListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public Builder clearHistoryList() {
        if (historyListBuilder_ == null) {
          historyList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          historyListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public Builder removeHistoryList(int index) {
        if (historyListBuilder_ == null) {
          ensureHistoryListIsMutable();
          historyList_.remove(index);
          onChanged();
        } else {
          historyListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_arena_history.Builder getHistoryListBuilder(
          int index) {
        return getHistoryListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_arena_historyOrBuilder getHistoryListOrBuilder(
          int index) {
        if (historyListBuilder_ == null) {
          return historyList_.get(index);  } else {
          return historyListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_arena_historyOrBuilder> 
           getHistoryListOrBuilderList() {
        if (historyListBuilder_ != null) {
          return historyListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(historyList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_arena_history.Builder addHistoryListBuilder() {
        return getHistoryListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_arena_history.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_arena_history.Builder addHistoryListBuilder(
          int index) {
        return getHistoryListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_arena_history.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_arena_history history_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_arena_history.Builder> 
           getHistoryListBuilderList() {
        return getHistoryListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_arena_history, org.gof.demo.worldsrv.msg.Define.p_arena_history.Builder, org.gof.demo.worldsrv.msg.Define.p_arena_historyOrBuilder> 
          getHistoryListFieldBuilder() {
        if (historyListBuilder_ == null) {
          historyListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_arena_history, org.gof.demo.worldsrv.msg.Define.p_arena_history.Builder, org.gof.demo.worldsrv.msg.Define.p_arena_historyOrBuilder>(
                  historyList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          historyList_ = null;
        }
        return historyListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_history_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_history_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_history_s2c>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_history_s2c>() {
      @java.lang.Override
      public cross_pvp_history_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_history_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_history_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_history_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_video_play_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_video_play_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 vid = 1;</code>
     * @return The vid.
     */
    long getVid();

    /**
     * <code>uint32 source = 2;</code>
     * @return The source.
     */
    int getSource();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_video_play_c2s}
   */
  public static final class cross_pvp_video_play_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_video_play_c2s)
      cross_pvp_video_play_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_video_play_c2s.newBuilder() to construct.
    private cross_pvp_video_play_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_video_play_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_video_play_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s.Builder.class);
    }

    public static final int VID_FIELD_NUMBER = 1;
    private long vid_ = 0L;
    /**
     * <code>uint64 vid = 1;</code>
     * @return The vid.
     */
    @java.lang.Override
    public long getVid() {
      return vid_;
    }

    public static final int SOURCE_FIELD_NUMBER = 2;
    private int source_ = 0;
    /**
     * <code>uint32 source = 2;</code>
     * @return The source.
     */
    @java.lang.Override
    public int getSource() {
      return source_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (vid_ != 0L) {
        output.writeUInt64(1, vid_);
      }
      if (source_ != 0) {
        output.writeUInt32(2, source_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (vid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, vid_);
      }
      if (source_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, source_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s) obj;

      if (getVid()
          != other.getVid()) return false;
      if (getSource()
          != other.getSource()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + VID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getVid());
      hash = (37 * hash) + SOURCE_FIELD_NUMBER;
      hash = (53 * hash) + getSource();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_video_play_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_video_play_c2s)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        vid_ = 0L;
        source_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.vid_ = vid_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.source_ = source_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s.getDefaultInstance()) return this;
        if (other.getVid() != 0L) {
          setVid(other.getVid());
        }
        if (other.getSource() != 0) {
          setSource(other.getSource());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                vid_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                source_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long vid_ ;
      /**
       * <code>uint64 vid = 1;</code>
       * @return The vid.
       */
      @java.lang.Override
      public long getVid() {
        return vid_;
      }
      /**
       * <code>uint64 vid = 1;</code>
       * @param value The vid to set.
       * @return This builder for chaining.
       */
      public Builder setVid(long value) {

        vid_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 vid = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearVid() {
        bitField0_ = (bitField0_ & ~0x00000001);
        vid_ = 0L;
        onChanged();
        return this;
      }

      private int source_ ;
      /**
       * <code>uint32 source = 2;</code>
       * @return The source.
       */
      @java.lang.Override
      public int getSource() {
        return source_;
      }
      /**
       * <code>uint32 source = 2;</code>
       * @param value The source to set.
       * @return This builder for chaining.
       */
      public Builder setSource(int value) {

        source_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 source = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSource() {
        bitField0_ = (bitField0_ & ~0x00000002);
        source_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_video_play_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_video_play_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_video_play_c2s>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_video_play_c2s>() {
      @java.lang.Override
      public cross_pvp_video_play_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_video_play_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_video_play_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_video_play_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_video_play_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>uint64 vid = 2;</code>
     * @return The vid.
     */
    long getVid();

    /**
     * <code>uint64 seed = 3;</code>
     * @return The seed.
     */
    long getSeed();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
     * @return Whether the atkData field is set.
     */
    boolean hasAtkData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
     * @return The atkData.
     */
    org.gof.demo.worldsrv.msg.Define.p_battle_role getAtkData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getAtkDataOrBuilder();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
     * @return Whether the defData field is set.
     */
    boolean hasDefData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
     * @return The defData.
     */
    org.gof.demo.worldsrv.msg.Define.p_battle_role getDefData();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getDefDataOrBuilder();

    /**
     * <code>uint32 source = 6;</code>
     * @return The source.
     */
    int getSource();

    /**
     * <code>string collects = 7;</code>
     * @return The collects.
     */
    java.lang.String getCollects();
    /**
     * <code>string collects = 7;</code>
     * @return The bytes for collects.
     */
    com.google.protobuf.ByteString
        getCollectsBytes();

    /**
     * <code>uint64 winner_id = 8;</code>
     * @return The winnerId.
     */
    long getWinnerId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_video_play_s2c}
   */
  public static final class cross_pvp_video_play_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_video_play_s2c)
      cross_pvp_video_play_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_video_play_s2c.newBuilder() to construct.
    private cross_pvp_video_play_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_video_play_s2c() {
      collects_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_video_play_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int VID_FIELD_NUMBER = 2;
    private long vid_ = 0L;
    /**
     * <code>uint64 vid = 2;</code>
     * @return The vid.
     */
    @java.lang.Override
    public long getVid() {
      return vid_;
    }

    public static final int SEED_FIELD_NUMBER = 3;
    private long seed_ = 0L;
    /**
     * <code>uint64 seed = 3;</code>
     * @return The seed.
     */
    @java.lang.Override
    public long getSeed() {
      return seed_;
    }

    public static final int ATK_DATA_FIELD_NUMBER = 4;
    private org.gof.demo.worldsrv.msg.Define.p_battle_role atkData_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
     * @return Whether the atkData field is set.
     */
    @java.lang.Override
    public boolean hasAtkData() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
     * @return The atkData.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_battle_role getAtkData() {
      return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : atkData_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getAtkDataOrBuilder() {
      return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : atkData_;
    }

    public static final int DEF_DATA_FIELD_NUMBER = 5;
    private org.gof.demo.worldsrv.msg.Define.p_battle_role defData_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
     * @return Whether the defData field is set.
     */
    @java.lang.Override
    public boolean hasDefData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
     * @return The defData.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_battle_role getDefData() {
      return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : defData_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getDefDataOrBuilder() {
      return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : defData_;
    }

    public static final int SOURCE_FIELD_NUMBER = 6;
    private int source_ = 0;
    /**
     * <code>uint32 source = 6;</code>
     * @return The source.
     */
    @java.lang.Override
    public int getSource() {
      return source_;
    }

    public static final int COLLECTS_FIELD_NUMBER = 7;
    @SuppressWarnings("serial")
    private volatile java.lang.Object collects_ = "";
    /**
     * <code>string collects = 7;</code>
     * @return The collects.
     */
    @java.lang.Override
    public java.lang.String getCollects() {
      java.lang.Object ref = collects_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        collects_ = s;
        return s;
      }
    }
    /**
     * <code>string collects = 7;</code>
     * @return The bytes for collects.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getCollectsBytes() {
      java.lang.Object ref = collects_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        collects_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int WINNER_ID_FIELD_NUMBER = 8;
    private long winnerId_ = 0L;
    /**
     * <code>uint64 winner_id = 8;</code>
     * @return The winnerId.
     */
    @java.lang.Override
    public long getWinnerId() {
      return winnerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (vid_ != 0L) {
        output.writeUInt64(2, vid_);
      }
      if (seed_ != 0L) {
        output.writeUInt64(3, seed_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(4, getAtkData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(5, getDefData());
      }
      if (source_ != 0) {
        output.writeUInt32(6, source_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(collects_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, collects_);
      }
      if (winnerId_ != 0L) {
        output.writeUInt64(8, winnerId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (vid_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, vid_);
      }
      if (seed_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(3, seed_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getAtkData());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getDefData());
      }
      if (source_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(6, source_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(collects_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, collects_);
      }
      if (winnerId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(8, winnerId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getVid()
          != other.getVid()) return false;
      if (getSeed()
          != other.getSeed()) return false;
      if (hasAtkData() != other.hasAtkData()) return false;
      if (hasAtkData()) {
        if (!getAtkData()
            .equals(other.getAtkData())) return false;
      }
      if (hasDefData() != other.hasDefData()) return false;
      if (hasDefData()) {
        if (!getDefData()
            .equals(other.getDefData())) return false;
      }
      if (getSource()
          != other.getSource()) return false;
      if (!getCollects()
          .equals(other.getCollects())) return false;
      if (getWinnerId()
          != other.getWinnerId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + VID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getVid());
      hash = (37 * hash) + SEED_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getSeed());
      if (hasAtkData()) {
        hash = (37 * hash) + ATK_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getAtkData().hashCode();
      }
      if (hasDefData()) {
        hash = (37 * hash) + DEF_DATA_FIELD_NUMBER;
        hash = (53 * hash) + getDefData().hashCode();
      }
      hash = (37 * hash) + SOURCE_FIELD_NUMBER;
      hash = (53 * hash) + getSource();
      hash = (37 * hash) + COLLECTS_FIELD_NUMBER;
      hash = (53 * hash) + getCollects().hashCode();
      hash = (37 * hash) + WINNER_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getWinnerId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_video_play_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_video_play_s2c)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getAtkDataFieldBuilder();
          getDefDataFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        vid_ = 0L;
        seed_ = 0L;
        atkData_ = null;
        if (atkDataBuilder_ != null) {
          atkDataBuilder_.dispose();
          atkDataBuilder_ = null;
        }
        defData_ = null;
        if (defDataBuilder_ != null) {
          defDataBuilder_.dispose();
          defDataBuilder_ = null;
        }
        source_ = 0;
        collects_ = "";
        winnerId_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.vid_ = vid_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.seed_ = seed_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.atkData_ = atkDataBuilder_ == null
              ? atkData_
              : atkDataBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.defData_ = defDataBuilder_ == null
              ? defData_
              : defDataBuilder_.build();
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.source_ = source_;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.collects_ = collects_;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.winnerId_ = winnerId_;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getVid() != 0L) {
          setVid(other.getVid());
        }
        if (other.getSeed() != 0L) {
          setSeed(other.getSeed());
        }
        if (other.hasAtkData()) {
          mergeAtkData(other.getAtkData());
        }
        if (other.hasDefData()) {
          mergeDefData(other.getDefData());
        }
        if (other.getSource() != 0) {
          setSource(other.getSource());
        }
        if (!other.getCollects().isEmpty()) {
          collects_ = other.collects_;
          bitField0_ |= 0x00000040;
          onChanged();
        }
        if (other.getWinnerId() != 0L) {
          setWinnerId(other.getWinnerId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                vid_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                seed_ = input.readUInt64();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                input.readMessage(
                    getAtkDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                input.readMessage(
                    getDefDataFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000010;
                break;
              } // case 42
              case 48: {
                source_ = input.readUInt32();
                bitField0_ |= 0x00000020;
                break;
              } // case 48
              case 58: {
                collects_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000040;
                break;
              } // case 58
              case 64: {
                winnerId_ = input.readUInt64();
                bitField0_ |= 0x00000080;
                break;
              } // case 64
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private long vid_ ;
      /**
       * <code>uint64 vid = 2;</code>
       * @return The vid.
       */
      @java.lang.Override
      public long getVid() {
        return vid_;
      }
      /**
       * <code>uint64 vid = 2;</code>
       * @param value The vid to set.
       * @return This builder for chaining.
       */
      public Builder setVid(long value) {

        vid_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 vid = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearVid() {
        bitField0_ = (bitField0_ & ~0x00000002);
        vid_ = 0L;
        onChanged();
        return this;
      }

      private long seed_ ;
      /**
       * <code>uint64 seed = 3;</code>
       * @return The seed.
       */
      @java.lang.Override
      public long getSeed() {
        return seed_;
      }
      /**
       * <code>uint64 seed = 3;</code>
       * @param value The seed to set.
       * @return This builder for chaining.
       */
      public Builder setSeed(long value) {

        seed_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 seed = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeed() {
        bitField0_ = (bitField0_ & ~0x00000004);
        seed_ = 0L;
        onChanged();
        return this;
      }

      private org.gof.demo.worldsrv.msg.Define.p_battle_role atkData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder> atkDataBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       * @return Whether the atkData field is set.
       */
      public boolean hasAtkData() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       * @return The atkData.
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_role getAtkData() {
        if (atkDataBuilder_ == null) {
          return atkData_ == null ? org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : atkData_;
        } else {
          return atkDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      public Builder setAtkData(org.gof.demo.worldsrv.msg.Define.p_battle_role value) {
        if (atkDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          atkData_ = value;
        } else {
          atkDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      public Builder setAtkData(
          org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder builderForValue) {
        if (atkDataBuilder_ == null) {
          atkData_ = builderForValue.build();
        } else {
          atkDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      public Builder mergeAtkData(org.gof.demo.worldsrv.msg.Define.p_battle_role value) {
        if (atkDataBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
            atkData_ != null &&
            atkData_ != org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance()) {
            getAtkDataBuilder().mergeFrom(value);
          } else {
            atkData_ = value;
          }
        } else {
          atkDataBuilder_.mergeFrom(value);
        }
        if (atkData_ != null) {
          bitField0_ |= 0x00000008;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      public Builder clearAtkData() {
        bitField0_ = (bitField0_ & ~0x00000008);
        atkData_ = null;
        if (atkDataBuilder_ != null) {
          atkDataBuilder_.dispose();
          atkDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder getAtkDataBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getAtkDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getAtkDataOrBuilder() {
        if (atkDataBuilder_ != null) {
          return atkDataBuilder_.getMessageOrBuilder();
        } else {
          return atkData_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : atkData_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role atk_data = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder> 
          getAtkDataFieldBuilder() {
        if (atkDataBuilder_ == null) {
          atkDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder>(
                  getAtkData(),
                  getParentForChildren(),
                  isClean());
          atkData_ = null;
        }
        return atkDataBuilder_;
      }

      private org.gof.demo.worldsrv.msg.Define.p_battle_role defData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder> defDataBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       * @return Whether the defData field is set.
       */
      public boolean hasDefData() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       * @return The defData.
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_role getDefData() {
        if (defDataBuilder_ == null) {
          return defData_ == null ? org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : defData_;
        } else {
          return defDataBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      public Builder setDefData(org.gof.demo.worldsrv.msg.Define.p_battle_role value) {
        if (defDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          defData_ = value;
        } else {
          defDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      public Builder setDefData(
          org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder builderForValue) {
        if (defDataBuilder_ == null) {
          defData_ = builderForValue.build();
        } else {
          defDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      public Builder mergeDefData(org.gof.demo.worldsrv.msg.Define.p_battle_role value) {
        if (defDataBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
            defData_ != null &&
            defData_ != org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance()) {
            getDefDataBuilder().mergeFrom(value);
          } else {
            defData_ = value;
          }
        } else {
          defDataBuilder_.mergeFrom(value);
        }
        if (defData_ != null) {
          bitField0_ |= 0x00000010;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      public Builder clearDefData() {
        bitField0_ = (bitField0_ & ~0x00000010);
        defData_ = null;
        if (defDataBuilder_ != null) {
          defDataBuilder_.dispose();
          defDataBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder getDefDataBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getDefDataFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder getDefDataOrBuilder() {
        if (defDataBuilder_ != null) {
          return defDataBuilder_.getMessageOrBuilder();
        } else {
          return defData_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_battle_role.getDefaultInstance() : defData_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_battle_role def_data = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder> 
          getDefDataFieldBuilder() {
        if (defDataBuilder_ == null) {
          defDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_battle_role, org.gof.demo.worldsrv.msg.Define.p_battle_role.Builder, org.gof.demo.worldsrv.msg.Define.p_battle_roleOrBuilder>(
                  getDefData(),
                  getParentForChildren(),
                  isClean());
          defData_ = null;
        }
        return defDataBuilder_;
      }

      private int source_ ;
      /**
       * <code>uint32 source = 6;</code>
       * @return The source.
       */
      @java.lang.Override
      public int getSource() {
        return source_;
      }
      /**
       * <code>uint32 source = 6;</code>
       * @param value The source to set.
       * @return This builder for chaining.
       */
      public Builder setSource(int value) {

        source_ = value;
        bitField0_ |= 0x00000020;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 source = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearSource() {
        bitField0_ = (bitField0_ & ~0x00000020);
        source_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object collects_ = "";
      /**
       * <code>string collects = 7;</code>
       * @return The collects.
       */
      public java.lang.String getCollects() {
        java.lang.Object ref = collects_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          collects_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string collects = 7;</code>
       * @return The bytes for collects.
       */
      public com.google.protobuf.ByteString
          getCollectsBytes() {
        java.lang.Object ref = collects_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          collects_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string collects = 7;</code>
       * @param value The collects to set.
       * @return This builder for chaining.
       */
      public Builder setCollects(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        collects_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }
      /**
       * <code>string collects = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearCollects() {
        collects_ = getDefaultInstance().getCollects();
        bitField0_ = (bitField0_ & ~0x00000040);
        onChanged();
        return this;
      }
      /**
       * <code>string collects = 7;</code>
       * @param value The bytes for collects to set.
       * @return This builder for chaining.
       */
      public Builder setCollectsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        collects_ = value;
        bitField0_ |= 0x00000040;
        onChanged();
        return this;
      }

      private long winnerId_ ;
      /**
       * <code>uint64 winner_id = 8;</code>
       * @return The winnerId.
       */
      @java.lang.Override
      public long getWinnerId() {
        return winnerId_;
      }
      /**
       * <code>uint64 winner_id = 8;</code>
       * @param value The winnerId to set.
       * @return This builder for chaining.
       */
      public Builder setWinnerId(long value) {

        winnerId_ = value;
        bitField0_ |= 0x00000080;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 winner_id = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearWinnerId() {
        bitField0_ = (bitField0_ & ~0x00000080);
        winnerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_video_play_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_video_play_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_video_play_s2c>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_video_play_s2c>() {
      @java.lang.Override
      public cross_pvp_video_play_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_video_play_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_video_play_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_video_play_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_sign_up_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_sign_up_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_sign_up_c2s}
   */
  public static final class cross_pvp_sign_up_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_sign_up_c2s)
      cross_pvp_sign_up_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_sign_up_c2s.newBuilder() to construct.
    private cross_pvp_sign_up_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_sign_up_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_sign_up_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_sign_up_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_sign_up_c2s)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_sign_up_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_sign_up_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_sign_up_c2s>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_sign_up_c2s>() {
      @java.lang.Override
      public cross_pvp_sign_up_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_sign_up_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_sign_up_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_sign_up_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_sign_up_s2c)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_sign_up_s2c}
   */
  public static final class cross_pvp_sign_up_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_sign_up_s2c)
      cross_pvp_sign_up_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_sign_up_s2c.newBuilder() to construct.
    private cross_pvp_sign_up_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_sign_up_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_sign_up_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_sign_up_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_sign_up_s2c)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_sign_up_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_sign_up_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_sign_up_s2c>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_sign_up_s2c>() {
      @java.lang.Override
      public cross_pvp_sign_up_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_sign_up_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_sign_up_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_sign_up_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_stage_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_stage_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 stage = 1;</code>
     * @return The stage.
     */
    int getStage();

    /**
     * <code>uint32 end_time = 2;</code>
     * @return The endTime.
     */
    int getEndTime();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_stage_s2c}
   */
  public static final class cross_pvp_stage_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_stage_s2c)
      cross_pvp_stage_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_stage_s2c.newBuilder() to construct.
    private cross_pvp_stage_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_stage_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_stage_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_stage_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_stage_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c.Builder.class);
    }

    public static final int STAGE_FIELD_NUMBER = 1;
    private int stage_ = 0;
    /**
     * <code>uint32 stage = 1;</code>
     * @return The stage.
     */
    @java.lang.Override
    public int getStage() {
      return stage_;
    }

    public static final int END_TIME_FIELD_NUMBER = 2;
    private int endTime_ = 0;
    /**
     * <code>uint32 end_time = 2;</code>
     * @return The endTime.
     */
    @java.lang.Override
    public int getEndTime() {
      return endTime_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (stage_ != 0) {
        output.writeUInt32(1, stage_);
      }
      if (endTime_ != 0) {
        output.writeUInt32(2, endTime_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (stage_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, stage_);
      }
      if (endTime_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, endTime_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c) obj;

      if (getStage()
          != other.getStage()) return false;
      if (getEndTime()
          != other.getEndTime()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + STAGE_FIELD_NUMBER;
      hash = (53 * hash) + getStage();
      hash = (37 * hash) + END_TIME_FIELD_NUMBER;
      hash = (53 * hash) + getEndTime();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_stage_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_stage_s2c)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_stage_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_stage_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        stage_ = 0;
        endTime_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_stage_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.stage_ = stage_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.endTime_ = endTime_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c.getDefaultInstance()) return this;
        if (other.getStage() != 0) {
          setStage(other.getStage());
        }
        if (other.getEndTime() != 0) {
          setEndTime(other.getEndTime());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                stage_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                endTime_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int stage_ ;
      /**
       * <code>uint32 stage = 1;</code>
       * @return The stage.
       */
      @java.lang.Override
      public int getStage() {
        return stage_;
      }
      /**
       * <code>uint32 stage = 1;</code>
       * @param value The stage to set.
       * @return This builder for chaining.
       */
      public Builder setStage(int value) {

        stage_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 stage = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearStage() {
        bitField0_ = (bitField0_ & ~0x00000001);
        stage_ = 0;
        onChanged();
        return this;
      }

      private int endTime_ ;
      /**
       * <code>uint32 end_time = 2;</code>
       * @return The endTime.
       */
      @java.lang.Override
      public int getEndTime() {
        return endTime_;
      }
      /**
       * <code>uint32 end_time = 2;</code>
       * @param value The endTime to set.
       * @return This builder for chaining.
       */
      public Builder setEndTime(int value) {

        endTime_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 end_time = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearEndTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        endTime_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_stage_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_stage_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_stage_s2c>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_stage_s2c>() {
      @java.lang.Override
      public cross_pvp_stage_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_stage_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_stage_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_stage_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_rank_change_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_rank_change_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> 
        getChangeListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_common_role getChangeList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
     */
    int getChangeListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
        getChangeListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder getChangeListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_rank_change_s2c}
   */
  public static final class cross_pvp_rank_change_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_rank_change_s2c)
      cross_pvp_rank_change_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_rank_change_s2c.newBuilder() to construct.
    private cross_pvp_rank_change_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_rank_change_s2c() {
      changeList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_rank_change_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_change_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_change_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c.Builder.class);
    }

    public static final int CHANGE_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> changeList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> getChangeListList() {
      return changeList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
        getChangeListOrBuilderList() {
      return changeList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
     */
    @java.lang.Override
    public int getChangeListCount() {
      return changeList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_common_role getChangeList(int index) {
      return changeList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder getChangeListOrBuilder(
        int index) {
      return changeList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < changeList_.size(); i++) {
        output.writeMessage(1, changeList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < changeList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, changeList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c) obj;

      if (!getChangeListList()
          .equals(other.getChangeListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getChangeListCount() > 0) {
        hash = (37 * hash) + CHANGE_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getChangeListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_rank_change_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_rank_change_s2c)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_change_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_change_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (changeListBuilder_ == null) {
          changeList_ = java.util.Collections.emptyList();
        } else {
          changeList_ = null;
          changeListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_change_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c result) {
        if (changeListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            changeList_ = java.util.Collections.unmodifiableList(changeList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.changeList_ = changeList_;
        } else {
          result.changeList_ = changeListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c.getDefaultInstance()) return this;
        if (changeListBuilder_ == null) {
          if (!other.changeList_.isEmpty()) {
            if (changeList_.isEmpty()) {
              changeList_ = other.changeList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureChangeListIsMutable();
              changeList_.addAll(other.changeList_);
            }
            onChanged();
          }
        } else {
          if (!other.changeList_.isEmpty()) {
            if (changeListBuilder_.isEmpty()) {
              changeListBuilder_.dispose();
              changeListBuilder_ = null;
              changeList_ = other.changeList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              changeListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getChangeListFieldBuilder() : null;
            } else {
              changeListBuilder_.addAllMessages(other.changeList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_common_role m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_common_role.parser(),
                        extensionRegistry);
                if (changeListBuilder_ == null) {
                  ensureChangeListIsMutable();
                  changeList_.add(m);
                } else {
                  changeListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> changeList_ =
        java.util.Collections.emptyList();
      private void ensureChangeListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          changeList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_common_role>(changeList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_common_role, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder, org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> changeListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> getChangeListList() {
        if (changeListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(changeList_);
        } else {
          return changeListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public int getChangeListCount() {
        if (changeListBuilder_ == null) {
          return changeList_.size();
        } else {
          return changeListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role getChangeList(int index) {
        if (changeListBuilder_ == null) {
          return changeList_.get(index);
        } else {
          return changeListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public Builder setChangeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role value) {
        if (changeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChangeListIsMutable();
          changeList_.set(index, value);
          onChanged();
        } else {
          changeListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public Builder setChangeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder builderForValue) {
        if (changeListBuilder_ == null) {
          ensureChangeListIsMutable();
          changeList_.set(index, builderForValue.build());
          onChanged();
        } else {
          changeListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public Builder addChangeList(org.gof.demo.worldsrv.msg.Define.p_common_role value) {
        if (changeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChangeListIsMutable();
          changeList_.add(value);
          onChanged();
        } else {
          changeListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public Builder addChangeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role value) {
        if (changeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChangeListIsMutable();
          changeList_.add(index, value);
          onChanged();
        } else {
          changeListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public Builder addChangeList(
          org.gof.demo.worldsrv.msg.Define.p_common_role.Builder builderForValue) {
        if (changeListBuilder_ == null) {
          ensureChangeListIsMutable();
          changeList_.add(builderForValue.build());
          onChanged();
        } else {
          changeListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public Builder addChangeList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder builderForValue) {
        if (changeListBuilder_ == null) {
          ensureChangeListIsMutable();
          changeList_.add(index, builderForValue.build());
          onChanged();
        } else {
          changeListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public Builder addAllChangeList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_common_role> values) {
        if (changeListBuilder_ == null) {
          ensureChangeListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, changeList_);
          onChanged();
        } else {
          changeListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public Builder clearChangeList() {
        if (changeListBuilder_ == null) {
          changeList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          changeListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public Builder removeChangeList(int index) {
        if (changeListBuilder_ == null) {
          ensureChangeListIsMutable();
          changeList_.remove(index);
          onChanged();
        } else {
          changeListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role.Builder getChangeListBuilder(
          int index) {
        return getChangeListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder getChangeListOrBuilder(
          int index) {
        if (changeListBuilder_ == null) {
          return changeList_.get(index);  } else {
          return changeListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
           getChangeListOrBuilderList() {
        if (changeListBuilder_ != null) {
          return changeListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(changeList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role.Builder addChangeListBuilder() {
        return getChangeListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_common_role.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role.Builder addChangeListBuilder(
          int index) {
        return getChangeListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_common_role.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role change_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role.Builder> 
           getChangeListBuilderList() {
        return getChangeListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_common_role, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder, org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
          getChangeListFieldBuilder() {
        if (changeListBuilder_ == null) {
          changeListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_common_role, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder, org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder>(
                  changeList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          changeList_ = null;
        }
        return changeListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_rank_change_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_rank_change_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_rank_change_s2c>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_rank_change_s2c>() {
      @java.lang.Override
      public cross_pvp_rank_change_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_rank_change_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_rank_change_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_change_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_rank_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_rank_info_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 page = 1;</code>
     * @return The page.
     */
    int getPage();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_rank_info_c2s}
   */
  public static final class cross_pvp_rank_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_rank_info_c2s)
      cross_pvp_rank_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_rank_info_c2s.newBuilder() to construct.
    private cross_pvp_rank_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_rank_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_rank_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s.Builder.class);
    }

    public static final int PAGE_FIELD_NUMBER = 1;
    private int page_ = 0;
    /**
     * <code>int32 page = 1;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (page_ != 0) {
        output.writeInt32(1, page_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (page_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, page_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s) obj;

      if (getPage()
          != other.getPage()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + PAGE_FIELD_NUMBER;
      hash = (53 * hash) + getPage();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_rank_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_rank_info_c2s)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        page_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.page_ = page_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s.getDefaultInstance()) return this;
        if (other.getPage() != 0) {
          setPage(other.getPage());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                page_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int page_ ;
      /**
       * <code>int32 page = 1;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <code>int32 page = 1;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {

        page_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 page = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        bitField0_ = (bitField0_ & ~0x00000001);
        page_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_rank_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_rank_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_rank_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_rank_info_c2s>() {
      @java.lang.Override
      public cross_pvp_rank_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_rank_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_rank_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface cross_pvp_rank_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.cross_pvp_rank_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> 
        getRankListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_common_role getRankList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
     */
    int getRankListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
        getRankListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder getRankListOrBuilder(
        int index);

    /**
     * <code>int32 page = 2;</code>
     * @return The page.
     */
    int getPage();

    /**
     * <code>int32 max_page = 3;</code>
     * @return The maxPage.
     */
    int getMaxPage();

    /**
     * <code>int32 total_num = 4;</code>
     * @return The totalNum.
     */
    int getTotalNum();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_rank_info_s2c}
   */
  public static final class cross_pvp_rank_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.cross_pvp_rank_info_s2c)
      cross_pvp_rank_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use cross_pvp_rank_info_s2c.newBuilder() to construct.
    private cross_pvp_rank_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private cross_pvp_rank_info_s2c() {
      rankList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new cross_pvp_rank_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c.Builder.class);
    }

    public static final int RANK_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> rankList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> getRankListList() {
      return rankList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
        getRankListOrBuilderList() {
      return rankList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
     */
    @java.lang.Override
    public int getRankListCount() {
      return rankList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_common_role getRankList(int index) {
      return rankList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder getRankListOrBuilder(
        int index) {
      return rankList_.get(index);
    }

    public static final int PAGE_FIELD_NUMBER = 2;
    private int page_ = 0;
    /**
     * <code>int32 page = 2;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    public static final int MAX_PAGE_FIELD_NUMBER = 3;
    private int maxPage_ = 0;
    /**
     * <code>int32 max_page = 3;</code>
     * @return The maxPage.
     */
    @java.lang.Override
    public int getMaxPage() {
      return maxPage_;
    }

    public static final int TOTAL_NUM_FIELD_NUMBER = 4;
    private int totalNum_ = 0;
    /**
     * <code>int32 total_num = 4;</code>
     * @return The totalNum.
     */
    @java.lang.Override
    public int getTotalNum() {
      return totalNum_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < rankList_.size(); i++) {
        output.writeMessage(1, rankList_.get(i));
      }
      if (page_ != 0) {
        output.writeInt32(2, page_);
      }
      if (maxPage_ != 0) {
        output.writeInt32(3, maxPage_);
      }
      if (totalNum_ != 0) {
        output.writeInt32(4, totalNum_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < rankList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, rankList_.get(i));
      }
      if (page_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, page_);
      }
      if (maxPage_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, maxPage_);
      }
      if (totalNum_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, totalNum_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c other = (org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c) obj;

      if (!getRankListList()
          .equals(other.getRankListList())) return false;
      if (getPage()
          != other.getPage()) return false;
      if (getMaxPage()
          != other.getMaxPage()) return false;
      if (getTotalNum()
          != other.getTotalNum()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRankListCount() > 0) {
        hash = (37 * hash) + RANK_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getRankListList().hashCode();
      }
      hash = (37 * hash) + PAGE_FIELD_NUMBER;
      hash = (53 * hash) + getPage();
      hash = (37 * hash) + MAX_PAGE_FIELD_NUMBER;
      hash = (53 * hash) + getMaxPage();
      hash = (37 * hash) + TOTAL_NUM_FIELD_NUMBER;
      hash = (53 * hash) + getTotalNum();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.cross_pvp_rank_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.cross_pvp_rank_info_s2c)
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c.class, org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (rankListBuilder_ == null) {
          rankList_ = java.util.Collections.emptyList();
        } else {
          rankList_ = null;
          rankListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        page_ = 0;
        maxPage_ = 0;
        totalNum_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c result = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c result) {
        if (rankListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rankList_ = java.util.Collections.unmodifiableList(rankList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rankList_ = rankList_;
        } else {
          result.rankList_ = rankListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.page_ = page_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.maxPage_ = maxPage_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.totalNum_ = totalNum_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c.getDefaultInstance()) return this;
        if (rankListBuilder_ == null) {
          if (!other.rankList_.isEmpty()) {
            if (rankList_.isEmpty()) {
              rankList_ = other.rankList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRankListIsMutable();
              rankList_.addAll(other.rankList_);
            }
            onChanged();
          }
        } else {
          if (!other.rankList_.isEmpty()) {
            if (rankListBuilder_.isEmpty()) {
              rankListBuilder_.dispose();
              rankListBuilder_ = null;
              rankList_ = other.rankList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rankListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRankListFieldBuilder() : null;
            } else {
              rankListBuilder_.addAllMessages(other.rankList_);
            }
          }
        }
        if (other.getPage() != 0) {
          setPage(other.getPage());
        }
        if (other.getMaxPage() != 0) {
          setMaxPage(other.getMaxPage());
        }
        if (other.getTotalNum() != 0) {
          setTotalNum(other.getTotalNum());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_common_role m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_common_role.parser(),
                        extensionRegistry);
                if (rankListBuilder_ == null) {
                  ensureRankListIsMutable();
                  rankList_.add(m);
                } else {
                  rankListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              case 16: {
                page_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                maxPage_ = input.readInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                totalNum_ = input.readInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> rankList_ =
        java.util.Collections.emptyList();
      private void ensureRankListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rankList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_common_role>(rankList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_common_role, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder, org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> rankListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role> getRankListList() {
        if (rankListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rankList_);
        } else {
          return rankListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public int getRankListCount() {
        if (rankListBuilder_ == null) {
          return rankList_.size();
        } else {
          return rankListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role getRankList(int index) {
        if (rankListBuilder_ == null) {
          return rankList_.get(index);
        } else {
          return rankListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public Builder setRankList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.set(index, value);
          onChanged();
        } else {
          rankListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public Builder setRankList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.set(index, builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public Builder addRankList(org.gof.demo.worldsrv.msg.Define.p_common_role value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.add(value);
          onChanged();
        } else {
          rankListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public Builder addRankList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role value) {
        if (rankListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRankListIsMutable();
          rankList_.add(index, value);
          onChanged();
        } else {
          rankListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public Builder addRankList(
          org.gof.demo.worldsrv.msg.Define.p_common_role.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.add(builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public Builder addRankList(
          int index, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder builderForValue) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.add(index, builderForValue.build());
          onChanged();
        } else {
          rankListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public Builder addAllRankList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_common_role> values) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rankList_);
          onChanged();
        } else {
          rankListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public Builder clearRankList() {
        if (rankListBuilder_ == null) {
          rankList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rankListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public Builder removeRankList(int index) {
        if (rankListBuilder_ == null) {
          ensureRankListIsMutable();
          rankList_.remove(index);
          onChanged();
        } else {
          rankListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role.Builder getRankListBuilder(
          int index) {
        return getRankListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder getRankListOrBuilder(
          int index) {
        if (rankListBuilder_ == null) {
          return rankList_.get(index);  } else {
          return rankListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
           getRankListOrBuilderList() {
        if (rankListBuilder_ != null) {
          return rankListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rankList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role.Builder addRankListBuilder() {
        return getRankListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_common_role.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_common_role.Builder addRankListBuilder(
          int index) {
        return getRankListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_common_role.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_common_role rank_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_common_role.Builder> 
           getRankListBuilderList() {
        return getRankListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_common_role, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder, org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder> 
          getRankListFieldBuilder() {
        if (rankListBuilder_ == null) {
          rankListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_common_role, org.gof.demo.worldsrv.msg.Define.p_common_role.Builder, org.gof.demo.worldsrv.msg.Define.p_common_roleOrBuilder>(
                  rankList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rankList_ = null;
        }
        return rankListBuilder_;
      }

      private int page_ ;
      /**
       * <code>int32 page = 2;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <code>int32 page = 2;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {

        page_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int32 page = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        bitField0_ = (bitField0_ & ~0x00000002);
        page_ = 0;
        onChanged();
        return this;
      }

      private int maxPage_ ;
      /**
       * <code>int32 max_page = 3;</code>
       * @return The maxPage.
       */
      @java.lang.Override
      public int getMaxPage() {
        return maxPage_;
      }
      /**
       * <code>int32 max_page = 3;</code>
       * @param value The maxPage to set.
       * @return This builder for chaining.
       */
      public Builder setMaxPage(int value) {

        maxPage_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>int32 max_page = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxPage() {
        bitField0_ = (bitField0_ & ~0x00000004);
        maxPage_ = 0;
        onChanged();
        return this;
      }

      private int totalNum_ ;
      /**
       * <code>int32 total_num = 4;</code>
       * @return The totalNum.
       */
      @java.lang.Override
      public int getTotalNum() {
        return totalNum_;
      }
      /**
       * <code>int32 total_num = 4;</code>
       * @param value The totalNum to set.
       * @return This builder for chaining.
       */
      public Builder setTotalNum(int value) {

        totalNum_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>int32 total_num = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalNum() {
        bitField0_ = (bitField0_ & ~0x00000008);
        totalNum_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.cross_pvp_rank_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.cross_pvp_rank_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<cross_pvp_rank_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<cross_pvp_rank_info_s2c>() {
      @java.lang.Override
      public cross_pvp_rank_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<cross_pvp_rank_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<cross_pvp_rank_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgCrossPvp.cross_pvp_rank_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_stage_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_stage_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_change_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_change_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\022msg.crossPvp.proto\022\031org.gof.demo.world" +
      "srv.msg\032\roptions.proto\032\014define.proto\"\033\n\022" +
      "cross_pvp_info_c2s:\005\210\303\032\201V\"\344\002\n\022cross_pvp_" +
      "info_s2c\022\014\n\004type\030\001 \001(\005\022\020\n\010end_time\030\002 \001(\r" +
      "\022\013\n\003dan\030\003 \001(\r\022\017\n\007win_num\030\004 \001(\r\022\022\n\ncombat" +
      "_num\030\005 \001(\r\022\020\n\010my_score\030\006 \001(\r\022\017\n\007my_rank\030" +
      "\007 \001(\r\022\017\n\007is_sign\030\010 \001(\r\022<\n\nenemy_list\030\t \003" +
      "(\0132(.org.gof.demo.worldsrv.msg.p_common_" +
      "role\022;\n\trank_list\030\n \003(\0132(.org.gof.demo.w" +
      "orldsrv.msg.p_common_role\0223\n\003ext\030\013 \003(\0132&" +
      ".org.gof.demo.worldsrv.msg.p_key_value\022\021" +
      "\n\tbuy_times\030\014 \001(\005:\005\210\303\032\201V\"*\n\024cross_pvp_co" +
      "mbat_c2s\022\013\n\003eid\030\001 \001(\003:\005\210\303\032\202V\"\276\001\n\024cross_p" +
      "vp_combat_s2c\022\014\n\004code\030\001 \001(\r\022\013\n\003eid\030\002 \001(\004" +
      "\022\014\n\004seed\030\003 \001(\004\022:\n\010atk_data\030\004 \001(\0132(.org.g" +
      "of.demo.worldsrv.msg.p_battle_role\022:\n\010de" +
      "f_data\030\005 \001(\0132(.org.gof.demo.worldsrv.msg" +
      ".p_battle_role:\005\210\303\032\202V\"*\n\024cross_pvp_resul" +
      "t_c2s\022\013\n\003wid\030\001 \001(\004:\005\210\303\032\203V\"\345\001\n\024cross_pvp_" +
      "result_s2c\022\016\n\006is_win\030\001 \001(\005\022\020\n\010my_score\030\002" +
      " \001(\005\022\017\n\007my_rank\030\003 \001(\r\022\027\n\017my_score_change" +
      "\030\004 \001(\005\022\016\n\006e_name\030\005 \001(\t\022\016\n\006e_rank\030\006 \001(\r\022\017" +
      "\n\007e_score\030\007 \001(\r\022\026\n\016e_score_change\030\010 \001(\005\022" +
      "1\n\006e_head\030\t \001(\0132!.org.gof.demo.worldsrv." +
      "msg.p_head:\005\210\303\032\203V\"\036\n\025cross_pvp_history_c" +
      "2s:\005\210\303\032\204V\"`\n\025cross_pvp_history_s2c\022@\n\014hi" +
      "story_list\030\001 \003(\0132*.org.gof.demo.worldsrv" +
      ".msg.p_arena_history:\005\210\303\032\204V\">\n\030cross_pvp" +
      "_video_play_c2s\022\013\n\003vid\030\001 \001(\004\022\016\n\006source\030\002" +
      " \001(\r:\005\210\303\032\205V\"\367\001\n\030cross_pvp_video_play_s2c" +
      "\022\014\n\004code\030\001 \001(\005\022\013\n\003vid\030\002 \001(\004\022\014\n\004seed\030\003 \001(" +
      "\004\022:\n\010atk_data\030\004 \001(\0132(.org.gof.demo.world" +
      "srv.msg.p_battle_role\022:\n\010def_data\030\005 \001(\0132" +
      "(.org.gof.demo.worldsrv.msg.p_battle_rol" +
      "e\022\016\n\006source\030\006 \001(\r\022\020\n\010collects\030\007 \001(\t\022\021\n\tw" +
      "inner_id\030\010 \001(\004:\005\210\303\032\205V\"\036\n\025cross_pvp_sign_" +
      "up_c2s:\005\210\303\032\206V\"\036\n\025cross_pvp_sign_up_s2c:\005" +
      "\210\303\032\206V\"=\n\023cross_pvp_stage_s2c\022\r\n\005stage\030\001 " +
      "\001(\r\022\020\n\010end_time\030\002 \001(\r:\005\210\303\032\207V\"a\n\031cross_pv" +
      "p_rank_change_s2c\022=\n\013change_list\030\001 \003(\0132(" +
      ".org.gof.demo.worldsrv.msg.p_common_role" +
      ":\005\210\303\032\210V\".\n\027cross_pvp_rank_info_c2s\022\014\n\004pa" +
      "ge\030\001 \001(\005:\005\210\303\032\211V\"\220\001\n\027cross_pvp_rank_info_" +
      "s2c\022;\n\trank_list\030\001 \003(\0132(.org.gof.demo.wo" +
      "rldsrv.msg.p_common_role\022\014\n\004page\030\002 \001(\005\022\020" +
      "\n\010max_page\030\003 \001(\005\022\021\n\ttotal_num\030\004 \001(\005:\005\210\303\032" +
      "\211Vb\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_info_s2c_descriptor,
        new java.lang.String[] { "Type", "EndTime", "Dan", "WinNum", "CombatNum", "MyScore", "MyRank", "IsSign", "EnemyList", "RankList", "Ext", "BuyTimes", });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_c2s_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_c2s_descriptor,
        new java.lang.String[] { "Eid", });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_combat_s2c_descriptor,
        new java.lang.String[] { "Code", "Eid", "Seed", "AtkData", "DefData", });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_c2s_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_c2s_descriptor,
        new java.lang.String[] { "Wid", });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_s2c_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_result_s2c_descriptor,
        new java.lang.String[] { "IsWin", "MyScore", "MyRank", "MyScoreChange", "EName", "ERank", "EScore", "EScoreChange", "EHead", });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_c2s_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_s2c_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_history_s2c_descriptor,
        new java.lang.String[] { "HistoryList", });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_c2s_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_c2s_descriptor,
        new java.lang.String[] { "Vid", "Source", });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_s2c_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_video_play_s2c_descriptor,
        new java.lang.String[] { "Code", "Vid", "Seed", "AtkData", "DefData", "Source", "Collects", "WinnerId", });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_c2s_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_s2c_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_sign_up_s2c_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_stage_s2c_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_stage_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_stage_s2c_descriptor,
        new java.lang.String[] { "Stage", "EndTime", });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_change_s2c_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_change_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_change_s2c_descriptor,
        new java.lang.String[] { "ChangeList", });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_c2s_descriptor,
        new java.lang.String[] { "Page", });
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_cross_pvp_rank_info_s2c_descriptor,
        new java.lang.String[] { "RankList", "Page", "MaxPage", "TotalNum", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
