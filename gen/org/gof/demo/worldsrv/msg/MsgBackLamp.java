// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.BackLamp.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgBackLamp {
  private MsgBackLamp() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface back_lamp_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.back_lamp_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_info_c2s}
   */
  public static final class back_lamp_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.back_lamp_info_c2s)
      back_lamp_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use back_lamp_info_c2s.newBuilder() to construct.
    private back_lamp_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private back_lamp_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new back_lamp_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s other = (org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.back_lamp_info_c2s)
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s result = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.back_lamp_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.back_lamp_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<back_lamp_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<back_lamp_info_c2s>() {
      @java.lang.Override
      public back_lamp_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<back_lamp_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<back_lamp_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface back_lamp_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.back_lamp_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     *章节是否完成
     * </pre>
     *
     * <code>repeated uint32 finish_ids = 1 [packed = false];</code>
     * @return A list containing the finishIds.
     */
    java.util.List<java.lang.Integer> getFinishIdsList();
    /**
     * <pre>
     *章节是否完成
     * </pre>
     *
     * <code>repeated uint32 finish_ids = 1 [packed = false];</code>
     * @return The count of finishIds.
     */
    int getFinishIdsCount();
    /**
     * <pre>
     *章节是否完成
     * </pre>
     *
     * <code>repeated uint32 finish_ids = 1 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The finishIds at the given index.
     */
    int getFinishIds(int index);

    /**
     * <pre>
     *章节是否已领取奖励
     * </pre>
     *
     * <code>repeated uint32 got_ids = 2 [packed = false];</code>
     * @return A list containing the gotIds.
     */
    java.util.List<java.lang.Integer> getGotIdsList();
    /**
     * <pre>
     *章节是否已领取奖励
     * </pre>
     *
     * <code>repeated uint32 got_ids = 2 [packed = false];</code>
     * @return The count of gotIds.
     */
    int getGotIdsCount();
    /**
     * <pre>
     *章节是否已领取奖励
     * </pre>
     *
     * <code>repeated uint32 got_ids = 2 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The gotIds at the given index.
     */
    int getGotIds(int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_info_s2c}
   */
  public static final class back_lamp_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.back_lamp_info_s2c)
      back_lamp_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use back_lamp_info_s2c.newBuilder() to construct.
    private back_lamp_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private back_lamp_info_s2c() {
      finishIds_ = emptyIntList();
      gotIds_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new back_lamp_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c.Builder.class);
    }

    public static final int FINISH_IDS_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList finishIds_ =
        emptyIntList();
    /**
     * <pre>
     *章节是否完成
     * </pre>
     *
     * <code>repeated uint32 finish_ids = 1 [packed = false];</code>
     * @return A list containing the finishIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getFinishIdsList() {
      return finishIds_;
    }
    /**
     * <pre>
     *章节是否完成
     * </pre>
     *
     * <code>repeated uint32 finish_ids = 1 [packed = false];</code>
     * @return The count of finishIds.
     */
    public int getFinishIdsCount() {
      return finishIds_.size();
    }
    /**
     * <pre>
     *章节是否完成
     * </pre>
     *
     * <code>repeated uint32 finish_ids = 1 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The finishIds at the given index.
     */
    public int getFinishIds(int index) {
      return finishIds_.getInt(index);
    }

    public static final int GOT_IDS_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList gotIds_ =
        emptyIntList();
    /**
     * <pre>
     *章节是否已领取奖励
     * </pre>
     *
     * <code>repeated uint32 got_ids = 2 [packed = false];</code>
     * @return A list containing the gotIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getGotIdsList() {
      return gotIds_;
    }
    /**
     * <pre>
     *章节是否已领取奖励
     * </pre>
     *
     * <code>repeated uint32 got_ids = 2 [packed = false];</code>
     * @return The count of gotIds.
     */
    public int getGotIdsCount() {
      return gotIds_.size();
    }
    /**
     * <pre>
     *章节是否已领取奖励
     * </pre>
     *
     * <code>repeated uint32 got_ids = 2 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The gotIds at the given index.
     */
    public int getGotIds(int index) {
      return gotIds_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < finishIds_.size(); i++) {
        output.writeUInt32(1, finishIds_.getInt(i));
      }
      for (int i = 0; i < gotIds_.size(); i++) {
        output.writeUInt32(2, gotIds_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < finishIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(finishIds_.getInt(i));
        }
        size += dataSize;
        size += 1 * getFinishIdsList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < gotIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(gotIds_.getInt(i));
        }
        size += dataSize;
        size += 1 * getGotIdsList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c other = (org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c) obj;

      if (!getFinishIdsList()
          .equals(other.getFinishIdsList())) return false;
      if (!getGotIdsList()
          .equals(other.getGotIdsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getFinishIdsCount() > 0) {
        hash = (37 * hash) + FINISH_IDS_FIELD_NUMBER;
        hash = (53 * hash) + getFinishIdsList().hashCode();
      }
      if (getGotIdsCount() > 0) {
        hash = (37 * hash) + GOT_IDS_FIELD_NUMBER;
        hash = (53 * hash) + getGotIdsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.back_lamp_info_s2c)
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        finishIds_ = emptyIntList();
        gotIds_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c result = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          finishIds_.makeImmutable();
          result.finishIds_ = finishIds_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          gotIds_.makeImmutable();
          result.gotIds_ = gotIds_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c.getDefaultInstance()) return this;
        if (!other.finishIds_.isEmpty()) {
          if (finishIds_.isEmpty()) {
            finishIds_ = other.finishIds_;
            finishIds_.makeImmutable();
            bitField0_ |= 0x00000001;
          } else {
            ensureFinishIdsIsMutable();
            finishIds_.addAll(other.finishIds_);
          }
          onChanged();
        }
        if (!other.gotIds_.isEmpty()) {
          if (gotIds_.isEmpty()) {
            gotIds_ = other.gotIds_;
            gotIds_.makeImmutable();
            bitField0_ |= 0x00000002;
          } else {
            ensureGotIdsIsMutable();
            gotIds_.addAll(other.gotIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                int v = input.readUInt32();
                ensureFinishIdsIsMutable();
                finishIds_.addInt(v);
                break;
              } // case 8
              case 10: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureFinishIdsIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  finishIds_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 10
              case 16: {
                int v = input.readUInt32();
                ensureGotIdsIsMutable();
                gotIds_.addInt(v);
                break;
              } // case 16
              case 18: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureGotIdsIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  gotIds_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList finishIds_ = emptyIntList();
      private void ensureFinishIdsIsMutable() {
        if (!finishIds_.isModifiable()) {
          finishIds_ = makeMutableCopy(finishIds_);
        }
        bitField0_ |= 0x00000001;
      }
      /**
       * <pre>
       *章节是否完成
       * </pre>
       *
       * <code>repeated uint32 finish_ids = 1 [packed = false];</code>
       * @return A list containing the finishIds.
       */
      public java.util.List<java.lang.Integer>
          getFinishIdsList() {
        finishIds_.makeImmutable();
        return finishIds_;
      }
      /**
       * <pre>
       *章节是否完成
       * </pre>
       *
       * <code>repeated uint32 finish_ids = 1 [packed = false];</code>
       * @return The count of finishIds.
       */
      public int getFinishIdsCount() {
        return finishIds_.size();
      }
      /**
       * <pre>
       *章节是否完成
       * </pre>
       *
       * <code>repeated uint32 finish_ids = 1 [packed = false];</code>
       * @param index The index of the element to return.
       * @return The finishIds at the given index.
       */
      public int getFinishIds(int index) {
        return finishIds_.getInt(index);
      }
      /**
       * <pre>
       *章节是否完成
       * </pre>
       *
       * <code>repeated uint32 finish_ids = 1 [packed = false];</code>
       * @param index The index to set the value at.
       * @param value The finishIds to set.
       * @return This builder for chaining.
       */
      public Builder setFinishIds(
          int index, int value) {

        ensureFinishIdsIsMutable();
        finishIds_.setInt(index, value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *章节是否完成
       * </pre>
       *
       * <code>repeated uint32 finish_ids = 1 [packed = false];</code>
       * @param value The finishIds to add.
       * @return This builder for chaining.
       */
      public Builder addFinishIds(int value) {

        ensureFinishIdsIsMutable();
        finishIds_.addInt(value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *章节是否完成
       * </pre>
       *
       * <code>repeated uint32 finish_ids = 1 [packed = false];</code>
       * @param values The finishIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllFinishIds(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureFinishIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, finishIds_);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *章节是否完成
       * </pre>
       *
       * <code>repeated uint32 finish_ids = 1 [packed = false];</code>
       * @return This builder for chaining.
       */
      public Builder clearFinishIds() {
        finishIds_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList gotIds_ = emptyIntList();
      private void ensureGotIdsIsMutable() {
        if (!gotIds_.isModifiable()) {
          gotIds_ = makeMutableCopy(gotIds_);
        }
        bitField0_ |= 0x00000002;
      }
      /**
       * <pre>
       *章节是否已领取奖励
       * </pre>
       *
       * <code>repeated uint32 got_ids = 2 [packed = false];</code>
       * @return A list containing the gotIds.
       */
      public java.util.List<java.lang.Integer>
          getGotIdsList() {
        gotIds_.makeImmutable();
        return gotIds_;
      }
      /**
       * <pre>
       *章节是否已领取奖励
       * </pre>
       *
       * <code>repeated uint32 got_ids = 2 [packed = false];</code>
       * @return The count of gotIds.
       */
      public int getGotIdsCount() {
        return gotIds_.size();
      }
      /**
       * <pre>
       *章节是否已领取奖励
       * </pre>
       *
       * <code>repeated uint32 got_ids = 2 [packed = false];</code>
       * @param index The index of the element to return.
       * @return The gotIds at the given index.
       */
      public int getGotIds(int index) {
        return gotIds_.getInt(index);
      }
      /**
       * <pre>
       *章节是否已领取奖励
       * </pre>
       *
       * <code>repeated uint32 got_ids = 2 [packed = false];</code>
       * @param index The index to set the value at.
       * @param value The gotIds to set.
       * @return This builder for chaining.
       */
      public Builder setGotIds(
          int index, int value) {

        ensureGotIdsIsMutable();
        gotIds_.setInt(index, value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *章节是否已领取奖励
       * </pre>
       *
       * <code>repeated uint32 got_ids = 2 [packed = false];</code>
       * @param value The gotIds to add.
       * @return This builder for chaining.
       */
      public Builder addGotIds(int value) {

        ensureGotIdsIsMutable();
        gotIds_.addInt(value);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *章节是否已领取奖励
       * </pre>
       *
       * <code>repeated uint32 got_ids = 2 [packed = false];</code>
       * @param values The gotIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllGotIds(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureGotIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, gotIds_);
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *章节是否已领取奖励
       * </pre>
       *
       * <code>repeated uint32 got_ids = 2 [packed = false];</code>
       * @return This builder for chaining.
       */
      public Builder clearGotIds() {
        gotIds_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.back_lamp_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.back_lamp_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<back_lamp_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<back_lamp_info_s2c>() {
      @java.lang.Override
      public back_lamp_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<back_lamp_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<back_lamp_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface back_lamp_chapter_start_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.back_lamp_chapter_start_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 发送开始章节请求 BackLampChapter的sn
     * </pre>
     *
     * <code>uint32 chapter_id = 1;</code>
     * @return The chapterId.
     */
    int getChapterId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_chapter_start_c2s}
   */
  public static final class back_lamp_chapter_start_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.back_lamp_chapter_start_c2s)
      back_lamp_chapter_start_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use back_lamp_chapter_start_c2s.newBuilder() to construct.
    private back_lamp_chapter_start_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private back_lamp_chapter_start_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new back_lamp_chapter_start_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s.Builder.class);
    }

    public static final int CHAPTER_ID_FIELD_NUMBER = 1;
    private int chapterId_ = 0;
    /**
     * <pre>
     * 发送开始章节请求 BackLampChapter的sn
     * </pre>
     *
     * <code>uint32 chapter_id = 1;</code>
     * @return The chapterId.
     */
    @java.lang.Override
    public int getChapterId() {
      return chapterId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (chapterId_ != 0) {
        output.writeUInt32(1, chapterId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (chapterId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, chapterId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s other = (org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s) obj;

      if (getChapterId()
          != other.getChapterId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHAPTER_ID_FIELD_NUMBER;
      hash = (53 * hash) + getChapterId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_chapter_start_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.back_lamp_chapter_start_c2s)
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        chapterId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s build() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s result = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chapterId_ = chapterId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s.getDefaultInstance()) return this;
        if (other.getChapterId() != 0) {
          setChapterId(other.getChapterId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                chapterId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int chapterId_ ;
      /**
       * <pre>
       * 发送开始章节请求 BackLampChapter的sn
       * </pre>
       *
       * <code>uint32 chapter_id = 1;</code>
       * @return The chapterId.
       */
      @java.lang.Override
      public int getChapterId() {
        return chapterId_;
      }
      /**
       * <pre>
       * 发送开始章节请求 BackLampChapter的sn
       * </pre>
       *
       * <code>uint32 chapter_id = 1;</code>
       * @param value The chapterId to set.
       * @return This builder for chaining.
       */
      public Builder setChapterId(int value) {

        chapterId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 发送开始章节请求 BackLampChapter的sn
       * </pre>
       *
       * <code>uint32 chapter_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChapterId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chapterId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.back_lamp_chapter_start_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.back_lamp_chapter_start_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<back_lamp_chapter_start_c2s>
        PARSER = new com.google.protobuf.AbstractParser<back_lamp_chapter_start_c2s>() {
      @java.lang.Override
      public back_lamp_chapter_start_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<back_lamp_chapter_start_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<back_lamp_chapter_start_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface back_lamp_chapter_start_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.back_lamp_chapter_start_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 chapter_id = 1;</code>
     * @return The chapterId.
     */
    int getChapterId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_chapter_start_s2c}
   */
  public static final class back_lamp_chapter_start_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.back_lamp_chapter_start_s2c)
      back_lamp_chapter_start_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use back_lamp_chapter_start_s2c.newBuilder() to construct.
    private back_lamp_chapter_start_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private back_lamp_chapter_start_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new back_lamp_chapter_start_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c.Builder.class);
    }

    public static final int CHAPTER_ID_FIELD_NUMBER = 1;
    private int chapterId_ = 0;
    /**
     * <code>uint32 chapter_id = 1;</code>
     * @return The chapterId.
     */
    @java.lang.Override
    public int getChapterId() {
      return chapterId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (chapterId_ != 0) {
        output.writeUInt32(1, chapterId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (chapterId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, chapterId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c other = (org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c) obj;

      if (getChapterId()
          != other.getChapterId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHAPTER_ID_FIELD_NUMBER;
      hash = (53 * hash) + getChapterId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_chapter_start_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.back_lamp_chapter_start_s2c)
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        chapterId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c build() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c result = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chapterId_ = chapterId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c.getDefaultInstance()) return this;
        if (other.getChapterId() != 0) {
          setChapterId(other.getChapterId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                chapterId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int chapterId_ ;
      /**
       * <code>uint32 chapter_id = 1;</code>
       * @return The chapterId.
       */
      @java.lang.Override
      public int getChapterId() {
        return chapterId_;
      }
      /**
       * <code>uint32 chapter_id = 1;</code>
       * @param value The chapterId to set.
       * @return This builder for chaining.
       */
      public Builder setChapterId(int value) {

        chapterId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 chapter_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChapterId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chapterId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.back_lamp_chapter_start_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.back_lamp_chapter_start_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<back_lamp_chapter_start_s2c>
        PARSER = new com.google.protobuf.AbstractParser<back_lamp_chapter_start_s2c>() {
      @java.lang.Override
      public back_lamp_chapter_start_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<back_lamp_chapter_start_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<back_lamp_chapter_start_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_start_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface back_lamp_chapter_result_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.back_lamp_chapter_result_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 发送章节结果请求 BackLampChapter的sn
     * </pre>
     *
     * <code>uint32 chapter_id = 1;</code>
     * @return The chapterId.
     */
    int getChapterId();

    /**
     * <pre>
     *结果失败与成功   0与1
     * </pre>
     *
     * <code>uint32 result = 2;</code>
     * @return The result.
     */
    int getResult();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_chapter_result_c2s}
   */
  public static final class back_lamp_chapter_result_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.back_lamp_chapter_result_c2s)
      back_lamp_chapter_result_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use back_lamp_chapter_result_c2s.newBuilder() to construct.
    private back_lamp_chapter_result_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private back_lamp_chapter_result_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new back_lamp_chapter_result_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s.Builder.class);
    }

    public static final int CHAPTER_ID_FIELD_NUMBER = 1;
    private int chapterId_ = 0;
    /**
     * <pre>
     * 发送章节结果请求 BackLampChapter的sn
     * </pre>
     *
     * <code>uint32 chapter_id = 1;</code>
     * @return The chapterId.
     */
    @java.lang.Override
    public int getChapterId() {
      return chapterId_;
    }

    public static final int RESULT_FIELD_NUMBER = 2;
    private int result_ = 0;
    /**
     * <pre>
     *结果失败与成功   0与1
     * </pre>
     *
     * <code>uint32 result = 2;</code>
     * @return The result.
     */
    @java.lang.Override
    public int getResult() {
      return result_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (chapterId_ != 0) {
        output.writeUInt32(1, chapterId_);
      }
      if (result_ != 0) {
        output.writeUInt32(2, result_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (chapterId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, chapterId_);
      }
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, result_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s other = (org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s) obj;

      if (getChapterId()
          != other.getChapterId()) return false;
      if (getResult()
          != other.getResult()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHAPTER_ID_FIELD_NUMBER;
      hash = (53 * hash) + getChapterId();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_chapter_result_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.back_lamp_chapter_result_c2s)
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        chapterId_ = 0;
        result_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s build() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s result = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chapterId_ = chapterId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.result_ = result_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s.getDefaultInstance()) return this;
        if (other.getChapterId() != 0) {
          setChapterId(other.getChapterId());
        }
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                chapterId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                result_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int chapterId_ ;
      /**
       * <pre>
       * 发送章节结果请求 BackLampChapter的sn
       * </pre>
       *
       * <code>uint32 chapter_id = 1;</code>
       * @return The chapterId.
       */
      @java.lang.Override
      public int getChapterId() {
        return chapterId_;
      }
      /**
       * <pre>
       * 发送章节结果请求 BackLampChapter的sn
       * </pre>
       *
       * <code>uint32 chapter_id = 1;</code>
       * @param value The chapterId to set.
       * @return This builder for chaining.
       */
      public Builder setChapterId(int value) {

        chapterId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 发送章节结果请求 BackLampChapter的sn
       * </pre>
       *
       * <code>uint32 chapter_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChapterId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chapterId_ = 0;
        onChanged();
        return this;
      }

      private int result_ ;
      /**
       * <pre>
       *结果失败与成功   0与1
       * </pre>
       *
       * <code>uint32 result = 2;</code>
       * @return The result.
       */
      @java.lang.Override
      public int getResult() {
        return result_;
      }
      /**
       * <pre>
       *结果失败与成功   0与1
       * </pre>
       *
       * <code>uint32 result = 2;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(int value) {

        result_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <pre>
       *结果失败与成功   0与1
       * </pre>
       *
       * <code>uint32 result = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000002);
        result_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.back_lamp_chapter_result_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.back_lamp_chapter_result_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<back_lamp_chapter_result_c2s>
        PARSER = new com.google.protobuf.AbstractParser<back_lamp_chapter_result_c2s>() {
      @java.lang.Override
      public back_lamp_chapter_result_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<back_lamp_chapter_result_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<back_lamp_chapter_result_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface back_lamp_chapter_result_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.back_lamp_chapter_result_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 chapter_id = 1;</code>
     * @return The chapterId.
     */
    int getChapterId();

    /**
     * <code>uint32 result = 2;</code>
     * @return The result.
     */
    int getResult();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_chapter_result_s2c}
   */
  public static final class back_lamp_chapter_result_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.back_lamp_chapter_result_s2c)
      back_lamp_chapter_result_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use back_lamp_chapter_result_s2c.newBuilder() to construct.
    private back_lamp_chapter_result_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private back_lamp_chapter_result_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new back_lamp_chapter_result_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c.Builder.class);
    }

    public static final int CHAPTER_ID_FIELD_NUMBER = 1;
    private int chapterId_ = 0;
    /**
     * <code>uint32 chapter_id = 1;</code>
     * @return The chapterId.
     */
    @java.lang.Override
    public int getChapterId() {
      return chapterId_;
    }

    public static final int RESULT_FIELD_NUMBER = 2;
    private int result_ = 0;
    /**
     * <code>uint32 result = 2;</code>
     * @return The result.
     */
    @java.lang.Override
    public int getResult() {
      return result_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (chapterId_ != 0) {
        output.writeUInt32(1, chapterId_);
      }
      if (result_ != 0) {
        output.writeUInt32(2, result_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (chapterId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, chapterId_);
      }
      if (result_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, result_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c other = (org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c) obj;

      if (getChapterId()
          != other.getChapterId()) return false;
      if (getResult()
          != other.getResult()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHAPTER_ID_FIELD_NUMBER;
      hash = (53 * hash) + getChapterId();
      hash = (37 * hash) + RESULT_FIELD_NUMBER;
      hash = (53 * hash) + getResult();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_chapter_result_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.back_lamp_chapter_result_s2c)
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        chapterId_ = 0;
        result_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c build() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c result = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chapterId_ = chapterId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.result_ = result_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c.getDefaultInstance()) return this;
        if (other.getChapterId() != 0) {
          setChapterId(other.getChapterId());
        }
        if (other.getResult() != 0) {
          setResult(other.getResult());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                chapterId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                result_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int chapterId_ ;
      /**
       * <code>uint32 chapter_id = 1;</code>
       * @return The chapterId.
       */
      @java.lang.Override
      public int getChapterId() {
        return chapterId_;
      }
      /**
       * <code>uint32 chapter_id = 1;</code>
       * @param value The chapterId to set.
       * @return This builder for chaining.
       */
      public Builder setChapterId(int value) {

        chapterId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 chapter_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChapterId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chapterId_ = 0;
        onChanged();
        return this;
      }

      private int result_ ;
      /**
       * <code>uint32 result = 2;</code>
       * @return The result.
       */
      @java.lang.Override
      public int getResult() {
        return result_;
      }
      /**
       * <code>uint32 result = 2;</code>
       * @param value The result to set.
       * @return This builder for chaining.
       */
      public Builder setResult(int value) {

        result_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 result = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearResult() {
        bitField0_ = (bitField0_ & ~0x00000002);
        result_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.back_lamp_chapter_result_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.back_lamp_chapter_result_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<back_lamp_chapter_result_s2c>
        PARSER = new com.google.protobuf.AbstractParser<back_lamp_chapter_result_s2c>() {
      @java.lang.Override
      public back_lamp_chapter_result_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<back_lamp_chapter_result_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<back_lamp_chapter_result_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_chapter_result_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface back_lamp_get_acc_reward_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 发送领取累计奖励请求 应该也是sn
     * </pre>
     *
     * <code>uint32 chapter_id = 1;</code>
     * @return The chapterId.
     */
    int getChapterId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_c2s}
   */
  public static final class back_lamp_get_acc_reward_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_c2s)
      back_lamp_get_acc_reward_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use back_lamp_get_acc_reward_c2s.newBuilder() to construct.
    private back_lamp_get_acc_reward_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private back_lamp_get_acc_reward_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new back_lamp_get_acc_reward_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s.Builder.class);
    }

    public static final int CHAPTER_ID_FIELD_NUMBER = 1;
    private int chapterId_ = 0;
    /**
     * <pre>
     * 发送领取累计奖励请求 应该也是sn
     * </pre>
     *
     * <code>uint32 chapter_id = 1;</code>
     * @return The chapterId.
     */
    @java.lang.Override
    public int getChapterId() {
      return chapterId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (chapterId_ != 0) {
        output.writeUInt32(1, chapterId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (chapterId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, chapterId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s other = (org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s) obj;

      if (getChapterId()
          != other.getChapterId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHAPTER_ID_FIELD_NUMBER;
      hash = (53 * hash) + getChapterId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_c2s)
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        chapterId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s build() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s result = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chapterId_ = chapterId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s.getDefaultInstance()) return this;
        if (other.getChapterId() != 0) {
          setChapterId(other.getChapterId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                chapterId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int chapterId_ ;
      /**
       * <pre>
       * 发送领取累计奖励请求 应该也是sn
       * </pre>
       *
       * <code>uint32 chapter_id = 1;</code>
       * @return The chapterId.
       */
      @java.lang.Override
      public int getChapterId() {
        return chapterId_;
      }
      /**
       * <pre>
       * 发送领取累计奖励请求 应该也是sn
       * </pre>
       *
       * <code>uint32 chapter_id = 1;</code>
       * @param value The chapterId to set.
       * @return This builder for chaining.
       */
      public Builder setChapterId(int value) {

        chapterId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 发送领取累计奖励请求 应该也是sn
       * </pre>
       *
       * <code>uint32 chapter_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChapterId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chapterId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<back_lamp_get_acc_reward_c2s>
        PARSER = new com.google.protobuf.AbstractParser<back_lamp_get_acc_reward_c2s>() {
      @java.lang.Override
      public back_lamp_get_acc_reward_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<back_lamp_get_acc_reward_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<back_lamp_get_acc_reward_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface back_lamp_get_acc_reward_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 chapter_id = 1;</code>
     * @return The chapterId.
     */
    int getChapterId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_s2c}
   */
  public static final class back_lamp_get_acc_reward_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_s2c)
      back_lamp_get_acc_reward_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use back_lamp_get_acc_reward_s2c.newBuilder() to construct.
    private back_lamp_get_acc_reward_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private back_lamp_get_acc_reward_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new back_lamp_get_acc_reward_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c.Builder.class);
    }

    public static final int CHAPTER_ID_FIELD_NUMBER = 1;
    private int chapterId_ = 0;
    /**
     * <code>uint32 chapter_id = 1;</code>
     * @return The chapterId.
     */
    @java.lang.Override
    public int getChapterId() {
      return chapterId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (chapterId_ != 0) {
        output.writeUInt32(1, chapterId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (chapterId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, chapterId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c other = (org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c) obj;

      if (getChapterId()
          != other.getChapterId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHAPTER_ID_FIELD_NUMBER;
      hash = (53 * hash) + getChapterId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_s2c)
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c.class, org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        chapterId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c build() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c result = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.chapterId_ = chapterId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c.getDefaultInstance()) return this;
        if (other.getChapterId() != 0) {
          setChapterId(other.getChapterId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                chapterId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int chapterId_ ;
      /**
       * <code>uint32 chapter_id = 1;</code>
       * @return The chapterId.
       */
      @java.lang.Override
      public int getChapterId() {
        return chapterId_;
      }
      /**
       * <code>uint32 chapter_id = 1;</code>
       * @param value The chapterId to set.
       * @return This builder for chaining.
       */
      public Builder setChapterId(int value) {

        chapterId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 chapter_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChapterId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chapterId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.back_lamp_get_acc_reward_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<back_lamp_get_acc_reward_s2c>
        PARSER = new com.google.protobuf.AbstractParser<back_lamp_get_acc_reward_s2c>() {
      @java.lang.Override
      public back_lamp_get_acc_reward_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<back_lamp_get_acc_reward_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<back_lamp_get_acc_reward_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgBackLamp.back_lamp_get_acc_reward_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\022msg.BackLamp.proto\022\031org.gof.demo.world" +
      "srv.msg\032\roptions.proto\032\014define.proto\"\034\n\022" +
      "back_lamp_info_c2s:\006\210\303\032\201\206\001\"I\n\022back_lamp_" +
      "info_s2c\022\026\n\nfinish_ids\030\001 \003(\rB\002\020\000\022\023\n\007got_" +
      "ids\030\002 \003(\rB\002\020\000:\006\210\303\032\201\206\001\"9\n\033back_lamp_chapt" +
      "er_start_c2s\022\022\n\nchapter_id\030\001 \001(\r:\006\210\303\032\202\206\001" +
      "\"9\n\033back_lamp_chapter_start_s2c\022\022\n\nchapt" +
      "er_id\030\001 \001(\r:\006\210\303\032\202\206\001\"J\n\034back_lamp_chapter" +
      "_result_c2s\022\022\n\nchapter_id\030\001 \001(\r\022\016\n\006resul" +
      "t\030\002 \001(\r:\006\210\303\032\203\206\001\"J\n\034back_lamp_chapter_res" +
      "ult_s2c\022\022\n\nchapter_id\030\001 \001(\r\022\016\n\006result\030\002 " +
      "\001(\r:\006\210\303\032\203\206\001\":\n\034back_lamp_get_acc_reward_" +
      "c2s\022\022\n\nchapter_id\030\001 \001(\r:\006\210\303\032\204\206\001\":\n\034back_" +
      "lamp_get_acc_reward_s2c\022\022\n\nchapter_id\030\001 " +
      "\001(\r:\006\210\303\032\204\206\001b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_back_lamp_info_s2c_descriptor,
        new java.lang.String[] { "FinishIds", "GotIds", });
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_c2s_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_c2s_descriptor,
        new java.lang.String[] { "ChapterId", });
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_start_s2c_descriptor,
        new java.lang.String[] { "ChapterId", });
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_c2s_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_c2s_descriptor,
        new java.lang.String[] { "ChapterId", "Result", });
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_s2c_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_back_lamp_chapter_result_s2c_descriptor,
        new java.lang.String[] { "ChapterId", "Result", });
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_c2s_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_c2s_descriptor,
        new java.lang.String[] { "ChapterId", });
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_s2c_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_back_lamp_get_acc_reward_s2c_descriptor,
        new java.lang.String[] { "ChapterId", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
