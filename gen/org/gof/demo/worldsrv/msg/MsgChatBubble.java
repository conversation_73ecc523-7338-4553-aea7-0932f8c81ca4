// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.chatBubble.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgChatBubble {
  private MsgChatBubble() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface chat_bubble_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_bubble_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_bubble_info_c2s}
   */
  public static final class chat_bubble_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_bubble_info_c2s)
      chat_bubble_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_bubble_info_c2s.newBuilder() to construct.
    private chat_bubble_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_bubble_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_bubble_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s.class, org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s other = (org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_bubble_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_bubble_info_c2s)
        org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s.class, org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s result = new org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_bubble_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_bubble_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_bubble_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<chat_bubble_info_c2s>() {
      @java.lang.Override
      public chat_bubble_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_bubble_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_bubble_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface chat_bubble_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_bubble_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 wear_id = 1;</code>
     * @return The wearId.
     */
    int getWearId();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_bubble> 
        getBubbleListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_chat_bubble getBubbleList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
     */
    int getBubbleListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder> 
        getBubbleListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder getBubbleListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_bubble_info_s2c}
   */
  public static final class chat_bubble_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_bubble_info_s2c)
      chat_bubble_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_bubble_info_s2c.newBuilder() to construct.
    private chat_bubble_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_bubble_info_s2c() {
      bubbleList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_bubble_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c.class, org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c.Builder.class);
    }

    public static final int WEAR_ID_FIELD_NUMBER = 1;
    private int wearId_ = 0;
    /**
     * <code>uint32 wear_id = 1;</code>
     * @return The wearId.
     */
    @java.lang.Override
    public int getWearId() {
      return wearId_;
    }

    public static final int BUBBLE_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_bubble> bubbleList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_bubble> getBubbleListList() {
      return bubbleList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder> 
        getBubbleListOrBuilderList() {
      return bubbleList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
     */
    @java.lang.Override
    public int getBubbleListCount() {
      return bubbleList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_chat_bubble getBubbleList(int index) {
      return bubbleList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder getBubbleListOrBuilder(
        int index) {
      return bubbleList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (wearId_ != 0) {
        output.writeUInt32(1, wearId_);
      }
      for (int i = 0; i < bubbleList_.size(); i++) {
        output.writeMessage(2, bubbleList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (wearId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, wearId_);
      }
      for (int i = 0; i < bubbleList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, bubbleList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c other = (org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c) obj;

      if (getWearId()
          != other.getWearId()) return false;
      if (!getBubbleListList()
          .equals(other.getBubbleListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + WEAR_ID_FIELD_NUMBER;
      hash = (53 * hash) + getWearId();
      if (getBubbleListCount() > 0) {
        hash = (37 * hash) + BUBBLE_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getBubbleListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_bubble_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_bubble_info_s2c)
        org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c.class, org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        wearId_ = 0;
        if (bubbleListBuilder_ == null) {
          bubbleList_ = java.util.Collections.emptyList();
        } else {
          bubbleList_ = null;
          bubbleListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c result = new org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c result) {
        if (bubbleListBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            bubbleList_ = java.util.Collections.unmodifiableList(bubbleList_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.bubbleList_ = bubbleList_;
        } else {
          result.bubbleList_ = bubbleListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.wearId_ = wearId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c.getDefaultInstance()) return this;
        if (other.getWearId() != 0) {
          setWearId(other.getWearId());
        }
        if (bubbleListBuilder_ == null) {
          if (!other.bubbleList_.isEmpty()) {
            if (bubbleList_.isEmpty()) {
              bubbleList_ = other.bubbleList_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureBubbleListIsMutable();
              bubbleList_.addAll(other.bubbleList_);
            }
            onChanged();
          }
        } else {
          if (!other.bubbleList_.isEmpty()) {
            if (bubbleListBuilder_.isEmpty()) {
              bubbleListBuilder_.dispose();
              bubbleListBuilder_ = null;
              bubbleList_ = other.bubbleList_;
              bitField0_ = (bitField0_ & ~0x00000002);
              bubbleListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getBubbleListFieldBuilder() : null;
            } else {
              bubbleListBuilder_.addAllMessages(other.bubbleList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                wearId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_chat_bubble m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_chat_bubble.parser(),
                        extensionRegistry);
                if (bubbleListBuilder_ == null) {
                  ensureBubbleListIsMutable();
                  bubbleList_.add(m);
                } else {
                  bubbleListBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int wearId_ ;
      /**
       * <code>uint32 wear_id = 1;</code>
       * @return The wearId.
       */
      @java.lang.Override
      public int getWearId() {
        return wearId_;
      }
      /**
       * <code>uint32 wear_id = 1;</code>
       * @param value The wearId to set.
       * @return This builder for chaining.
       */
      public Builder setWearId(int value) {

        wearId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 wear_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearWearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        wearId_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_bubble> bubbleList_ =
        java.util.Collections.emptyList();
      private void ensureBubbleListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          bubbleList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_chat_bubble>(bubbleList_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_chat_bubble, org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder, org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder> bubbleListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_bubble> getBubbleListList() {
        if (bubbleListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(bubbleList_);
        } else {
          return bubbleListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public int getBubbleListCount() {
        if (bubbleListBuilder_ == null) {
          return bubbleList_.size();
        } else {
          return bubbleListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_bubble getBubbleList(int index) {
        if (bubbleListBuilder_ == null) {
          return bubbleList_.get(index);
        } else {
          return bubbleListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public Builder setBubbleList(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat_bubble value) {
        if (bubbleListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBubbleListIsMutable();
          bubbleList_.set(index, value);
          onChanged();
        } else {
          bubbleListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public Builder setBubbleList(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder builderForValue) {
        if (bubbleListBuilder_ == null) {
          ensureBubbleListIsMutable();
          bubbleList_.set(index, builderForValue.build());
          onChanged();
        } else {
          bubbleListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public Builder addBubbleList(org.gof.demo.worldsrv.msg.Define.p_chat_bubble value) {
        if (bubbleListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBubbleListIsMutable();
          bubbleList_.add(value);
          onChanged();
        } else {
          bubbleListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public Builder addBubbleList(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat_bubble value) {
        if (bubbleListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureBubbleListIsMutable();
          bubbleList_.add(index, value);
          onChanged();
        } else {
          bubbleListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public Builder addBubbleList(
          org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder builderForValue) {
        if (bubbleListBuilder_ == null) {
          ensureBubbleListIsMutable();
          bubbleList_.add(builderForValue.build());
          onChanged();
        } else {
          bubbleListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public Builder addBubbleList(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder builderForValue) {
        if (bubbleListBuilder_ == null) {
          ensureBubbleListIsMutable();
          bubbleList_.add(index, builderForValue.build());
          onChanged();
        } else {
          bubbleListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public Builder addAllBubbleList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_chat_bubble> values) {
        if (bubbleListBuilder_ == null) {
          ensureBubbleListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, bubbleList_);
          onChanged();
        } else {
          bubbleListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public Builder clearBubbleList() {
        if (bubbleListBuilder_ == null) {
          bubbleList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          bubbleListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public Builder removeBubbleList(int index) {
        if (bubbleListBuilder_ == null) {
          ensureBubbleListIsMutable();
          bubbleList_.remove(index);
          onChanged();
        } else {
          bubbleListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder getBubbleListBuilder(
          int index) {
        return getBubbleListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder getBubbleListOrBuilder(
          int index) {
        if (bubbleListBuilder_ == null) {
          return bubbleList_.get(index);  } else {
          return bubbleListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder> 
           getBubbleListOrBuilderList() {
        if (bubbleListBuilder_ != null) {
          return bubbleListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(bubbleList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder addBubbleListBuilder() {
        return getBubbleListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_chat_bubble.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder addBubbleListBuilder(
          int index) {
        return getBubbleListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_chat_bubble.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble bubble_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder> 
           getBubbleListBuilderList() {
        return getBubbleListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_chat_bubble, org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder, org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder> 
          getBubbleListFieldBuilder() {
        if (bubbleListBuilder_ == null) {
          bubbleListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_chat_bubble, org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder, org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder>(
                  bubbleList_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          bubbleList_ = null;
        }
        return bubbleListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_bubble_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_bubble_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_bubble_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<chat_bubble_info_s2c>() {
      @java.lang.Override
      public chat_bubble_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_bubble_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_bubble_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface change_chat_bubble_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.change_chat_bubble_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 cfg_id = 1;</code>
     * @return The cfgId.
     */
    int getCfgId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.change_chat_bubble_c2s}
   */
  public static final class change_chat_bubble_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.change_chat_bubble_c2s)
      change_chat_bubble_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use change_chat_bubble_c2s.newBuilder() to construct.
    private change_chat_bubble_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private change_chat_bubble_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new change_chat_bubble_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s.class, org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s.Builder.class);
    }

    public static final int CFG_ID_FIELD_NUMBER = 1;
    private int cfgId_ = 0;
    /**
     * <code>uint32 cfg_id = 1;</code>
     * @return The cfgId.
     */
    @java.lang.Override
    public int getCfgId() {
      return cfgId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (cfgId_ != 0) {
        output.writeUInt32(1, cfgId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (cfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, cfgId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s other = (org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s) obj;

      if (getCfgId()
          != other.getCfgId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CFG_ID_FIELD_NUMBER;
      hash = (53 * hash) + getCfgId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.change_chat_bubble_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.change_chat_bubble_c2s)
        org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s.class, org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        cfgId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s build() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s result = new org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.cfgId_ = cfgId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s.getDefaultInstance()) return this;
        if (other.getCfgId() != 0) {
          setCfgId(other.getCfgId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                cfgId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int cfgId_ ;
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @return The cfgId.
       */
      @java.lang.Override
      public int getCfgId() {
        return cfgId_;
      }
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @param value The cfgId to set.
       * @return This builder for chaining.
       */
      public Builder setCfgId(int value) {

        cfgId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCfgId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        cfgId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.change_chat_bubble_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.change_chat_bubble_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<change_chat_bubble_c2s>
        PARSER = new com.google.protobuf.AbstractParser<change_chat_bubble_c2s>() {
      @java.lang.Override
      public change_chat_bubble_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<change_chat_bubble_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<change_chat_bubble_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface change_chat_bubble_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.change_chat_bubble_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 wear_id = 1;</code>
     * @return The wearId.
     */
    int getWearId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.change_chat_bubble_s2c}
   */
  public static final class change_chat_bubble_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.change_chat_bubble_s2c)
      change_chat_bubble_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use change_chat_bubble_s2c.newBuilder() to construct.
    private change_chat_bubble_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private change_chat_bubble_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new change_chat_bubble_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c.class, org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c.Builder.class);
    }

    public static final int WEAR_ID_FIELD_NUMBER = 1;
    private int wearId_ = 0;
    /**
     * <code>uint32 wear_id = 1;</code>
     * @return The wearId.
     */
    @java.lang.Override
    public int getWearId() {
      return wearId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (wearId_ != 0) {
        output.writeUInt32(1, wearId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (wearId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, wearId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c other = (org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c) obj;

      if (getWearId()
          != other.getWearId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + WEAR_ID_FIELD_NUMBER;
      hash = (53 * hash) + getWearId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.change_chat_bubble_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.change_chat_bubble_s2c)
        org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c.class, org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        wearId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c build() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c result = new org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.wearId_ = wearId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c.getDefaultInstance()) return this;
        if (other.getWearId() != 0) {
          setWearId(other.getWearId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                wearId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int wearId_ ;
      /**
       * <code>uint32 wear_id = 1;</code>
       * @return The wearId.
       */
      @java.lang.Override
      public int getWearId() {
        return wearId_;
      }
      /**
       * <code>uint32 wear_id = 1;</code>
       * @param value The wearId to set.
       * @return This builder for chaining.
       */
      public Builder setWearId(int value) {

        wearId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 wear_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearWearId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        wearId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.change_chat_bubble_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.change_chat_bubble_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<change_chat_bubble_s2c>
        PARSER = new com.google.protobuf.AbstractParser<change_chat_bubble_s2c>() {
      @java.lang.Override
      public change_chat_bubble_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<change_chat_bubble_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<change_chat_bubble_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChatBubble.change_chat_bubble_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface update_chat_bubble_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.update_chat_bubble_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_bubble> 
        getUpdateListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_chat_bubble getUpdateList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
     */
    int getUpdateListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder> 
        getUpdateListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder getUpdateListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.update_chat_bubble_s2c}
   */
  public static final class update_chat_bubble_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.update_chat_bubble_s2c)
      update_chat_bubble_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use update_chat_bubble_s2c.newBuilder() to construct.
    private update_chat_bubble_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private update_chat_bubble_s2c() {
      updateList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new update_chat_bubble_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_update_chat_bubble_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_update_chat_bubble_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c.class, org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c.Builder.class);
    }

    public static final int UPDATE_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_bubble> updateList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_bubble> getUpdateListList() {
      return updateList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder> 
        getUpdateListOrBuilderList() {
      return updateList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
     */
    @java.lang.Override
    public int getUpdateListCount() {
      return updateList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_chat_bubble getUpdateList(int index) {
      return updateList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder getUpdateListOrBuilder(
        int index) {
      return updateList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < updateList_.size(); i++) {
        output.writeMessage(1, updateList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < updateList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, updateList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c other = (org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c) obj;

      if (!getUpdateListList()
          .equals(other.getUpdateListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getUpdateListCount() > 0) {
        hash = (37 * hash) + UPDATE_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getUpdateListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.update_chat_bubble_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.update_chat_bubble_s2c)
        org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_update_chat_bubble_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_update_chat_bubble_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c.class, org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (updateListBuilder_ == null) {
          updateList_ = java.util.Collections.emptyList();
        } else {
          updateList_ = null;
          updateListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_update_chat_bubble_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c build() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c result = new org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c result) {
        if (updateListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            updateList_ = java.util.Collections.unmodifiableList(updateList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.updateList_ = updateList_;
        } else {
          result.updateList_ = updateListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c.getDefaultInstance()) return this;
        if (updateListBuilder_ == null) {
          if (!other.updateList_.isEmpty()) {
            if (updateList_.isEmpty()) {
              updateList_ = other.updateList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureUpdateListIsMutable();
              updateList_.addAll(other.updateList_);
            }
            onChanged();
          }
        } else {
          if (!other.updateList_.isEmpty()) {
            if (updateListBuilder_.isEmpty()) {
              updateListBuilder_.dispose();
              updateListBuilder_ = null;
              updateList_ = other.updateList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              updateListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getUpdateListFieldBuilder() : null;
            } else {
              updateListBuilder_.addAllMessages(other.updateList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_chat_bubble m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_chat_bubble.parser(),
                        extensionRegistry);
                if (updateListBuilder_ == null) {
                  ensureUpdateListIsMutable();
                  updateList_.add(m);
                } else {
                  updateListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_bubble> updateList_ =
        java.util.Collections.emptyList();
      private void ensureUpdateListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          updateList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_chat_bubble>(updateList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_chat_bubble, org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder, org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder> updateListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_bubble> getUpdateListList() {
        if (updateListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(updateList_);
        } else {
          return updateListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public int getUpdateListCount() {
        if (updateListBuilder_ == null) {
          return updateList_.size();
        } else {
          return updateListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_bubble getUpdateList(int index) {
        if (updateListBuilder_ == null) {
          return updateList_.get(index);
        } else {
          return updateListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public Builder setUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat_bubble value) {
        if (updateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateListIsMutable();
          updateList_.set(index, value);
          onChanged();
        } else {
          updateListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public Builder setUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder builderForValue) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          updateList_.set(index, builderForValue.build());
          onChanged();
        } else {
          updateListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public Builder addUpdateList(org.gof.demo.worldsrv.msg.Define.p_chat_bubble value) {
        if (updateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateListIsMutable();
          updateList_.add(value);
          onChanged();
        } else {
          updateListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public Builder addUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat_bubble value) {
        if (updateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateListIsMutable();
          updateList_.add(index, value);
          onChanged();
        } else {
          updateListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public Builder addUpdateList(
          org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder builderForValue) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          updateList_.add(builderForValue.build());
          onChanged();
        } else {
          updateListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public Builder addUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder builderForValue) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          updateList_.add(index, builderForValue.build());
          onChanged();
        } else {
          updateListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public Builder addAllUpdateList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_chat_bubble> values) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, updateList_);
          onChanged();
        } else {
          updateListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public Builder clearUpdateList() {
        if (updateListBuilder_ == null) {
          updateList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          updateListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public Builder removeUpdateList(int index) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          updateList_.remove(index);
          onChanged();
        } else {
          updateListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder getUpdateListBuilder(
          int index) {
        return getUpdateListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder getUpdateListOrBuilder(
          int index) {
        if (updateListBuilder_ == null) {
          return updateList_.get(index);  } else {
          return updateListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder> 
           getUpdateListOrBuilderList() {
        if (updateListBuilder_ != null) {
          return updateListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(updateList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder addUpdateListBuilder() {
        return getUpdateListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_chat_bubble.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder addUpdateListBuilder(
          int index) {
        return getUpdateListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_chat_bubble.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_bubble update_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder> 
           getUpdateListBuilderList() {
        return getUpdateListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_chat_bubble, org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder, org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder> 
          getUpdateListFieldBuilder() {
        if (updateListBuilder_ == null) {
          updateListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_chat_bubble, org.gof.demo.worldsrv.msg.Define.p_chat_bubble.Builder, org.gof.demo.worldsrv.msg.Define.p_chat_bubbleOrBuilder>(
                  updateList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          updateList_ = null;
        }
        return updateListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.update_chat_bubble_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.update_chat_bubble_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<update_chat_bubble_s2c>
        PARSER = new com.google.protobuf.AbstractParser<update_chat_bubble_s2c>() {
      @java.lang.Override
      public update_chat_bubble_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<update_chat_bubble_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<update_chat_bubble_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChatBubble.update_chat_bubble_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface chat_bubble_red_point_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_bubble_red_point_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 cfg_id = 1;</code>
     * @return The cfgId.
     */
    int getCfgId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_bubble_red_point_c2s}
   */
  public static final class chat_bubble_red_point_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_bubble_red_point_c2s)
      chat_bubble_red_point_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_bubble_red_point_c2s.newBuilder() to construct.
    private chat_bubble_red_point_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_bubble_red_point_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_bubble_red_point_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s.class, org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s.Builder.class);
    }

    public static final int CFG_ID_FIELD_NUMBER = 1;
    private int cfgId_ = 0;
    /**
     * <code>uint32 cfg_id = 1;</code>
     * @return The cfgId.
     */
    @java.lang.Override
    public int getCfgId() {
      return cfgId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (cfgId_ != 0) {
        output.writeUInt32(1, cfgId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (cfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, cfgId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s other = (org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s) obj;

      if (getCfgId()
          != other.getCfgId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CFG_ID_FIELD_NUMBER;
      hash = (53 * hash) + getCfgId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_bubble_red_point_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_bubble_red_point_c2s)
        org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s.class, org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        cfgId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s build() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s result = new org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.cfgId_ = cfgId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s.getDefaultInstance()) return this;
        if (other.getCfgId() != 0) {
          setCfgId(other.getCfgId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                cfgId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int cfgId_ ;
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @return The cfgId.
       */
      @java.lang.Override
      public int getCfgId() {
        return cfgId_;
      }
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @param value The cfgId to set.
       * @return This builder for chaining.
       */
      public Builder setCfgId(int value) {

        cfgId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCfgId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        cfgId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_bubble_red_point_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_bubble_red_point_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_bubble_red_point_c2s>
        PARSER = new com.google.protobuf.AbstractParser<chat_bubble_red_point_c2s>() {
      @java.lang.Override
      public chat_bubble_red_point_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_bubble_red_point_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_bubble_red_point_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface chat_bubble_red_point_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_bubble_red_point_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 cfg_id = 1;</code>
     * @return The cfgId.
     */
    int getCfgId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_bubble_red_point_s2c}
   */
  public static final class chat_bubble_red_point_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_bubble_red_point_s2c)
      chat_bubble_red_point_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_bubble_red_point_s2c.newBuilder() to construct.
    private chat_bubble_red_point_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_bubble_red_point_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_bubble_red_point_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c.class, org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c.Builder.class);
    }

    public static final int CFG_ID_FIELD_NUMBER = 1;
    private int cfgId_ = 0;
    /**
     * <code>uint32 cfg_id = 1;</code>
     * @return The cfgId.
     */
    @java.lang.Override
    public int getCfgId() {
      return cfgId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (cfgId_ != 0) {
        output.writeUInt32(1, cfgId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (cfgId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, cfgId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c other = (org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c) obj;

      if (getCfgId()
          != other.getCfgId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CFG_ID_FIELD_NUMBER;
      hash = (53 * hash) + getCfgId();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_bubble_red_point_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_bubble_red_point_s2c)
        org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c.class, org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        cfgId_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c build() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c result = new org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.cfgId_ = cfgId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c.getDefaultInstance()) return this;
        if (other.getCfgId() != 0) {
          setCfgId(other.getCfgId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                cfgId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int cfgId_ ;
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @return The cfgId.
       */
      @java.lang.Override
      public int getCfgId() {
        return cfgId_;
      }
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @param value The cfgId to set.
       * @return This builder for chaining.
       */
      public Builder setCfgId(int value) {

        cfgId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 cfg_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCfgId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        cfgId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_bubble_red_point_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_bubble_red_point_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_bubble_red_point_s2c>
        PARSER = new com.google.protobuf.AbstractParser<chat_bubble_red_point_s2c>() {
      @java.lang.Override
      public chat_bubble_red_point_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_bubble_red_point_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_bubble_red_point_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChatBubble.chat_bubble_red_point_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_update_chat_bubble_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_update_chat_bubble_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\024msg.chatBubble.proto\022\031org.gof.demo.wor" +
      "ldsrv.msg\032\roptions.proto\032\014define.proto\"\035" +
      "\n\024chat_bubble_info_c2s:\005\210\303\032\201h\"m\n\024chat_bu" +
      "bble_info_s2c\022\017\n\007wear_id\030\001 \001(\r\022=\n\013bubble" +
      "_list\030\002 \003(\0132(.org.gof.demo.worldsrv.msg." +
      "p_chat_bubble:\005\210\303\032\201h\"/\n\026change_chat_bubb" +
      "le_c2s\022\016\n\006cfg_id\030\001 \001(\r:\005\210\303\032\202h\"0\n\026change_" +
      "chat_bubble_s2c\022\017\n\007wear_id\030\001 \001(\r:\005\210\303\032\202h\"" +
      "^\n\026update_chat_bubble_s2c\022=\n\013update_list" +
      "\030\001 \003(\0132(.org.gof.demo.worldsrv.msg.p_cha" +
      "t_bubble:\005\210\303\032\203h\"2\n\031chat_bubble_red_point" +
      "_c2s\022\016\n\006cfg_id\030\001 \001(\r:\005\210\303\032\204h\"2\n\031chat_bubb" +
      "le_red_point_s2c\022\016\n\006cfg_id\030\001 \001(\r:\005\210\303\032\204hb" +
      "\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_bubble_info_s2c_descriptor,
        new java.lang.String[] { "WearId", "BubbleList", });
    internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_c2s_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_c2s_descriptor,
        new java.lang.String[] { "CfgId", });
    internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_change_chat_bubble_s2c_descriptor,
        new java.lang.String[] { "WearId", });
    internal_static_org_gof_demo_worldsrv_msg_update_chat_bubble_s2c_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_update_chat_bubble_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_update_chat_bubble_s2c_descriptor,
        new java.lang.String[] { "UpdateList", });
    internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_c2s_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_c2s_descriptor,
        new java.lang.String[] { "CfgId", });
    internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_s2c_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_bubble_red_point_s2c_descriptor,
        new java.lang.String[] { "CfgId", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
