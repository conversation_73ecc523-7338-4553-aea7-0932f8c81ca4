// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.goods.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgGoods {
  private MsgGoods() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface goods_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.goods_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_info_c2s}
   */
  public static final class goods_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.goods_info_c2s)
      goods_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use goods_info_c2s.newBuilder() to construct.
    private goods_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private goods_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new goods_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s other = (org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.goods_info_c2s)
        org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s result = new org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.goods_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.goods_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<goods_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<goods_info_c2s>() {
      @java.lang.Override
      public goods_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<goods_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<goods_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgGoods.goods_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface goods_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.goods_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_goods> 
        getGoodsListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_goods getGoodsList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
     */
    int getGoodsListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder> 
        getGoodsListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder getGoodsListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_info_s2c}
   */
  public static final class goods_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.goods_info_s2c)
      goods_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use goods_info_s2c.newBuilder() to construct.
    private goods_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private goods_info_s2c() {
      goodsList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new goods_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c.Builder.class);
    }

    public static final int GOODS_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_goods> goodsList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_goods> getGoodsListList() {
      return goodsList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder> 
        getGoodsListOrBuilderList() {
      return goodsList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
     */
    @java.lang.Override
    public int getGoodsListCount() {
      return goodsList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_goods getGoodsList(int index) {
      return goodsList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder getGoodsListOrBuilder(
        int index) {
      return goodsList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < goodsList_.size(); i++) {
        output.writeMessage(1, goodsList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < goodsList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, goodsList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c other = (org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c) obj;

      if (!getGoodsListList()
          .equals(other.getGoodsListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getGoodsListCount() > 0) {
        hash = (37 * hash) + GOODS_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getGoodsListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.goods_info_s2c)
        org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (goodsListBuilder_ == null) {
          goodsList_ = java.util.Collections.emptyList();
        } else {
          goodsList_ = null;
          goodsListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c result = new org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c result) {
        if (goodsListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            goodsList_ = java.util.Collections.unmodifiableList(goodsList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.goodsList_ = goodsList_;
        } else {
          result.goodsList_ = goodsListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c.getDefaultInstance()) return this;
        if (goodsListBuilder_ == null) {
          if (!other.goodsList_.isEmpty()) {
            if (goodsList_.isEmpty()) {
              goodsList_ = other.goodsList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureGoodsListIsMutable();
              goodsList_.addAll(other.goodsList_);
            }
            onChanged();
          }
        } else {
          if (!other.goodsList_.isEmpty()) {
            if (goodsListBuilder_.isEmpty()) {
              goodsListBuilder_.dispose();
              goodsListBuilder_ = null;
              goodsList_ = other.goodsList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              goodsListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getGoodsListFieldBuilder() : null;
            } else {
              goodsListBuilder_.addAllMessages(other.goodsList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_goods m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_goods.parser(),
                        extensionRegistry);
                if (goodsListBuilder_ == null) {
                  ensureGoodsListIsMutable();
                  goodsList_.add(m);
                } else {
                  goodsListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_goods> goodsList_ =
        java.util.Collections.emptyList();
      private void ensureGoodsListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          goodsList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_goods>(goodsList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_goods, org.gof.demo.worldsrv.msg.Define.p_goods.Builder, org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder> goodsListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_goods> getGoodsListList() {
        if (goodsListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(goodsList_);
        } else {
          return goodsListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public int getGoodsListCount() {
        if (goodsListBuilder_ == null) {
          return goodsList_.size();
        } else {
          return goodsListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_goods getGoodsList(int index) {
        if (goodsListBuilder_ == null) {
          return goodsList_.get(index);
        } else {
          return goodsListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public Builder setGoodsList(
          int index, org.gof.demo.worldsrv.msg.Define.p_goods value) {
        if (goodsListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGoodsListIsMutable();
          goodsList_.set(index, value);
          onChanged();
        } else {
          goodsListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public Builder setGoodsList(
          int index, org.gof.demo.worldsrv.msg.Define.p_goods.Builder builderForValue) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          goodsList_.set(index, builderForValue.build());
          onChanged();
        } else {
          goodsListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public Builder addGoodsList(org.gof.demo.worldsrv.msg.Define.p_goods value) {
        if (goodsListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGoodsListIsMutable();
          goodsList_.add(value);
          onChanged();
        } else {
          goodsListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public Builder addGoodsList(
          int index, org.gof.demo.worldsrv.msg.Define.p_goods value) {
        if (goodsListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGoodsListIsMutable();
          goodsList_.add(index, value);
          onChanged();
        } else {
          goodsListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public Builder addGoodsList(
          org.gof.demo.worldsrv.msg.Define.p_goods.Builder builderForValue) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          goodsList_.add(builderForValue.build());
          onChanged();
        } else {
          goodsListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public Builder addGoodsList(
          int index, org.gof.demo.worldsrv.msg.Define.p_goods.Builder builderForValue) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          goodsList_.add(index, builderForValue.build());
          onChanged();
        } else {
          goodsListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public Builder addAllGoodsList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_goods> values) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, goodsList_);
          onChanged();
        } else {
          goodsListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public Builder clearGoodsList() {
        if (goodsListBuilder_ == null) {
          goodsList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          goodsListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public Builder removeGoodsList(int index) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          goodsList_.remove(index);
          onChanged();
        } else {
          goodsListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_goods.Builder getGoodsListBuilder(
          int index) {
        return getGoodsListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder getGoodsListOrBuilder(
          int index) {
        if (goodsListBuilder_ == null) {
          return goodsList_.get(index);  } else {
          return goodsListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder> 
           getGoodsListOrBuilderList() {
        if (goodsListBuilder_ != null) {
          return goodsListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(goodsList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_goods.Builder addGoodsListBuilder() {
        return getGoodsListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_goods.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_goods.Builder addGoodsListBuilder(
          int index) {
        return getGoodsListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_goods.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_goods.Builder> 
           getGoodsListBuilderList() {
        return getGoodsListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_goods, org.gof.demo.worldsrv.msg.Define.p_goods.Builder, org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder> 
          getGoodsListFieldBuilder() {
        if (goodsListBuilder_ == null) {
          goodsListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_goods, org.gof.demo.worldsrv.msg.Define.p_goods.Builder, org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder>(
                  goodsList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          goodsList_ = null;
        }
        return goodsListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.goods_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.goods_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<goods_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<goods_info_s2c>() {
      @java.lang.Override
      public goods_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<goods_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<goods_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgGoods.goods_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface goods_change_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.goods_change_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 source = 1;</code>
     * @return The source.
     */
    int getSource();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_goods> 
        getGoodsListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_goods getGoodsList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
     */
    int getGoodsListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder> 
        getGoodsListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder getGoodsListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_change_s2c}
   */
  public static final class goods_change_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.goods_change_s2c)
      goods_change_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use goods_change_s2c.newBuilder() to construct.
    private goods_change_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private goods_change_s2c() {
      goodsList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new goods_change_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_change_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_change_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c.Builder.class);
    }

    public static final int SOURCE_FIELD_NUMBER = 1;
    private int source_ = 0;
    /**
     * <code>int32 source = 1;</code>
     * @return The source.
     */
    @java.lang.Override
    public int getSource() {
      return source_;
    }

    public static final int GOODS_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_goods> goodsList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_goods> getGoodsListList() {
      return goodsList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder> 
        getGoodsListOrBuilderList() {
      return goodsList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
     */
    @java.lang.Override
    public int getGoodsListCount() {
      return goodsList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_goods getGoodsList(int index) {
      return goodsList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder getGoodsListOrBuilder(
        int index) {
      return goodsList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (source_ != 0) {
        output.writeInt32(1, source_);
      }
      for (int i = 0; i < goodsList_.size(); i++) {
        output.writeMessage(2, goodsList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (source_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, source_);
      }
      for (int i = 0; i < goodsList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, goodsList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c other = (org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c) obj;

      if (getSource()
          != other.getSource()) return false;
      if (!getGoodsListList()
          .equals(other.getGoodsListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SOURCE_FIELD_NUMBER;
      hash = (53 * hash) + getSource();
      if (getGoodsListCount() > 0) {
        hash = (37 * hash) + GOODS_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getGoodsListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_change_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.goods_change_s2c)
        org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_change_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_change_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        source_ = 0;
        if (goodsListBuilder_ == null) {
          goodsList_ = java.util.Collections.emptyList();
        } else {
          goodsList_ = null;
          goodsListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_change_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c build() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c result = new org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c result) {
        if (goodsListBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            goodsList_ = java.util.Collections.unmodifiableList(goodsList_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.goodsList_ = goodsList_;
        } else {
          result.goodsList_ = goodsListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.source_ = source_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c.getDefaultInstance()) return this;
        if (other.getSource() != 0) {
          setSource(other.getSource());
        }
        if (goodsListBuilder_ == null) {
          if (!other.goodsList_.isEmpty()) {
            if (goodsList_.isEmpty()) {
              goodsList_ = other.goodsList_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureGoodsListIsMutable();
              goodsList_.addAll(other.goodsList_);
            }
            onChanged();
          }
        } else {
          if (!other.goodsList_.isEmpty()) {
            if (goodsListBuilder_.isEmpty()) {
              goodsListBuilder_.dispose();
              goodsListBuilder_ = null;
              goodsList_ = other.goodsList_;
              bitField0_ = (bitField0_ & ~0x00000002);
              goodsListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getGoodsListFieldBuilder() : null;
            } else {
              goodsListBuilder_.addAllMessages(other.goodsList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                source_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_goods m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_goods.parser(),
                        extensionRegistry);
                if (goodsListBuilder_ == null) {
                  ensureGoodsListIsMutable();
                  goodsList_.add(m);
                } else {
                  goodsListBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int source_ ;
      /**
       * <code>int32 source = 1;</code>
       * @return The source.
       */
      @java.lang.Override
      public int getSource() {
        return source_;
      }
      /**
       * <code>int32 source = 1;</code>
       * @param value The source to set.
       * @return This builder for chaining.
       */
      public Builder setSource(int value) {

        source_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 source = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSource() {
        bitField0_ = (bitField0_ & ~0x00000001);
        source_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_goods> goodsList_ =
        java.util.Collections.emptyList();
      private void ensureGoodsListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          goodsList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_goods>(goodsList_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_goods, org.gof.demo.worldsrv.msg.Define.p_goods.Builder, org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder> goodsListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_goods> getGoodsListList() {
        if (goodsListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(goodsList_);
        } else {
          return goodsListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public int getGoodsListCount() {
        if (goodsListBuilder_ == null) {
          return goodsList_.size();
        } else {
          return goodsListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_goods getGoodsList(int index) {
        if (goodsListBuilder_ == null) {
          return goodsList_.get(index);
        } else {
          return goodsListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public Builder setGoodsList(
          int index, org.gof.demo.worldsrv.msg.Define.p_goods value) {
        if (goodsListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGoodsListIsMutable();
          goodsList_.set(index, value);
          onChanged();
        } else {
          goodsListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public Builder setGoodsList(
          int index, org.gof.demo.worldsrv.msg.Define.p_goods.Builder builderForValue) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          goodsList_.set(index, builderForValue.build());
          onChanged();
        } else {
          goodsListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public Builder addGoodsList(org.gof.demo.worldsrv.msg.Define.p_goods value) {
        if (goodsListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGoodsListIsMutable();
          goodsList_.add(value);
          onChanged();
        } else {
          goodsListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public Builder addGoodsList(
          int index, org.gof.demo.worldsrv.msg.Define.p_goods value) {
        if (goodsListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGoodsListIsMutable();
          goodsList_.add(index, value);
          onChanged();
        } else {
          goodsListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public Builder addGoodsList(
          org.gof.demo.worldsrv.msg.Define.p_goods.Builder builderForValue) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          goodsList_.add(builderForValue.build());
          onChanged();
        } else {
          goodsListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public Builder addGoodsList(
          int index, org.gof.demo.worldsrv.msg.Define.p_goods.Builder builderForValue) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          goodsList_.add(index, builderForValue.build());
          onChanged();
        } else {
          goodsListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public Builder addAllGoodsList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_goods> values) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, goodsList_);
          onChanged();
        } else {
          goodsListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public Builder clearGoodsList() {
        if (goodsListBuilder_ == null) {
          goodsList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          goodsListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public Builder removeGoodsList(int index) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          goodsList_.remove(index);
          onChanged();
        } else {
          goodsListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_goods.Builder getGoodsListBuilder(
          int index) {
        return getGoodsListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder getGoodsListOrBuilder(
          int index) {
        if (goodsListBuilder_ == null) {
          return goodsList_.get(index);  } else {
          return goodsListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder> 
           getGoodsListOrBuilderList() {
        if (goodsListBuilder_ != null) {
          return goodsListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(goodsList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_goods.Builder addGoodsListBuilder() {
        return getGoodsListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_goods.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_goods.Builder addGoodsListBuilder(
          int index) {
        return getGoodsListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_goods.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_goods goods_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_goods.Builder> 
           getGoodsListBuilderList() {
        return getGoodsListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_goods, org.gof.demo.worldsrv.msg.Define.p_goods.Builder, org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder> 
          getGoodsListFieldBuilder() {
        if (goodsListBuilder_ == null) {
          goodsListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_goods, org.gof.demo.worldsrv.msg.Define.p_goods.Builder, org.gof.demo.worldsrv.msg.Define.p_goodsOrBuilder>(
                  goodsList_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          goodsList_ = null;
        }
        return goodsListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.goods_change_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.goods_change_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<goods_change_s2c>
        PARSER = new com.google.protobuf.AbstractParser<goods_change_s2c>() {
      @java.lang.Override
      public goods_change_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<goods_change_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<goods_change_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgGoods.goods_change_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface goods_use_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.goods_use_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 goods_id = 1;</code>
     * @return The goodsId.
     */
    long getGoodsId();

    /**
     * <code>int32 num = 2;</code>
     * @return The num.
     */
    int getNum();

    /**
     * <code>int32 choose_gtid = 3;</code>
     * @return The chooseGtid.
     */
    int getChooseGtid();

    /**
     * <code>int32 choose_use = 4;</code>
     * @return The chooseUse.
     */
    int getChooseUse();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getExtListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getExtList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
     */
    int getExtListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getExtListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getExtListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_use_c2s}
   */
  public static final class goods_use_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.goods_use_c2s)
      goods_use_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use goods_use_c2s.newBuilder() to construct.
    private goods_use_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private goods_use_c2s() {
      extList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new goods_use_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_use_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_use_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s.Builder.class);
    }

    public static final int GOODS_ID_FIELD_NUMBER = 1;
    private long goodsId_ = 0L;
    /**
     * <code>uint64 goods_id = 1;</code>
     * @return The goodsId.
     */
    @java.lang.Override
    public long getGoodsId() {
      return goodsId_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_ = 0;
    /**
     * <code>int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    public static final int CHOOSE_GTID_FIELD_NUMBER = 3;
    private int chooseGtid_ = 0;
    /**
     * <code>int32 choose_gtid = 3;</code>
     * @return The chooseGtid.
     */
    @java.lang.Override
    public int getChooseGtid() {
      return chooseGtid_;
    }

    public static final int CHOOSE_USE_FIELD_NUMBER = 4;
    private int chooseUse_ = 0;
    /**
     * <code>int32 choose_use = 4;</code>
     * @return The chooseUse.
     */
    @java.lang.Override
    public int getChooseUse() {
      return chooseUse_;
    }

    public static final int EXT_LIST_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> extList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getExtListList() {
      return extList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getExtListOrBuilderList() {
      return extList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
     */
    @java.lang.Override
    public int getExtListCount() {
      return extList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getExtList(int index) {
      return extList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getExtListOrBuilder(
        int index) {
      return extList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (goodsId_ != 0L) {
        output.writeUInt64(1, goodsId_);
      }
      if (num_ != 0) {
        output.writeInt32(2, num_);
      }
      if (chooseGtid_ != 0) {
        output.writeInt32(3, chooseGtid_);
      }
      if (chooseUse_ != 0) {
        output.writeInt32(4, chooseUse_);
      }
      for (int i = 0; i < extList_.size(); i++) {
        output.writeMessage(5, extList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (goodsId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, goodsId_);
      }
      if (num_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      if (chooseGtid_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, chooseGtid_);
      }
      if (chooseUse_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, chooseUse_);
      }
      for (int i = 0; i < extList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, extList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s other = (org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s) obj;

      if (getGoodsId()
          != other.getGoodsId()) return false;
      if (getNum()
          != other.getNum()) return false;
      if (getChooseGtid()
          != other.getChooseGtid()) return false;
      if (getChooseUse()
          != other.getChooseUse()) return false;
      if (!getExtListList()
          .equals(other.getExtListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + GOODS_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getGoodsId());
      hash = (37 * hash) + NUM_FIELD_NUMBER;
      hash = (53 * hash) + getNum();
      hash = (37 * hash) + CHOOSE_GTID_FIELD_NUMBER;
      hash = (53 * hash) + getChooseGtid();
      hash = (37 * hash) + CHOOSE_USE_FIELD_NUMBER;
      hash = (53 * hash) + getChooseUse();
      if (getExtListCount() > 0) {
        hash = (37 * hash) + EXT_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getExtListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_use_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.goods_use_c2s)
        org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_use_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_use_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        goodsId_ = 0L;
        num_ = 0;
        chooseGtid_ = 0;
        chooseUse_ = 0;
        if (extListBuilder_ == null) {
          extList_ = java.util.Collections.emptyList();
        } else {
          extList_ = null;
          extListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_use_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s build() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s result = new org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s result) {
        if (extListBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0)) {
            extList_ = java.util.Collections.unmodifiableList(extList_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.extList_ = extList_;
        } else {
          result.extList_ = extListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.goodsId_ = goodsId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.chooseGtid_ = chooseGtid_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.chooseUse_ = chooseUse_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s.getDefaultInstance()) return this;
        if (other.getGoodsId() != 0L) {
          setGoodsId(other.getGoodsId());
        }
        if (other.getNum() != 0) {
          setNum(other.getNum());
        }
        if (other.getChooseGtid() != 0) {
          setChooseGtid(other.getChooseGtid());
        }
        if (other.getChooseUse() != 0) {
          setChooseUse(other.getChooseUse());
        }
        if (extListBuilder_ == null) {
          if (!other.extList_.isEmpty()) {
            if (extList_.isEmpty()) {
              extList_ = other.extList_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureExtListIsMutable();
              extList_.addAll(other.extList_);
            }
            onChanged();
          }
        } else {
          if (!other.extList_.isEmpty()) {
            if (extListBuilder_.isEmpty()) {
              extListBuilder_.dispose();
              extListBuilder_ = null;
              extList_ = other.extList_;
              bitField0_ = (bitField0_ & ~0x00000010);
              extListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getExtListFieldBuilder() : null;
            } else {
              extListBuilder_.addAllMessages(other.extList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                goodsId_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                num_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                chooseGtid_ = input.readInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 32: {
                chooseUse_ = input.readInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              case 42: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (extListBuilder_ == null) {
                  ensureExtListIsMutable();
                  extList_.add(m);
                } else {
                  extListBuilder_.addMessage(m);
                }
                break;
              } // case 42
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long goodsId_ ;
      /**
       * <code>uint64 goods_id = 1;</code>
       * @return The goodsId.
       */
      @java.lang.Override
      public long getGoodsId() {
        return goodsId_;
      }
      /**
       * <code>uint64 goods_id = 1;</code>
       * @param value The goodsId to set.
       * @return This builder for chaining.
       */
      public Builder setGoodsId(long value) {

        goodsId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 goods_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearGoodsId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        goodsId_ = 0L;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {

        num_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }

      private int chooseGtid_ ;
      /**
       * <code>int32 choose_gtid = 3;</code>
       * @return The chooseGtid.
       */
      @java.lang.Override
      public int getChooseGtid() {
        return chooseGtid_;
      }
      /**
       * <code>int32 choose_gtid = 3;</code>
       * @param value The chooseGtid to set.
       * @return This builder for chaining.
       */
      public Builder setChooseGtid(int value) {

        chooseGtid_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>int32 choose_gtid = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearChooseGtid() {
        bitField0_ = (bitField0_ & ~0x00000004);
        chooseGtid_ = 0;
        onChanged();
        return this;
      }

      private int chooseUse_ ;
      /**
       * <code>int32 choose_use = 4;</code>
       * @return The chooseUse.
       */
      @java.lang.Override
      public int getChooseUse() {
        return chooseUse_;
      }
      /**
       * <code>int32 choose_use = 4;</code>
       * @param value The chooseUse to set.
       * @return This builder for chaining.
       */
      public Builder setChooseUse(int value) {

        chooseUse_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>int32 choose_use = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearChooseUse() {
        bitField0_ = (bitField0_ & ~0x00000008);
        chooseUse_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> extList_ =
        java.util.Collections.emptyList();
      private void ensureExtListIsMutable() {
        if (!((bitField0_ & 0x00000010) != 0)) {
          extList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(extList_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> extListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getExtListList() {
        if (extListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(extList_);
        } else {
          return extListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public int getExtListCount() {
        if (extListBuilder_ == null) {
          return extList_.size();
        } else {
          return extListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getExtList(int index) {
        if (extListBuilder_ == null) {
          return extList_.get(index);
        } else {
          return extListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public Builder setExtList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (extListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExtListIsMutable();
          extList_.set(index, value);
          onChanged();
        } else {
          extListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public Builder setExtList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (extListBuilder_ == null) {
          ensureExtListIsMutable();
          extList_.set(index, builderForValue.build());
          onChanged();
        } else {
          extListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public Builder addExtList(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (extListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExtListIsMutable();
          extList_.add(value);
          onChanged();
        } else {
          extListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public Builder addExtList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (extListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExtListIsMutable();
          extList_.add(index, value);
          onChanged();
        } else {
          extListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public Builder addExtList(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (extListBuilder_ == null) {
          ensureExtListIsMutable();
          extList_.add(builderForValue.build());
          onChanged();
        } else {
          extListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public Builder addExtList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (extListBuilder_ == null) {
          ensureExtListIsMutable();
          extList_.add(index, builderForValue.build());
          onChanged();
        } else {
          extListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public Builder addAllExtList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (extListBuilder_ == null) {
          ensureExtListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, extList_);
          onChanged();
        } else {
          extListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public Builder clearExtList() {
        if (extListBuilder_ == null) {
          extList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          extListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public Builder removeExtList(int index) {
        if (extListBuilder_ == null) {
          ensureExtListIsMutable();
          extList_.remove(index);
          onChanged();
        } else {
          extListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getExtListBuilder(
          int index) {
        return getExtListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getExtListOrBuilder(
          int index) {
        if (extListBuilder_ == null) {
          return extList_.get(index);  } else {
          return extListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getExtListOrBuilderList() {
        if (extListBuilder_ != null) {
          return extListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(extList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addExtListBuilder() {
        return getExtListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addExtListBuilder(
          int index) {
        return getExtListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 5;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getExtListBuilderList() {
        return getExtListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getExtListFieldBuilder() {
        if (extListBuilder_ == null) {
          extListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  extList_,
                  ((bitField0_ & 0x00000010) != 0),
                  getParentForChildren(),
                  isClean());
          extList_ = null;
        }
        return extListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.goods_use_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.goods_use_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<goods_use_c2s>
        PARSER = new com.google.protobuf.AbstractParser<goods_use_c2s>() {
      @java.lang.Override
      public goods_use_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<goods_use_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<goods_use_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgGoods.goods_use_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface goods_use_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.goods_use_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();

    /**
     * <code>uint64 goods_id = 2;</code>
     * @return The goodsId.
     */
    long getGoodsId();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> 
        getGoodsListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward getGoodsList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
     */
    int getGoodsListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getGoodsListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getGoodsListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_use_s2c}
   */
  public static final class goods_use_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.goods_use_s2c)
      goods_use_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use goods_use_s2c.newBuilder() to construct.
    private goods_use_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private goods_use_s2c() {
      goodsList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new goods_use_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_use_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_use_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    public static final int GOODS_ID_FIELD_NUMBER = 2;
    private long goodsId_ = 0L;
    /**
     * <code>uint64 goods_id = 2;</code>
     * @return The goodsId.
     */
    @java.lang.Override
    public long getGoodsId() {
      return goodsId_;
    }

    public static final int GOODS_LIST_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> goodsList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getGoodsListList() {
      return goodsList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getGoodsListOrBuilderList() {
      return goodsList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
     */
    @java.lang.Override
    public int getGoodsListCount() {
      return goodsList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward getGoodsList(int index) {
      return goodsList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getGoodsListOrBuilder(
        int index) {
      return goodsList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      if (goodsId_ != 0L) {
        output.writeUInt64(2, goodsId_);
      }
      for (int i = 0; i < goodsList_.size(); i++) {
        output.writeMessage(3, goodsList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      if (goodsId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, goodsId_);
      }
      for (int i = 0; i < goodsList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, goodsList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c other = (org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c) obj;

      if (getCode()
          != other.getCode()) return false;
      if (getGoodsId()
          != other.getGoodsId()) return false;
      if (!getGoodsListList()
          .equals(other.getGoodsListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (37 * hash) + GOODS_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getGoodsId());
      if (getGoodsListCount() > 0) {
        hash = (37 * hash) + GOODS_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getGoodsListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_use_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.goods_use_s2c)
        org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_use_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_use_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        goodsId_ = 0L;
        if (goodsListBuilder_ == null) {
          goodsList_ = java.util.Collections.emptyList();
        } else {
          goodsList_ = null;
          goodsListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_use_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c build() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c result = new org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c result) {
        if (goodsListBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            goodsList_ = java.util.Collections.unmodifiableList(goodsList_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.goodsList_ = goodsList_;
        } else {
          result.goodsList_ = goodsListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.goodsId_ = goodsId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        if (other.getGoodsId() != 0L) {
          setGoodsId(other.getGoodsId());
        }
        if (goodsListBuilder_ == null) {
          if (!other.goodsList_.isEmpty()) {
            if (goodsList_.isEmpty()) {
              goodsList_ = other.goodsList_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureGoodsListIsMutable();
              goodsList_.addAll(other.goodsList_);
            }
            onChanged();
          }
        } else {
          if (!other.goodsList_.isEmpty()) {
            if (goodsListBuilder_.isEmpty()) {
              goodsListBuilder_.dispose();
              goodsListBuilder_ = null;
              goodsList_ = other.goodsList_;
              bitField0_ = (bitField0_ & ~0x00000004);
              goodsListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getGoodsListFieldBuilder() : null;
            } else {
              goodsListBuilder_.addAllMessages(other.goodsList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                goodsId_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                org.gof.demo.worldsrv.msg.Define.p_reward m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_reward.parser(),
                        extensionRegistry);
                if (goodsListBuilder_ == null) {
                  ensureGoodsListIsMutable();
                  goodsList_.add(m);
                } else {
                  goodsListBuilder_.addMessage(m);
                }
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }

      private long goodsId_ ;
      /**
       * <code>uint64 goods_id = 2;</code>
       * @return The goodsId.
       */
      @java.lang.Override
      public long getGoodsId() {
        return goodsId_;
      }
      /**
       * <code>uint64 goods_id = 2;</code>
       * @param value The goodsId to set.
       * @return This builder for chaining.
       */
      public Builder setGoodsId(long value) {

        goodsId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 goods_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearGoodsId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        goodsId_ = 0L;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> goodsList_ =
        java.util.Collections.emptyList();
      private void ensureGoodsListIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          goodsList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_reward>(goodsList_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> goodsListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getGoodsListList() {
        if (goodsListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(goodsList_);
        } else {
          return goodsListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public int getGoodsListCount() {
        if (goodsListBuilder_ == null) {
          return goodsList_.size();
        } else {
          return goodsListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward getGoodsList(int index) {
        if (goodsListBuilder_ == null) {
          return goodsList_.get(index);
        } else {
          return goodsListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public Builder setGoodsList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (goodsListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGoodsListIsMutable();
          goodsList_.set(index, value);
          onChanged();
        } else {
          goodsListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public Builder setGoodsList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          goodsList_.set(index, builderForValue.build());
          onChanged();
        } else {
          goodsListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public Builder addGoodsList(org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (goodsListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGoodsListIsMutable();
          goodsList_.add(value);
          onChanged();
        } else {
          goodsListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public Builder addGoodsList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (goodsListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureGoodsListIsMutable();
          goodsList_.add(index, value);
          onChanged();
        } else {
          goodsListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public Builder addGoodsList(
          org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          goodsList_.add(builderForValue.build());
          onChanged();
        } else {
          goodsListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public Builder addGoodsList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          goodsList_.add(index, builderForValue.build());
          onChanged();
        } else {
          goodsListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public Builder addAllGoodsList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_reward> values) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, goodsList_);
          onChanged();
        } else {
          goodsListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public Builder clearGoodsList() {
        if (goodsListBuilder_ == null) {
          goodsList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          goodsListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public Builder removeGoodsList(int index) {
        if (goodsListBuilder_ == null) {
          ensureGoodsListIsMutable();
          goodsList_.remove(index);
          onChanged();
        } else {
          goodsListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder getGoodsListBuilder(
          int index) {
        return getGoodsListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getGoodsListOrBuilder(
          int index) {
        if (goodsListBuilder_ == null) {
          return goodsList_.get(index);  } else {
          return goodsListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
           getGoodsListOrBuilderList() {
        if (goodsListBuilder_ != null) {
          return goodsListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(goodsList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addGoodsListBuilder() {
        return getGoodsListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addGoodsListBuilder(
          int index) {
        return getGoodsListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward goods_list = 3;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward.Builder> 
           getGoodsListBuilderList() {
        return getGoodsListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
          getGoodsListFieldBuilder() {
        if (goodsListBuilder_ == null) {
          goodsListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder>(
                  goodsList_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          goodsList_ = null;
        }
        return goodsListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.goods_use_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.goods_use_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<goods_use_s2c>
        PARSER = new com.google.protobuf.AbstractParser<goods_use_s2c>() {
      @java.lang.Override
      public goods_use_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<goods_use_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<goods_use_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgGoods.goods_use_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface goods_buy_and_use_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.goods_buy_and_use_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 goods_id = 1;</code>
     * @return The goodsId.
     */
    int getGoodsId();

    /**
     * <code>int32 num = 2;</code>
     * @return The num.
     */
    int getNum();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_buy_and_use_c2s}
   */
  public static final class goods_buy_and_use_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.goods_buy_and_use_c2s)
      goods_buy_and_use_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use goods_buy_and_use_c2s.newBuilder() to construct.
    private goods_buy_and_use_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private goods_buy_and_use_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new goods_buy_and_use_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_buy_and_use_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_buy_and_use_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s.Builder.class);
    }

    public static final int GOODS_ID_FIELD_NUMBER = 1;
    private int goodsId_ = 0;
    /**
     * <code>int32 goods_id = 1;</code>
     * @return The goodsId.
     */
    @java.lang.Override
    public int getGoodsId() {
      return goodsId_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_ = 0;
    /**
     * <code>int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (goodsId_ != 0) {
        output.writeInt32(1, goodsId_);
      }
      if (num_ != 0) {
        output.writeInt32(2, num_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (goodsId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, goodsId_);
      }
      if (num_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s other = (org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s) obj;

      if (getGoodsId()
          != other.getGoodsId()) return false;
      if (getNum()
          != other.getNum()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + GOODS_ID_FIELD_NUMBER;
      hash = (53 * hash) + getGoodsId();
      hash = (37 * hash) + NUM_FIELD_NUMBER;
      hash = (53 * hash) + getNum();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_buy_and_use_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.goods_buy_and_use_c2s)
        org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_buy_and_use_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_buy_and_use_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        goodsId_ = 0;
        num_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_buy_and_use_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s build() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s result = new org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.goodsId_ = goodsId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s.getDefaultInstance()) return this;
        if (other.getGoodsId() != 0) {
          setGoodsId(other.getGoodsId());
        }
        if (other.getNum() != 0) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                goodsId_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                num_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int goodsId_ ;
      /**
       * <code>int32 goods_id = 1;</code>
       * @return The goodsId.
       */
      @java.lang.Override
      public int getGoodsId() {
        return goodsId_;
      }
      /**
       * <code>int32 goods_id = 1;</code>
       * @param value The goodsId to set.
       * @return This builder for chaining.
       */
      public Builder setGoodsId(int value) {

        goodsId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 goods_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearGoodsId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        goodsId_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {

        num_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.goods_buy_and_use_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.goods_buy_and_use_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<goods_buy_and_use_c2s>
        PARSER = new com.google.protobuf.AbstractParser<goods_buy_and_use_c2s>() {
      @java.lang.Override
      public goods_buy_and_use_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<goods_buy_and_use_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<goods_buy_and_use_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_and_use_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface goods_gift_code_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.goods_gift_code_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>string gift_code = 1;</code>
     * @return The giftCode.
     */
    java.lang.String getGiftCode();
    /**
     * <code>string gift_code = 1;</code>
     * @return The bytes for giftCode.
     */
    com.google.protobuf.ByteString
        getGiftCodeBytes();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_gift_code_c2s}
   */
  public static final class goods_gift_code_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.goods_gift_code_c2s)
      goods_gift_code_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use goods_gift_code_c2s.newBuilder() to construct.
    private goods_gift_code_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private goods_gift_code_c2s() {
      giftCode_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new goods_gift_code_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s.Builder.class);
    }

    public static final int GIFT_CODE_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private volatile java.lang.Object giftCode_ = "";
    /**
     * <code>string gift_code = 1;</code>
     * @return The giftCode.
     */
    @java.lang.Override
    public java.lang.String getGiftCode() {
      java.lang.Object ref = giftCode_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        giftCode_ = s;
        return s;
      }
    }
    /**
     * <code>string gift_code = 1;</code>
     * @return The bytes for giftCode.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getGiftCodeBytes() {
      java.lang.Object ref = giftCode_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        giftCode_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(giftCode_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, giftCode_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(giftCode_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, giftCode_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s other = (org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s) obj;

      if (!getGiftCode()
          .equals(other.getGiftCode())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + GIFT_CODE_FIELD_NUMBER;
      hash = (53 * hash) + getGiftCode().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_gift_code_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.goods_gift_code_c2s)
        org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        giftCode_ = "";
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s build() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s result = new org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.giftCode_ = giftCode_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s.getDefaultInstance()) return this;
        if (!other.getGiftCode().isEmpty()) {
          giftCode_ = other.giftCode_;
          bitField0_ |= 0x00000001;
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                giftCode_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000001;
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.lang.Object giftCode_ = "";
      /**
       * <code>string gift_code = 1;</code>
       * @return The giftCode.
       */
      public java.lang.String getGiftCode() {
        java.lang.Object ref = giftCode_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          giftCode_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string gift_code = 1;</code>
       * @return The bytes for giftCode.
       */
      public com.google.protobuf.ByteString
          getGiftCodeBytes() {
        java.lang.Object ref = giftCode_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          giftCode_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string gift_code = 1;</code>
       * @param value The giftCode to set.
       * @return This builder for chaining.
       */
      public Builder setGiftCode(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        giftCode_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>string gift_code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearGiftCode() {
        giftCode_ = getDefaultInstance().getGiftCode();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>string gift_code = 1;</code>
       * @param value The bytes for giftCode to set.
       * @return This builder for chaining.
       */
      public Builder setGiftCodeBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        giftCode_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.goods_gift_code_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.goods_gift_code_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<goods_gift_code_c2s>
        PARSER = new com.google.protobuf.AbstractParser<goods_gift_code_c2s>() {
      @java.lang.Override
      public goods_gift_code_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<goods_gift_code_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<goods_gift_code_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface goods_gift_code_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.goods_gift_code_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    int getCode();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_gift_code_s2c}
   */
  public static final class goods_gift_code_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.goods_gift_code_s2c)
      goods_gift_code_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use goods_gift_code_s2c.newBuilder() to construct.
    private goods_gift_code_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private goods_gift_code_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new goods_gift_code_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c.Builder.class);
    }

    public static final int CODE_FIELD_NUMBER = 1;
    private int code_ = 0;
    /**
     * <code>int32 code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public int getCode() {
      return code_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (code_ != 0) {
        output.writeInt32(1, code_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (code_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, code_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c other = (org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c) obj;

      if (getCode()
          != other.getCode()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CODE_FIELD_NUMBER;
      hash = (53 * hash) + getCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_gift_code_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.goods_gift_code_s2c)
        org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        code_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c build() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c result = new org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.code_ = code_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c.getDefaultInstance()) return this;
        if (other.getCode() != 0) {
          setCode(other.getCode());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                code_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int code_ ;
      /**
       * <code>int32 code = 1;</code>
       * @return The code.
       */
      @java.lang.Override
      public int getCode() {
        return code_;
      }
      /**
       * <code>int32 code = 1;</code>
       * @param value The code to set.
       * @return This builder for chaining.
       */
      public Builder setCode(int value) {

        code_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 code = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearCode() {
        bitField0_ = (bitField0_ & ~0x00000001);
        code_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.goods_gift_code_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.goods_gift_code_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<goods_gift_code_s2c>
        PARSER = new com.google.protobuf.AbstractParser<goods_gift_code_s2c>() {
      @java.lang.Override
      public goods_gift_code_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<goods_gift_code_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<goods_gift_code_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgGoods.goods_gift_code_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface goods_show_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.goods_show_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> 
        getRewardListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward getRewardList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    int getRewardListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getRewardListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getRewardListOrBuilder(
        int index);

    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getExtListList();
    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getExtList(int index);
    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
     */
    int getExtListCount();
    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getExtListOrBuilderList();
    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getExtListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_show_s2c}
   */
  public static final class goods_show_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.goods_show_s2c)
      goods_show_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use goods_show_s2c.newBuilder() to construct.
    private goods_show_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private goods_show_s2c() {
      rewardList_ = java.util.Collections.emptyList();
      extList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new goods_show_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_show_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_show_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>int32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int REWARD_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> rewardList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getRewardListList() {
      return rewardList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getRewardListOrBuilderList() {
      return rewardList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    @java.lang.Override
    public int getRewardListCount() {
      return rewardList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward getRewardList(int index) {
      return rewardList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getRewardListOrBuilder(
        int index) {
      return rewardList_.get(index);
    }

    public static final int EXT_LIST_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> extList_;
    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getExtListList() {
      return extList_;
    }
    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getExtListOrBuilderList() {
      return extList_;
    }
    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
     */
    @java.lang.Override
    public int getExtListCount() {
      return extList_.size();
    }
    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getExtList(int index) {
      return extList_.get(index);
    }
    /**
     * <pre>
     * 额外参数
     * </pre>
     *
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getExtListOrBuilder(
        int index) {
      return extList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeInt32(1, type_);
      }
      for (int i = 0; i < rewardList_.size(); i++) {
        output.writeMessage(2, rewardList_.get(i));
      }
      for (int i = 0; i < extList_.size(); i++) {
        output.writeMessage(3, extList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, type_);
      }
      for (int i = 0; i < rewardList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, rewardList_.get(i));
      }
      for (int i = 0; i < extList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, extList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c other = (org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c) obj;

      if (getType()
          != other.getType()) return false;
      if (!getRewardListList()
          .equals(other.getRewardListList())) return false;
      if (!getExtListList()
          .equals(other.getExtListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      if (getRewardListCount() > 0) {
        hash = (37 * hash) + REWARD_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getRewardListList().hashCode();
      }
      if (getExtListCount() > 0) {
        hash = (37 * hash) + EXT_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getExtListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_show_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.goods_show_s2c)
        org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_show_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_show_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        if (rewardListBuilder_ == null) {
          rewardList_ = java.util.Collections.emptyList();
        } else {
          rewardList_ = null;
          rewardListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        if (extListBuilder_ == null) {
          extList_ = java.util.Collections.emptyList();
        } else {
          extList_ = null;
          extListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_show_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c build() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c result = new org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c result) {
        if (rewardListBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            rewardList_ = java.util.Collections.unmodifiableList(rewardList_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.rewardList_ = rewardList_;
        } else {
          result.rewardList_ = rewardListBuilder_.build();
        }
        if (extListBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            extList_ = java.util.Collections.unmodifiableList(extList_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.extList_ = extList_;
        } else {
          result.extList_ = extListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (rewardListBuilder_ == null) {
          if (!other.rewardList_.isEmpty()) {
            if (rewardList_.isEmpty()) {
              rewardList_ = other.rewardList_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureRewardListIsMutable();
              rewardList_.addAll(other.rewardList_);
            }
            onChanged();
          }
        } else {
          if (!other.rewardList_.isEmpty()) {
            if (rewardListBuilder_.isEmpty()) {
              rewardListBuilder_.dispose();
              rewardListBuilder_ = null;
              rewardList_ = other.rewardList_;
              bitField0_ = (bitField0_ & ~0x00000002);
              rewardListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRewardListFieldBuilder() : null;
            } else {
              rewardListBuilder_.addAllMessages(other.rewardList_);
            }
          }
        }
        if (extListBuilder_ == null) {
          if (!other.extList_.isEmpty()) {
            if (extList_.isEmpty()) {
              extList_ = other.extList_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureExtListIsMutable();
              extList_.addAll(other.extList_);
            }
            onChanged();
          }
        } else {
          if (!other.extList_.isEmpty()) {
            if (extListBuilder_.isEmpty()) {
              extListBuilder_.dispose();
              extListBuilder_ = null;
              extList_ = other.extList_;
              bitField0_ = (bitField0_ & ~0x00000004);
              extListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getExtListFieldBuilder() : null;
            } else {
              extListBuilder_.addAllMessages(other.extList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_reward m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_reward.parser(),
                        extensionRegistry);
                if (rewardListBuilder_ == null) {
                  ensureRewardListIsMutable();
                  rewardList_.add(m);
                } else {
                  rewardListBuilder_.addMessage(m);
                }
                break;
              } // case 18
              case 26: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (extListBuilder_ == null) {
                  ensureExtListIsMutable();
                  extList_.add(m);
                } else {
                  extListBuilder_.addMessage(m);
                }
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>int32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>int32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> rewardList_ =
        java.util.Collections.emptyList();
      private void ensureRewardListIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          rewardList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_reward>(rewardList_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> rewardListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getRewardListList() {
        if (rewardListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewardList_);
        } else {
          return rewardListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public int getRewardListCount() {
        if (rewardListBuilder_ == null) {
          return rewardList_.size();
        } else {
          return rewardListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward getRewardList(int index) {
        if (rewardListBuilder_ == null) {
          return rewardList_.get(index);
        } else {
          return rewardListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder setRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (rewardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardListIsMutable();
          rewardList_.set(index, value);
          onChanged();
        } else {
          rewardListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder setRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder addRewardList(org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (rewardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardListIsMutable();
          rewardList_.add(value);
          onChanged();
        } else {
          rewardListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder addRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (rewardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardListIsMutable();
          rewardList_.add(index, value);
          onChanged();
        } else {
          rewardListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder addRewardList(
          org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.add(builderForValue.build());
          onChanged();
        } else {
          rewardListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder addRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder addAllRewardList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_reward> values) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewardList_);
          onChanged();
        } else {
          rewardListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder clearRewardList() {
        if (rewardListBuilder_ == null) {
          rewardList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          rewardListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public Builder removeRewardList(int index) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.remove(index);
          onChanged();
        } else {
          rewardListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder getRewardListBuilder(
          int index) {
        return getRewardListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getRewardListOrBuilder(
          int index) {
        if (rewardListBuilder_ == null) {
          return rewardList_.get(index);  } else {
          return rewardListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
           getRewardListOrBuilderList() {
        if (rewardListBuilder_ != null) {
          return rewardListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewardList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addRewardListBuilder() {
        return getRewardListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addRewardListBuilder(
          int index) {
        return getRewardListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward.Builder> 
           getRewardListBuilderList() {
        return getRewardListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
          getRewardListFieldBuilder() {
        if (rewardListBuilder_ == null) {
          rewardListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder>(
                  rewardList_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          rewardList_ = null;
        }
        return rewardListBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> extList_ =
        java.util.Collections.emptyList();
      private void ensureExtListIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          extList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(extList_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> extListBuilder_;

      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getExtListList() {
        if (extListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(extList_);
        } else {
          return extListBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public int getExtListCount() {
        if (extListBuilder_ == null) {
          return extList_.size();
        } else {
          return extListBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getExtList(int index) {
        if (extListBuilder_ == null) {
          return extList_.get(index);
        } else {
          return extListBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public Builder setExtList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (extListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExtListIsMutable();
          extList_.set(index, value);
          onChanged();
        } else {
          extListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public Builder setExtList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (extListBuilder_ == null) {
          ensureExtListIsMutable();
          extList_.set(index, builderForValue.build());
          onChanged();
        } else {
          extListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public Builder addExtList(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (extListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExtListIsMutable();
          extList_.add(value);
          onChanged();
        } else {
          extListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public Builder addExtList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (extListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureExtListIsMutable();
          extList_.add(index, value);
          onChanged();
        } else {
          extListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public Builder addExtList(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (extListBuilder_ == null) {
          ensureExtListIsMutable();
          extList_.add(builderForValue.build());
          onChanged();
        } else {
          extListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public Builder addExtList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (extListBuilder_ == null) {
          ensureExtListIsMutable();
          extList_.add(index, builderForValue.build());
          onChanged();
        } else {
          extListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public Builder addAllExtList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (extListBuilder_ == null) {
          ensureExtListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, extList_);
          onChanged();
        } else {
          extListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public Builder clearExtList() {
        if (extListBuilder_ == null) {
          extList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          extListBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public Builder removeExtList(int index) {
        if (extListBuilder_ == null) {
          ensureExtListIsMutable();
          extList_.remove(index);
          onChanged();
        } else {
          extListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getExtListBuilder(
          int index) {
        return getExtListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getExtListOrBuilder(
          int index) {
        if (extListBuilder_ == null) {
          return extList_.get(index);  } else {
          return extListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getExtListOrBuilderList() {
        if (extListBuilder_ != null) {
          return extListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(extList_);
        }
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addExtListBuilder() {
        return getExtListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addExtListBuilder(
          int index) {
        return getExtListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <pre>
       * 额外参数
       * </pre>
       *
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value ext_list = 3;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getExtListBuilderList() {
        return getExtListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getExtListFieldBuilder() {
        if (extListBuilder_ == null) {
          extListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  extList_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          extList_ = null;
        }
        return extListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.goods_show_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.goods_show_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<goods_show_s2c>
        PARSER = new com.google.protobuf.AbstractParser<goods_show_s2c>() {
      @java.lang.Override
      public goods_show_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<goods_show_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<goods_show_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgGoods.goods_show_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface goods_buy_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.goods_buy_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 goods_id = 1;</code>
     * @return The goodsId.
     */
    int getGoodsId();

    /**
     * <code>int32 num = 2;</code>
     * @return The num.
     */
    int getNum();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_buy_c2s}
   */
  public static final class goods_buy_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.goods_buy_c2s)
      goods_buy_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use goods_buy_c2s.newBuilder() to construct.
    private goods_buy_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private goods_buy_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new goods_buy_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_buy_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_buy_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s.Builder.class);
    }

    public static final int GOODS_ID_FIELD_NUMBER = 1;
    private int goodsId_ = 0;
    /**
     * <code>int32 goods_id = 1;</code>
     * @return The goodsId.
     */
    @java.lang.Override
    public int getGoodsId() {
      return goodsId_;
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_ = 0;
    /**
     * <code>int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (goodsId_ != 0) {
        output.writeInt32(1, goodsId_);
      }
      if (num_ != 0) {
        output.writeInt32(2, num_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (goodsId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, goodsId_);
      }
      if (num_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s other = (org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s) obj;

      if (getGoodsId()
          != other.getGoodsId()) return false;
      if (getNum()
          != other.getNum()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + GOODS_ID_FIELD_NUMBER;
      hash = (53 * hash) + getGoodsId();
      hash = (37 * hash) + NUM_FIELD_NUMBER;
      hash = (53 * hash) + getNum();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_buy_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.goods_buy_c2s)
        org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_buy_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_buy_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        goodsId_ = 0;
        num_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_buy_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s build() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s result = new org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.goodsId_ = goodsId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s.getDefaultInstance()) return this;
        if (other.getGoodsId() != 0) {
          setGoodsId(other.getGoodsId());
        }
        if (other.getNum() != 0) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                goodsId_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                num_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int goodsId_ ;
      /**
       * <code>int32 goods_id = 1;</code>
       * @return The goodsId.
       */
      @java.lang.Override
      public int getGoodsId() {
        return goodsId_;
      }
      /**
       * <code>int32 goods_id = 1;</code>
       * @param value The goodsId to set.
       * @return This builder for chaining.
       */
      public Builder setGoodsId(int value) {

        goodsId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 goods_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearGoodsId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        goodsId_ = 0;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <code>int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <code>int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {

        num_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.goods_buy_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.goods_buy_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<goods_buy_c2s>
        PARSER = new com.google.protobuf.AbstractParser<goods_buy_c2s>() {
      @java.lang.Override
      public goods_buy_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<goods_buy_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<goods_buy_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgGoods.goods_buy_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface goods_compose_skill_pet_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.goods_compose_skill_pet_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>int32 once = 1;</code>
     * @return The once.
     */
    int getOnce();

    /**
     * <code>int32 type = 2;</code>
     * @return The type.
     */
    int getType();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getCostListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getCostList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
     */
    int getCostListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getCostListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getCostListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_compose_skill_pet_c2s}
   */
  public static final class goods_compose_skill_pet_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.goods_compose_skill_pet_c2s)
      goods_compose_skill_pet_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use goods_compose_skill_pet_c2s.newBuilder() to construct.
    private goods_compose_skill_pet_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private goods_compose_skill_pet_c2s() {
      costList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new goods_compose_skill_pet_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s.Builder.class);
    }

    public static final int ONCE_FIELD_NUMBER = 1;
    private int once_ = 0;
    /**
     * <code>int32 once = 1;</code>
     * @return The once.
     */
    @java.lang.Override
    public int getOnce() {
      return once_;
    }

    public static final int TYPE_FIELD_NUMBER = 2;
    private int type_ = 0;
    /**
     * <code>int32 type = 2;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int COST_LIST_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> costList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getCostListList() {
      return costList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getCostListOrBuilderList() {
      return costList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
     */
    @java.lang.Override
    public int getCostListCount() {
      return costList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getCostList(int index) {
      return costList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getCostListOrBuilder(
        int index) {
      return costList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (once_ != 0) {
        output.writeInt32(1, once_);
      }
      if (type_ != 0) {
        output.writeInt32(2, type_);
      }
      for (int i = 0; i < costList_.size(); i++) {
        output.writeMessage(3, costList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (once_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, once_);
      }
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, type_);
      }
      for (int i = 0; i < costList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, costList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s other = (org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s) obj;

      if (getOnce()
          != other.getOnce()) return false;
      if (getType()
          != other.getType()) return false;
      if (!getCostListList()
          .equals(other.getCostListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + ONCE_FIELD_NUMBER;
      hash = (53 * hash) + getOnce();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      if (getCostListCount() > 0) {
        hash = (37 * hash) + COST_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getCostListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_compose_skill_pet_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.goods_compose_skill_pet_c2s)
        org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        once_ = 0;
        type_ = 0;
        if (costListBuilder_ == null) {
          costList_ = java.util.Collections.emptyList();
        } else {
          costList_ = null;
          costListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s build() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s result = new org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s result) {
        if (costListBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            costList_ = java.util.Collections.unmodifiableList(costList_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.costList_ = costList_;
        } else {
          result.costList_ = costListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.once_ = once_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.type_ = type_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s.getDefaultInstance()) return this;
        if (other.getOnce() != 0) {
          setOnce(other.getOnce());
        }
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (costListBuilder_ == null) {
          if (!other.costList_.isEmpty()) {
            if (costList_.isEmpty()) {
              costList_ = other.costList_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureCostListIsMutable();
              costList_.addAll(other.costList_);
            }
            onChanged();
          }
        } else {
          if (!other.costList_.isEmpty()) {
            if (costListBuilder_.isEmpty()) {
              costListBuilder_.dispose();
              costListBuilder_ = null;
              costList_ = other.costList_;
              bitField0_ = (bitField0_ & ~0x00000004);
              costListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getCostListFieldBuilder() : null;
            } else {
              costListBuilder_.addAllMessages(other.costList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                once_ = input.readInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                type_ = input.readInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (costListBuilder_ == null) {
                  ensureCostListIsMutable();
                  costList_.add(m);
                } else {
                  costListBuilder_.addMessage(m);
                }
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int once_ ;
      /**
       * <code>int32 once = 1;</code>
       * @return The once.
       */
      @java.lang.Override
      public int getOnce() {
        return once_;
      }
      /**
       * <code>int32 once = 1;</code>
       * @param value The once to set.
       * @return This builder for chaining.
       */
      public Builder setOnce(int value) {

        once_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>int32 once = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOnce() {
        bitField0_ = (bitField0_ & ~0x00000001);
        once_ = 0;
        onChanged();
        return this;
      }

      private int type_ ;
      /**
       * <code>int32 type = 2;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>int32 type = 2;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>int32 type = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        type_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> costList_ =
        java.util.Collections.emptyList();
      private void ensureCostListIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          costList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(costList_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> costListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getCostListList() {
        if (costListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(costList_);
        } else {
          return costListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public int getCostListCount() {
        if (costListBuilder_ == null) {
          return costList_.size();
        } else {
          return costListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getCostList(int index) {
        if (costListBuilder_ == null) {
          return costList_.get(index);
        } else {
          return costListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public Builder setCostList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (costListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCostListIsMutable();
          costList_.set(index, value);
          onChanged();
        } else {
          costListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public Builder setCostList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (costListBuilder_ == null) {
          ensureCostListIsMutable();
          costList_.set(index, builderForValue.build());
          onChanged();
        } else {
          costListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public Builder addCostList(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (costListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCostListIsMutable();
          costList_.add(value);
          onChanged();
        } else {
          costListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public Builder addCostList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (costListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureCostListIsMutable();
          costList_.add(index, value);
          onChanged();
        } else {
          costListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public Builder addCostList(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (costListBuilder_ == null) {
          ensureCostListIsMutable();
          costList_.add(builderForValue.build());
          onChanged();
        } else {
          costListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public Builder addCostList(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (costListBuilder_ == null) {
          ensureCostListIsMutable();
          costList_.add(index, builderForValue.build());
          onChanged();
        } else {
          costListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public Builder addAllCostList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (costListBuilder_ == null) {
          ensureCostListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, costList_);
          onChanged();
        } else {
          costListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public Builder clearCostList() {
        if (costListBuilder_ == null) {
          costList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          costListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public Builder removeCostList(int index) {
        if (costListBuilder_ == null) {
          ensureCostListIsMutable();
          costList_.remove(index);
          onChanged();
        } else {
          costListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getCostListBuilder(
          int index) {
        return getCostListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getCostListOrBuilder(
          int index) {
        if (costListBuilder_ == null) {
          return costList_.get(index);  } else {
          return costListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getCostListOrBuilderList() {
        if (costListBuilder_ != null) {
          return costListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(costList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addCostListBuilder() {
        return getCostListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addCostListBuilder(
          int index) {
        return getCostListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value cost_list = 3;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getCostListBuilderList() {
        return getCostListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getCostListFieldBuilder() {
        if (costListBuilder_ == null) {
          costListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  costList_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          costList_ = null;
        }
        return costListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.goods_compose_skill_pet_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.goods_compose_skill_pet_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<goods_compose_skill_pet_c2s>
        PARSER = new com.google.protobuf.AbstractParser<goods_compose_skill_pet_c2s>() {
      @java.lang.Override
      public goods_compose_skill_pet_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<goods_compose_skill_pet_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<goods_compose_skill_pet_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface goods_compose_skill_pet_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.goods_compose_skill_pet_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> 
        getRewardListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_reward getRewardList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
     */
    int getRewardListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getRewardListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getRewardListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_compose_skill_pet_s2c}
   */
  public static final class goods_compose_skill_pet_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.goods_compose_skill_pet_s2c)
      goods_compose_skill_pet_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use goods_compose_skill_pet_s2c.newBuilder() to construct.
    private goods_compose_skill_pet_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private goods_compose_skill_pet_s2c() {
      rewardList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new goods_compose_skill_pet_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c.Builder.class);
    }

    public static final int REWARD_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> rewardList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getRewardListList() {
      return rewardList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
        getRewardListOrBuilderList() {
      return rewardList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
     */
    @java.lang.Override
    public int getRewardListCount() {
      return rewardList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_reward getRewardList(int index) {
      return rewardList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getRewardListOrBuilder(
        int index) {
      return rewardList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < rewardList_.size(); i++) {
        output.writeMessage(1, rewardList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < rewardList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, rewardList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c other = (org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c) obj;

      if (!getRewardListList()
          .equals(other.getRewardListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRewardListCount() > 0) {
        hash = (37 * hash) + REWARD_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getRewardListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.goods_compose_skill_pet_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.goods_compose_skill_pet_s2c)
        org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c.class, org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (rewardListBuilder_ == null) {
          rewardList_ = java.util.Collections.emptyList();
        } else {
          rewardList_ = null;
          rewardListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c build() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c result = new org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c result) {
        if (rewardListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            rewardList_ = java.util.Collections.unmodifiableList(rewardList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.rewardList_ = rewardList_;
        } else {
          result.rewardList_ = rewardListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c.getDefaultInstance()) return this;
        if (rewardListBuilder_ == null) {
          if (!other.rewardList_.isEmpty()) {
            if (rewardList_.isEmpty()) {
              rewardList_ = other.rewardList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRewardListIsMutable();
              rewardList_.addAll(other.rewardList_);
            }
            onChanged();
          }
        } else {
          if (!other.rewardList_.isEmpty()) {
            if (rewardListBuilder_.isEmpty()) {
              rewardListBuilder_.dispose();
              rewardListBuilder_ = null;
              rewardList_ = other.rewardList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              rewardListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRewardListFieldBuilder() : null;
            } else {
              rewardListBuilder_.addAllMessages(other.rewardList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_reward m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_reward.parser(),
                        extensionRegistry);
                if (rewardListBuilder_ == null) {
                  ensureRewardListIsMutable();
                  rewardList_.add(m);
                } else {
                  rewardListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> rewardList_ =
        java.util.Collections.emptyList();
      private void ensureRewardListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          rewardList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_reward>(rewardList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> rewardListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward> getRewardListList() {
        if (rewardListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(rewardList_);
        } else {
          return rewardListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public int getRewardListCount() {
        if (rewardListBuilder_ == null) {
          return rewardList_.size();
        } else {
          return rewardListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward getRewardList(int index) {
        if (rewardListBuilder_ == null) {
          return rewardList_.get(index);
        } else {
          return rewardListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public Builder setRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (rewardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardListIsMutable();
          rewardList_.set(index, value);
          onChanged();
        } else {
          rewardListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public Builder setRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.set(index, builderForValue.build());
          onChanged();
        } else {
          rewardListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public Builder addRewardList(org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (rewardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardListIsMutable();
          rewardList_.add(value);
          onChanged();
        } else {
          rewardListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public Builder addRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward value) {
        if (rewardListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRewardListIsMutable();
          rewardList_.add(index, value);
          onChanged();
        } else {
          rewardListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public Builder addRewardList(
          org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.add(builderForValue.build());
          onChanged();
        } else {
          rewardListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public Builder addRewardList(
          int index, org.gof.demo.worldsrv.msg.Define.p_reward.Builder builderForValue) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.add(index, builderForValue.build());
          onChanged();
        } else {
          rewardListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public Builder addAllRewardList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_reward> values) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, rewardList_);
          onChanged();
        } else {
          rewardListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public Builder clearRewardList() {
        if (rewardListBuilder_ == null) {
          rewardList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          rewardListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public Builder removeRewardList(int index) {
        if (rewardListBuilder_ == null) {
          ensureRewardListIsMutable();
          rewardList_.remove(index);
          onChanged();
        } else {
          rewardListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder getRewardListBuilder(
          int index) {
        return getRewardListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder getRewardListOrBuilder(
          int index) {
        if (rewardListBuilder_ == null) {
          return rewardList_.get(index);  } else {
          return rewardListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
           getRewardListOrBuilderList() {
        if (rewardListBuilder_ != null) {
          return rewardListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(rewardList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addRewardListBuilder() {
        return getRewardListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_reward.Builder addRewardListBuilder(
          int index) {
        return getRewardListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_reward.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_reward reward_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_reward.Builder> 
           getRewardListBuilderList() {
        return getRewardListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder> 
          getRewardListFieldBuilder() {
        if (rewardListBuilder_ == null) {
          rewardListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_reward, org.gof.demo.worldsrv.msg.Define.p_reward.Builder, org.gof.demo.worldsrv.msg.Define.p_rewardOrBuilder>(
                  rewardList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          rewardList_ = null;
        }
        return rewardListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.goods_compose_skill_pet_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.goods_compose_skill_pet_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<goods_compose_skill_pet_s2c>
        PARSER = new com.google.protobuf.AbstractParser<goods_compose_skill_pet_s2c>() {
      @java.lang.Override
      public goods_compose_skill_pet_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<goods_compose_skill_pet_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<goods_compose_skill_pet_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgGoods.goods_compose_skill_pet_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_goods_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_goods_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_goods_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_goods_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_goods_change_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_goods_change_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_goods_use_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_goods_use_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_goods_use_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_goods_use_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_goods_buy_and_use_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_goods_buy_and_use_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_goods_show_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_goods_show_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_goods_buy_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_goods_buy_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\017msg.goods.proto\022\031org.gof.demo.worldsrv" +
      ".msg\032\roptions.proto\032\014define.proto\"\027\n\016goo" +
      "ds_info_c2s:\005\210\303\032\201\010\"O\n\016goods_info_s2c\0226\n\n" +
      "goods_list\030\001 \003(\0132\".org.gof.demo.worldsrv" +
      ".msg.p_goods:\005\210\303\032\201\010\"a\n\020goods_change_s2c\022" +
      "\016\n\006source\030\001 \001(\005\0226\n\ngoods_list\030\002 \003(\0132\".or" +
      "g.gof.demo.worldsrv.msg.p_goods:\005\210\303\032\202\010\"\230" +
      "\001\n\rgoods_use_c2s\022\020\n\010goods_id\030\001 \001(\004\022\013\n\003nu" +
      "m\030\002 \001(\005\022\023\n\013choose_gtid\030\003 \001(\005\022\022\n\nchoose_u" +
      "se\030\004 \001(\005\0228\n\010ext_list\030\005 \003(\0132&.org.gof.dem" +
      "o.worldsrv.msg.p_key_value:\005\210\303\032\203\010\"o\n\rgoo" +
      "ds_use_s2c\022\014\n\004code\030\001 \001(\005\022\020\n\010goods_id\030\002 \001" +
      "(\004\0227\n\ngoods_list\030\003 \003(\0132#.org.gof.demo.wo" +
      "rldsrv.msg.p_reward:\005\210\303\032\203\010\"=\n\025goods_buy_" +
      "and_use_c2s\022\020\n\010goods_id\030\001 \001(\005\022\013\n\003num\030\002 \001" +
      "(\005:\005\210\303\032\204\010\"/\n\023goods_gift_code_c2s\022\021\n\tgift" +
      "_code\030\001 \001(\t:\005\210\303\032\205\010\"*\n\023goods_gift_code_s2" +
      "c\022\014\n\004code\030\001 \001(\005:\005\210\303\032\205\010\"\231\001\n\016goods_show_s2" +
      "c\022\014\n\004type\030\001 \001(\005\0228\n\013reward_list\030\002 \003(\0132#.o" +
      "rg.gof.demo.worldsrv.msg.p_reward\0228\n\010ext" +
      "_list\030\003 \003(\0132&.org.gof.demo.worldsrv.msg." +
      "p_key_value:\005\210\303\032\206\010\"5\n\rgoods_buy_c2s\022\020\n\010g" +
      "oods_id\030\001 \001(\005\022\013\n\003num\030\002 \001(\005:\005\210\303\032\207\010\"{\n\033goo" +
      "ds_compose_skill_pet_c2s\022\014\n\004once\030\001 \001(\005\022\014" +
      "\n\004type\030\002 \001(\005\0229\n\tcost_list\030\003 \003(\0132&.org.go" +
      "f.demo.worldsrv.msg.p_key_value:\005\210\303\032\210\010\"^" +
      "\n\033goods_compose_skill_pet_s2c\0228\n\013reward_" +
      "list\030\001 \003(\0132#.org.gof.demo.worldsrv.msg.p" +
      "_reward:\005\210\303\032\210\010b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_goods_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_goods_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_goods_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_goods_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_goods_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_goods_info_s2c_descriptor,
        new java.lang.String[] { "GoodsList", });
    internal_static_org_gof_demo_worldsrv_msg_goods_change_s2c_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_goods_change_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_goods_change_s2c_descriptor,
        new java.lang.String[] { "Source", "GoodsList", });
    internal_static_org_gof_demo_worldsrv_msg_goods_use_c2s_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_goods_use_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_goods_use_c2s_descriptor,
        new java.lang.String[] { "GoodsId", "Num", "ChooseGtid", "ChooseUse", "ExtList", });
    internal_static_org_gof_demo_worldsrv_msg_goods_use_s2c_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_goods_use_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_goods_use_s2c_descriptor,
        new java.lang.String[] { "Code", "GoodsId", "GoodsList", });
    internal_static_org_gof_demo_worldsrv_msg_goods_buy_and_use_c2s_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_goods_buy_and_use_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_goods_buy_and_use_c2s_descriptor,
        new java.lang.String[] { "GoodsId", "Num", });
    internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_c2s_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_c2s_descriptor,
        new java.lang.String[] { "GiftCode", });
    internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_s2c_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_goods_gift_code_s2c_descriptor,
        new java.lang.String[] { "Code", });
    internal_static_org_gof_demo_worldsrv_msg_goods_show_s2c_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_org_gof_demo_worldsrv_msg_goods_show_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_goods_show_s2c_descriptor,
        new java.lang.String[] { "Type", "RewardList", "ExtList", });
    internal_static_org_gof_demo_worldsrv_msg_goods_buy_c2s_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_org_gof_demo_worldsrv_msg_goods_buy_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_goods_buy_c2s_descriptor,
        new java.lang.String[] { "GoodsId", "Num", });
    internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_c2s_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_c2s_descriptor,
        new java.lang.String[] { "Once", "Type", "CostList", });
    internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_s2c_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_goods_compose_skill_pet_s2c_descriptor,
        new java.lang.String[] { "RewardList", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
