// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.science.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgScience {
  private MsgScience() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface science_info_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.science_info_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.science_info_c2s}
   */
  public static final class science_info_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.science_info_c2s)
      science_info_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use science_info_c2s.newBuilder() to construct.
    private science_info_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private science_info_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new science_info_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_info_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_info_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s.class, org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s other = (org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.science_info_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.science_info_c2s)
        org.gof.demo.worldsrv.msg.MsgScience.science_info_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_info_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_info_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s.class, org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_info_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s build() {
        org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s result = new org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.science_info_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.science_info_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<science_info_c2s>
        PARSER = new com.google.protobuf.AbstractParser<science_info_c2s>() {
      @java.lang.Override
      public science_info_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<science_info_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<science_info_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgScience.science_info_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface science_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.science_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_science_tree> 
        getScienceInfoList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_science_tree getScienceInfo(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
     */
    int getScienceInfoCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder> 
        getScienceInfoOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder getScienceInfoOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.science_info_s2c}
   */
  public static final class science_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.science_info_s2c)
      science_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use science_info_s2c.newBuilder() to construct.
    private science_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private science_info_s2c() {
      scienceInfo_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new science_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c.class, org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c.Builder.class);
    }

    public static final int SCIENCE_INFO_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_science_tree> scienceInfo_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_science_tree> getScienceInfoList() {
      return scienceInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder> 
        getScienceInfoOrBuilderList() {
      return scienceInfo_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
     */
    @java.lang.Override
    public int getScienceInfoCount() {
      return scienceInfo_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_science_tree getScienceInfo(int index) {
      return scienceInfo_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder getScienceInfoOrBuilder(
        int index) {
      return scienceInfo_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < scienceInfo_.size(); i++) {
        output.writeMessage(1, scienceInfo_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < scienceInfo_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, scienceInfo_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c other = (org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c) obj;

      if (!getScienceInfoList()
          .equals(other.getScienceInfoList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getScienceInfoCount() > 0) {
        hash = (37 * hash) + SCIENCE_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getScienceInfoList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.science_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.science_info_s2c)
        org.gof.demo.worldsrv.msg.MsgScience.science_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c.class, org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (scienceInfoBuilder_ == null) {
          scienceInfo_ = java.util.Collections.emptyList();
        } else {
          scienceInfo_ = null;
          scienceInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c result = new org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c result) {
        if (scienceInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            scienceInfo_ = java.util.Collections.unmodifiableList(scienceInfo_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.scienceInfo_ = scienceInfo_;
        } else {
          result.scienceInfo_ = scienceInfoBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c.getDefaultInstance()) return this;
        if (scienceInfoBuilder_ == null) {
          if (!other.scienceInfo_.isEmpty()) {
            if (scienceInfo_.isEmpty()) {
              scienceInfo_ = other.scienceInfo_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureScienceInfoIsMutable();
              scienceInfo_.addAll(other.scienceInfo_);
            }
            onChanged();
          }
        } else {
          if (!other.scienceInfo_.isEmpty()) {
            if (scienceInfoBuilder_.isEmpty()) {
              scienceInfoBuilder_.dispose();
              scienceInfoBuilder_ = null;
              scienceInfo_ = other.scienceInfo_;
              bitField0_ = (bitField0_ & ~0x00000001);
              scienceInfoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getScienceInfoFieldBuilder() : null;
            } else {
              scienceInfoBuilder_.addAllMessages(other.scienceInfo_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_science_tree m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_science_tree.parser(),
                        extensionRegistry);
                if (scienceInfoBuilder_ == null) {
                  ensureScienceInfoIsMutable();
                  scienceInfo_.add(m);
                } else {
                  scienceInfoBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_science_tree> scienceInfo_ =
        java.util.Collections.emptyList();
      private void ensureScienceInfoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          scienceInfo_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_science_tree>(scienceInfo_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_science_tree, org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder, org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder> scienceInfoBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_science_tree> getScienceInfoList() {
        if (scienceInfoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(scienceInfo_);
        } else {
          return scienceInfoBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public int getScienceInfoCount() {
        if (scienceInfoBuilder_ == null) {
          return scienceInfo_.size();
        } else {
          return scienceInfoBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_science_tree getScienceInfo(int index) {
        if (scienceInfoBuilder_ == null) {
          return scienceInfo_.get(index);
        } else {
          return scienceInfoBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public Builder setScienceInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_science_tree value) {
        if (scienceInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureScienceInfoIsMutable();
          scienceInfo_.set(index, value);
          onChanged();
        } else {
          scienceInfoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public Builder setScienceInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder builderForValue) {
        if (scienceInfoBuilder_ == null) {
          ensureScienceInfoIsMutable();
          scienceInfo_.set(index, builderForValue.build());
          onChanged();
        } else {
          scienceInfoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public Builder addScienceInfo(org.gof.demo.worldsrv.msg.Define.p_science_tree value) {
        if (scienceInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureScienceInfoIsMutable();
          scienceInfo_.add(value);
          onChanged();
        } else {
          scienceInfoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public Builder addScienceInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_science_tree value) {
        if (scienceInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureScienceInfoIsMutable();
          scienceInfo_.add(index, value);
          onChanged();
        } else {
          scienceInfoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public Builder addScienceInfo(
          org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder builderForValue) {
        if (scienceInfoBuilder_ == null) {
          ensureScienceInfoIsMutable();
          scienceInfo_.add(builderForValue.build());
          onChanged();
        } else {
          scienceInfoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public Builder addScienceInfo(
          int index, org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder builderForValue) {
        if (scienceInfoBuilder_ == null) {
          ensureScienceInfoIsMutable();
          scienceInfo_.add(index, builderForValue.build());
          onChanged();
        } else {
          scienceInfoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public Builder addAllScienceInfo(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_science_tree> values) {
        if (scienceInfoBuilder_ == null) {
          ensureScienceInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, scienceInfo_);
          onChanged();
        } else {
          scienceInfoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public Builder clearScienceInfo() {
        if (scienceInfoBuilder_ == null) {
          scienceInfo_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          scienceInfoBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public Builder removeScienceInfo(int index) {
        if (scienceInfoBuilder_ == null) {
          ensureScienceInfoIsMutable();
          scienceInfo_.remove(index);
          onChanged();
        } else {
          scienceInfoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder getScienceInfoBuilder(
          int index) {
        return getScienceInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder getScienceInfoOrBuilder(
          int index) {
        if (scienceInfoBuilder_ == null) {
          return scienceInfo_.get(index);  } else {
          return scienceInfoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder> 
           getScienceInfoOrBuilderList() {
        if (scienceInfoBuilder_ != null) {
          return scienceInfoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(scienceInfo_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder addScienceInfoBuilder() {
        return getScienceInfoFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_science_tree.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder addScienceInfoBuilder(
          int index) {
        return getScienceInfoFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_science_tree.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree science_info = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder> 
           getScienceInfoBuilderList() {
        return getScienceInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_science_tree, org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder, org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder> 
          getScienceInfoFieldBuilder() {
        if (scienceInfoBuilder_ == null) {
          scienceInfoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_science_tree, org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder, org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder>(
                  scienceInfo_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          scienceInfo_ = null;
        }
        return scienceInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.science_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.science_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<science_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<science_info_s2c>() {
      @java.lang.Override
      public science_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<science_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<science_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgScience.science_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface science_update_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.science_update_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_science_tree> 
        getUpdateListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_science_tree getUpdateList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
     */
    int getUpdateListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder> 
        getUpdateListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder getUpdateListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.science_update_s2c}
   */
  public static final class science_update_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.science_update_s2c)
      science_update_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use science_update_s2c.newBuilder() to construct.
    private science_update_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private science_update_s2c() {
      updateList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new science_update_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_update_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_update_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c.class, org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c.Builder.class);
    }

    public static final int UPDATE_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_science_tree> updateList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_science_tree> getUpdateListList() {
      return updateList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder> 
        getUpdateListOrBuilderList() {
      return updateList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
     */
    @java.lang.Override
    public int getUpdateListCount() {
      return updateList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_science_tree getUpdateList(int index) {
      return updateList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder getUpdateListOrBuilder(
        int index) {
      return updateList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < updateList_.size(); i++) {
        output.writeMessage(1, updateList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < updateList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, updateList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c other = (org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c) obj;

      if (!getUpdateListList()
          .equals(other.getUpdateListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getUpdateListCount() > 0) {
        hash = (37 * hash) + UPDATE_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getUpdateListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.science_update_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.science_update_s2c)
        org.gof.demo.worldsrv.msg.MsgScience.science_update_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_update_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_update_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c.class, org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (updateListBuilder_ == null) {
          updateList_ = java.util.Collections.emptyList();
        } else {
          updateList_ = null;
          updateListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_update_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c build() {
        org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c result = new org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c result) {
        if (updateListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            updateList_ = java.util.Collections.unmodifiableList(updateList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.updateList_ = updateList_;
        } else {
          result.updateList_ = updateListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c.getDefaultInstance()) return this;
        if (updateListBuilder_ == null) {
          if (!other.updateList_.isEmpty()) {
            if (updateList_.isEmpty()) {
              updateList_ = other.updateList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureUpdateListIsMutable();
              updateList_.addAll(other.updateList_);
            }
            onChanged();
          }
        } else {
          if (!other.updateList_.isEmpty()) {
            if (updateListBuilder_.isEmpty()) {
              updateListBuilder_.dispose();
              updateListBuilder_ = null;
              updateList_ = other.updateList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              updateListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getUpdateListFieldBuilder() : null;
            } else {
              updateListBuilder_.addAllMessages(other.updateList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_science_tree m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_science_tree.parser(),
                        extensionRegistry);
                if (updateListBuilder_ == null) {
                  ensureUpdateListIsMutable();
                  updateList_.add(m);
                } else {
                  updateListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_science_tree> updateList_ =
        java.util.Collections.emptyList();
      private void ensureUpdateListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          updateList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_science_tree>(updateList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_science_tree, org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder, org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder> updateListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_science_tree> getUpdateListList() {
        if (updateListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(updateList_);
        } else {
          return updateListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public int getUpdateListCount() {
        if (updateListBuilder_ == null) {
          return updateList_.size();
        } else {
          return updateListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_science_tree getUpdateList(int index) {
        if (updateListBuilder_ == null) {
          return updateList_.get(index);
        } else {
          return updateListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public Builder setUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_science_tree value) {
        if (updateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateListIsMutable();
          updateList_.set(index, value);
          onChanged();
        } else {
          updateListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public Builder setUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder builderForValue) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          updateList_.set(index, builderForValue.build());
          onChanged();
        } else {
          updateListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public Builder addUpdateList(org.gof.demo.worldsrv.msg.Define.p_science_tree value) {
        if (updateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateListIsMutable();
          updateList_.add(value);
          onChanged();
        } else {
          updateListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public Builder addUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_science_tree value) {
        if (updateListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureUpdateListIsMutable();
          updateList_.add(index, value);
          onChanged();
        } else {
          updateListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public Builder addUpdateList(
          org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder builderForValue) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          updateList_.add(builderForValue.build());
          onChanged();
        } else {
          updateListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public Builder addUpdateList(
          int index, org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder builderForValue) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          updateList_.add(index, builderForValue.build());
          onChanged();
        } else {
          updateListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public Builder addAllUpdateList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_science_tree> values) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, updateList_);
          onChanged();
        } else {
          updateListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public Builder clearUpdateList() {
        if (updateListBuilder_ == null) {
          updateList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          updateListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public Builder removeUpdateList(int index) {
        if (updateListBuilder_ == null) {
          ensureUpdateListIsMutable();
          updateList_.remove(index);
          onChanged();
        } else {
          updateListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder getUpdateListBuilder(
          int index) {
        return getUpdateListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder getUpdateListOrBuilder(
          int index) {
        if (updateListBuilder_ == null) {
          return updateList_.get(index);  } else {
          return updateListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder> 
           getUpdateListOrBuilderList() {
        if (updateListBuilder_ != null) {
          return updateListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(updateList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder addUpdateListBuilder() {
        return getUpdateListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_science_tree.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder addUpdateListBuilder(
          int index) {
        return getUpdateListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_science_tree.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_science_tree update_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder> 
           getUpdateListBuilderList() {
        return getUpdateListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_science_tree, org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder, org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder> 
          getUpdateListFieldBuilder() {
        if (updateListBuilder_ == null) {
          updateListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_science_tree, org.gof.demo.worldsrv.msg.Define.p_science_tree.Builder, org.gof.demo.worldsrv.msg.Define.p_science_treeOrBuilder>(
                  updateList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          updateList_ = null;
        }
        return updateListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.science_update_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.science_update_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<science_update_s2c>
        PARSER = new com.google.protobuf.AbstractParser<science_update_s2c>() {
      @java.lang.Override
      public science_update_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<science_update_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<science_update_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgScience.science_update_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface science_research_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.science_research_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 science_id = 1;</code>
     * @return The scienceId.
     */
    int getScienceId();

    /**
     * <code>uint32 science_lv = 2;</code>
     * @return The scienceLv.
     */
    int getScienceLv();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.science_research_c2s}
   */
  public static final class science_research_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.science_research_c2s)
      science_research_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use science_research_c2s.newBuilder() to construct.
    private science_research_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private science_research_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new science_research_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_research_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_research_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s.class, org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s.Builder.class);
    }

    public static final int SCIENCE_ID_FIELD_NUMBER = 1;
    private int scienceId_ = 0;
    /**
     * <code>uint32 science_id = 1;</code>
     * @return The scienceId.
     */
    @java.lang.Override
    public int getScienceId() {
      return scienceId_;
    }

    public static final int SCIENCE_LV_FIELD_NUMBER = 2;
    private int scienceLv_ = 0;
    /**
     * <code>uint32 science_lv = 2;</code>
     * @return The scienceLv.
     */
    @java.lang.Override
    public int getScienceLv() {
      return scienceLv_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (scienceId_ != 0) {
        output.writeUInt32(1, scienceId_);
      }
      if (scienceLv_ != 0) {
        output.writeUInt32(2, scienceLv_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (scienceId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, scienceId_);
      }
      if (scienceLv_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, scienceLv_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s other = (org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s) obj;

      if (getScienceId()
          != other.getScienceId()) return false;
      if (getScienceLv()
          != other.getScienceLv()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SCIENCE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getScienceId();
      hash = (37 * hash) + SCIENCE_LV_FIELD_NUMBER;
      hash = (53 * hash) + getScienceLv();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.science_research_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.science_research_c2s)
        org.gof.demo.worldsrv.msg.MsgScience.science_research_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_research_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_research_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s.class, org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        scienceId_ = 0;
        scienceLv_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_research_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s build() {
        org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s result = new org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.scienceId_ = scienceId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.scienceLv_ = scienceLv_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s.getDefaultInstance()) return this;
        if (other.getScienceId() != 0) {
          setScienceId(other.getScienceId());
        }
        if (other.getScienceLv() != 0) {
          setScienceLv(other.getScienceLv());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                scienceId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                scienceLv_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int scienceId_ ;
      /**
       * <code>uint32 science_id = 1;</code>
       * @return The scienceId.
       */
      @java.lang.Override
      public int getScienceId() {
        return scienceId_;
      }
      /**
       * <code>uint32 science_id = 1;</code>
       * @param value The scienceId to set.
       * @return This builder for chaining.
       */
      public Builder setScienceId(int value) {

        scienceId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 science_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearScienceId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        scienceId_ = 0;
        onChanged();
        return this;
      }

      private int scienceLv_ ;
      /**
       * <code>uint32 science_lv = 2;</code>
       * @return The scienceLv.
       */
      @java.lang.Override
      public int getScienceLv() {
        return scienceLv_;
      }
      /**
       * <code>uint32 science_lv = 2;</code>
       * @param value The scienceLv to set.
       * @return This builder for chaining.
       */
      public Builder setScienceLv(int value) {

        scienceLv_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 science_lv = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearScienceLv() {
        bitField0_ = (bitField0_ & ~0x00000002);
        scienceLv_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.science_research_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.science_research_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<science_research_c2s>
        PARSER = new com.google.protobuf.AbstractParser<science_research_c2s>() {
      @java.lang.Override
      public science_research_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<science_research_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<science_research_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgScience.science_research_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface science_finish_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.science_finish_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 science_id = 1;</code>
     * @return The scienceId.
     */
    int getScienceId();

    /**
     * <code>uint32 science_lv = 2;</code>
     * @return The scienceLv.
     */
    int getScienceLv();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.science_finish_s2c}
   */
  public static final class science_finish_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.science_finish_s2c)
      science_finish_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use science_finish_s2c.newBuilder() to construct.
    private science_finish_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private science_finish_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new science_finish_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_finish_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_finish_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c.class, org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c.Builder.class);
    }

    public static final int SCIENCE_ID_FIELD_NUMBER = 1;
    private int scienceId_ = 0;
    /**
     * <code>uint32 science_id = 1;</code>
     * @return The scienceId.
     */
    @java.lang.Override
    public int getScienceId() {
      return scienceId_;
    }

    public static final int SCIENCE_LV_FIELD_NUMBER = 2;
    private int scienceLv_ = 0;
    /**
     * <code>uint32 science_lv = 2;</code>
     * @return The scienceLv.
     */
    @java.lang.Override
    public int getScienceLv() {
      return scienceLv_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (scienceId_ != 0) {
        output.writeUInt32(1, scienceId_);
      }
      if (scienceLv_ != 0) {
        output.writeUInt32(2, scienceLv_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (scienceId_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, scienceId_);
      }
      if (scienceLv_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, scienceLv_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c other = (org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c) obj;

      if (getScienceId()
          != other.getScienceId()) return false;
      if (getScienceLv()
          != other.getScienceLv()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + SCIENCE_ID_FIELD_NUMBER;
      hash = (53 * hash) + getScienceId();
      hash = (37 * hash) + SCIENCE_LV_FIELD_NUMBER;
      hash = (53 * hash) + getScienceLv();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.science_finish_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.science_finish_s2c)
        org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_finish_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_finish_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c.class, org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        scienceId_ = 0;
        scienceLv_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgScience.internal_static_org_gof_demo_worldsrv_msg_science_finish_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c build() {
        org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c result = new org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.scienceId_ = scienceId_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.scienceLv_ = scienceLv_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c.getDefaultInstance()) return this;
        if (other.getScienceId() != 0) {
          setScienceId(other.getScienceId());
        }
        if (other.getScienceLv() != 0) {
          setScienceLv(other.getScienceLv());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                scienceId_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                scienceLv_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int scienceId_ ;
      /**
       * <code>uint32 science_id = 1;</code>
       * @return The scienceId.
       */
      @java.lang.Override
      public int getScienceId() {
        return scienceId_;
      }
      /**
       * <code>uint32 science_id = 1;</code>
       * @param value The scienceId to set.
       * @return This builder for chaining.
       */
      public Builder setScienceId(int value) {

        scienceId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 science_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearScienceId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        scienceId_ = 0;
        onChanged();
        return this;
      }

      private int scienceLv_ ;
      /**
       * <code>uint32 science_lv = 2;</code>
       * @return The scienceLv.
       */
      @java.lang.Override
      public int getScienceLv() {
        return scienceLv_;
      }
      /**
       * <code>uint32 science_lv = 2;</code>
       * @param value The scienceLv to set.
       * @return This builder for chaining.
       */
      public Builder setScienceLv(int value) {

        scienceLv_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 science_lv = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearScienceLv() {
        bitField0_ = (bitField0_ & ~0x00000002);
        scienceLv_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.science_finish_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.science_finish_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<science_finish_s2c>
        PARSER = new com.google.protobuf.AbstractParser<science_finish_s2c>() {
      @java.lang.Override
      public science_finish_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<science_finish_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<science_finish_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgScience.science_finish_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_science_info_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_science_info_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_science_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_science_info_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_science_update_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_science_update_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_science_research_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_science_research_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_science_finish_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_science_finish_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\021msg.science.proto\022\031org.gof.demo.worlds" +
      "rv.msg\032\roptions.proto\032\014define.proto\"\031\n\020s" +
      "cience_info_c2s:\005\210\303\032\201\026\"Z\n\020science_info_s" +
      "2c\022?\n\014science_info\030\001 \003(\0132).org.gof.demo." +
      "worldsrv.msg.p_science_tree:\005\210\303\032\201\026\"[\n\022sc" +
      "ience_update_s2c\022>\n\013update_list\030\001 \003(\0132)." +
      "org.gof.demo.worldsrv.msg.p_science_tree" +
      ":\005\210\303\032\202\026\"E\n\024science_research_c2s\022\022\n\nscien" +
      "ce_id\030\001 \001(\r\022\022\n\nscience_lv\030\002 \001(\r:\005\210\303\032\203\026\"C" +
      "\n\022science_finish_s2c\022\022\n\nscience_id\030\001 \001(\r" +
      "\022\022\n\nscience_lv\030\002 \001(\r:\005\210\303\032\204\026b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_science_info_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_science_info_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_science_info_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_science_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_science_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_science_info_s2c_descriptor,
        new java.lang.String[] { "ScienceInfo", });
    internal_static_org_gof_demo_worldsrv_msg_science_update_s2c_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_science_update_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_science_update_s2c_descriptor,
        new java.lang.String[] { "UpdateList", });
    internal_static_org_gof_demo_worldsrv_msg_science_research_c2s_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_science_research_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_science_research_c2s_descriptor,
        new java.lang.String[] { "ScienceId", "ScienceLv", });
    internal_static_org_gof_demo_worldsrv_msg_science_finish_s2c_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_science_finish_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_science_finish_s2c_descriptor,
        new java.lang.String[] { "ScienceId", "ScienceLv", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
