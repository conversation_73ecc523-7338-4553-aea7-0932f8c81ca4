// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: msg.chat.proto

// Protobuf Java Version: 3.25.3
package org.gof.demo.worldsrv.msg;

public final class MsgChat {
  private MsgChat() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface chat_message_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_message_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 channel = 1;</code>
     * @return The channel.
     */
    int getChannel();

    /**
     * <code>uint64 target_id = 2;</code>
     * @return The targetId.
     */
    long getTargetId();

    /**
     * <code>uint32 content_type = 3;</code>
     * @return The contentType.
     */
    int getContentType();

    /**
     * <code>string content = 4;</code>
     * @return The content.
     */
    java.lang.String getContent();
    /**
     * <code>string content = 4;</code>
     * @return The bytes for content.
     */
    com.google.protobuf.ByteString
        getContentBytes();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_link> 
        getLinksList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_link getLinks(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
     */
    int getLinksCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_linkOrBuilder> 
        getLinksOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_linkOrBuilder getLinksOrBuilder(
        int index);

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value_string> 
        getArgsList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value_string getArgs(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
     */
    int getArgsCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_value_stringOrBuilder> 
        getArgsOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value_stringOrBuilder getArgsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_message_c2s}
   */
  public static final class chat_message_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_message_c2s)
      chat_message_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_message_c2s.newBuilder() to construct.
    private chat_message_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_message_c2s() {
      content_ = "";
      links_ = java.util.Collections.emptyList();
      args_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_message_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_message_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_message_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s.class, org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s.Builder.class);
    }

    public static final int CHANNEL_FIELD_NUMBER = 1;
    private int channel_ = 0;
    /**
     * <code>uint32 channel = 1;</code>
     * @return The channel.
     */
    @java.lang.Override
    public int getChannel() {
      return channel_;
    }

    public static final int TARGET_ID_FIELD_NUMBER = 2;
    private long targetId_ = 0L;
    /**
     * <code>uint64 target_id = 2;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    public static final int CONTENT_TYPE_FIELD_NUMBER = 3;
    private int contentType_ = 0;
    /**
     * <code>uint32 content_type = 3;</code>
     * @return The contentType.
     */
    @java.lang.Override
    public int getContentType() {
      return contentType_;
    }

    public static final int CONTENT_FIELD_NUMBER = 4;
    @SuppressWarnings("serial")
    private volatile java.lang.Object content_ = "";
    /**
     * <code>string content = 4;</code>
     * @return The content.
     */
    @java.lang.Override
    public java.lang.String getContent() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        content_ = s;
        return s;
      }
    }
    /**
     * <code>string content = 4;</code>
     * @return The bytes for content.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getContentBytes() {
      java.lang.Object ref = content_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        content_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int LINKS_FIELD_NUMBER = 5;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_link> links_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_link> getLinksList() {
      return links_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_linkOrBuilder> 
        getLinksOrBuilderList() {
      return links_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
     */
    @java.lang.Override
    public int getLinksCount() {
      return links_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_link getLinks(int index) {
      return links_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_linkOrBuilder getLinksOrBuilder(
        int index) {
      return links_.get(index);
    }

    public static final int ARGS_FIELD_NUMBER = 6;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value_string> args_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value_string> getArgsList() {
      return args_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_value_stringOrBuilder> 
        getArgsOrBuilderList() {
      return args_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
     */
    @java.lang.Override
    public int getArgsCount() {
      return args_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value_string getArgs(int index) {
      return args_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value_stringOrBuilder getArgsOrBuilder(
        int index) {
      return args_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (channel_ != 0) {
        output.writeUInt32(1, channel_);
      }
      if (targetId_ != 0L) {
        output.writeUInt64(2, targetId_);
      }
      if (contentType_ != 0) {
        output.writeUInt32(3, contentType_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(content_)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, content_);
      }
      for (int i = 0; i < links_.size(); i++) {
        output.writeMessage(5, links_.get(i));
      }
      for (int i = 0; i < args_.size(); i++) {
        output.writeMessage(6, args_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (channel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, channel_);
      }
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, targetId_);
      }
      if (contentType_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(3, contentType_);
      }
      if (!com.google.protobuf.GeneratedMessageV3.isStringEmpty(content_)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, content_);
      }
      for (int i = 0; i < links_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, links_.get(i));
      }
      for (int i = 0; i < args_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, args_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s other = (org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s) obj;

      if (getChannel()
          != other.getChannel()) return false;
      if (getTargetId()
          != other.getTargetId()) return false;
      if (getContentType()
          != other.getContentType()) return false;
      if (!getContent()
          .equals(other.getContent())) return false;
      if (!getLinksList()
          .equals(other.getLinksList())) return false;
      if (!getArgsList()
          .equals(other.getArgsList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
      hash = (53 * hash) + getChannel();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      hash = (37 * hash) + CONTENT_TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getContentType();
      hash = (37 * hash) + CONTENT_FIELD_NUMBER;
      hash = (53 * hash) + getContent().hashCode();
      if (getLinksCount() > 0) {
        hash = (37 * hash) + LINKS_FIELD_NUMBER;
        hash = (53 * hash) + getLinksList().hashCode();
      }
      if (getArgsCount() > 0) {
        hash = (37 * hash) + ARGS_FIELD_NUMBER;
        hash = (53 * hash) + getArgsList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_message_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_message_c2s)
        org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_message_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_message_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s.class, org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        channel_ = 0;
        targetId_ = 0L;
        contentType_ = 0;
        content_ = "";
        if (linksBuilder_ == null) {
          links_ = java.util.Collections.emptyList();
        } else {
          links_ = null;
          linksBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        if (argsBuilder_ == null) {
          args_ = java.util.Collections.emptyList();
        } else {
          args_ = null;
          argsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_message_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s build() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s result = new org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s result) {
        if (linksBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0)) {
            links_ = java.util.Collections.unmodifiableList(links_);
            bitField0_ = (bitField0_ & ~0x00000010);
          }
          result.links_ = links_;
        } else {
          result.links_ = linksBuilder_.build();
        }
        if (argsBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0)) {
            args_ = java.util.Collections.unmodifiableList(args_);
            bitField0_ = (bitField0_ & ~0x00000020);
          }
          result.args_ = args_;
        } else {
          result.args_ = argsBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.channel_ = channel_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.targetId_ = targetId_;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.contentType_ = contentType_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.content_ = content_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s.getDefaultInstance()) return this;
        if (other.getChannel() != 0) {
          setChannel(other.getChannel());
        }
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        if (other.getContentType() != 0) {
          setContentType(other.getContentType());
        }
        if (!other.getContent().isEmpty()) {
          content_ = other.content_;
          bitField0_ |= 0x00000008;
          onChanged();
        }
        if (linksBuilder_ == null) {
          if (!other.links_.isEmpty()) {
            if (links_.isEmpty()) {
              links_ = other.links_;
              bitField0_ = (bitField0_ & ~0x00000010);
            } else {
              ensureLinksIsMutable();
              links_.addAll(other.links_);
            }
            onChanged();
          }
        } else {
          if (!other.links_.isEmpty()) {
            if (linksBuilder_.isEmpty()) {
              linksBuilder_.dispose();
              linksBuilder_ = null;
              links_ = other.links_;
              bitField0_ = (bitField0_ & ~0x00000010);
              linksBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getLinksFieldBuilder() : null;
            } else {
              linksBuilder_.addAllMessages(other.links_);
            }
          }
        }
        if (argsBuilder_ == null) {
          if (!other.args_.isEmpty()) {
            if (args_.isEmpty()) {
              args_ = other.args_;
              bitField0_ = (bitField0_ & ~0x00000020);
            } else {
              ensureArgsIsMutable();
              args_.addAll(other.args_);
            }
            onChanged();
          }
        } else {
          if (!other.args_.isEmpty()) {
            if (argsBuilder_.isEmpty()) {
              argsBuilder_.dispose();
              argsBuilder_ = null;
              args_ = other.args_;
              bitField0_ = (bitField0_ & ~0x00000020);
              argsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getArgsFieldBuilder() : null;
            } else {
              argsBuilder_.addAllMessages(other.args_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                channel_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 24: {
                contentType_ = input.readUInt32();
                bitField0_ |= 0x00000004;
                break;
              } // case 24
              case 34: {
                content_ = input.readStringRequireUtf8();
                bitField0_ |= 0x00000008;
                break;
              } // case 34
              case 42: {
                org.gof.demo.worldsrv.msg.Define.p_link m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_link.parser(),
                        extensionRegistry);
                if (linksBuilder_ == null) {
                  ensureLinksIsMutable();
                  links_.add(m);
                } else {
                  linksBuilder_.addMessage(m);
                }
                break;
              } // case 42
              case 50: {
                org.gof.demo.worldsrv.msg.Define.p_key_value_string m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value_string.parser(),
                        extensionRegistry);
                if (argsBuilder_ == null) {
                  ensureArgsIsMutable();
                  args_.add(m);
                } else {
                  argsBuilder_.addMessage(m);
                }
                break;
              } // case 50
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int channel_ ;
      /**
       * <code>uint32 channel = 1;</code>
       * @return The channel.
       */
      @java.lang.Override
      public int getChannel() {
        return channel_;
      }
      /**
       * <code>uint32 channel = 1;</code>
       * @param value The channel to set.
       * @return This builder for chaining.
       */
      public Builder setChannel(int value) {

        channel_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 channel = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channel_ = 0;
        onChanged();
        return this;
      }

      private long targetId_ ;
      /**
       * <code>uint64 target_id = 2;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <code>uint64 target_id = 2;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 target_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        targetId_ = 0L;
        onChanged();
        return this;
      }

      private int contentType_ ;
      /**
       * <code>uint32 content_type = 3;</code>
       * @return The contentType.
       */
      @java.lang.Override
      public int getContentType() {
        return contentType_;
      }
      /**
       * <code>uint32 content_type = 3;</code>
       * @param value The contentType to set.
       * @return This builder for chaining.
       */
      public Builder setContentType(int value) {

        contentType_ = value;
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 content_type = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearContentType() {
        bitField0_ = (bitField0_ & ~0x00000004);
        contentType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object content_ = "";
      /**
       * <code>string content = 4;</code>
       * @return The content.
       */
      public java.lang.String getContent() {
        java.lang.Object ref = content_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          content_ = s;
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>string content = 4;</code>
       * @return The bytes for content.
       */
      public com.google.protobuf.ByteString
          getContentBytes() {
        java.lang.Object ref = content_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          content_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>string content = 4;</code>
       * @param value The content to set.
       * @return This builder for chaining.
       */
      public Builder setContent(
          java.lang.String value) {
        if (value == null) { throw new NullPointerException(); }
        content_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <code>string content = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearContent() {
        content_ = getDefaultInstance().getContent();
        bitField0_ = (bitField0_ & ~0x00000008);
        onChanged();
        return this;
      }
      /**
       * <code>string content = 4;</code>
       * @param value The bytes for content to set.
       * @return This builder for chaining.
       */
      public Builder setContentBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) { throw new NullPointerException(); }
        checkByteStringIsUtf8(value);
        content_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_link> links_ =
        java.util.Collections.emptyList();
      private void ensureLinksIsMutable() {
        if (!((bitField0_ & 0x00000010) != 0)) {
          links_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_link>(links_);
          bitField0_ |= 0x00000010;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_link, org.gof.demo.worldsrv.msg.Define.p_link.Builder, org.gof.demo.worldsrv.msg.Define.p_linkOrBuilder> linksBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_link> getLinksList() {
        if (linksBuilder_ == null) {
          return java.util.Collections.unmodifiableList(links_);
        } else {
          return linksBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public int getLinksCount() {
        if (linksBuilder_ == null) {
          return links_.size();
        } else {
          return linksBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_link getLinks(int index) {
        if (linksBuilder_ == null) {
          return links_.get(index);
        } else {
          return linksBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public Builder setLinks(
          int index, org.gof.demo.worldsrv.msg.Define.p_link value) {
        if (linksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureLinksIsMutable();
          links_.set(index, value);
          onChanged();
        } else {
          linksBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public Builder setLinks(
          int index, org.gof.demo.worldsrv.msg.Define.p_link.Builder builderForValue) {
        if (linksBuilder_ == null) {
          ensureLinksIsMutable();
          links_.set(index, builderForValue.build());
          onChanged();
        } else {
          linksBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public Builder addLinks(org.gof.demo.worldsrv.msg.Define.p_link value) {
        if (linksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureLinksIsMutable();
          links_.add(value);
          onChanged();
        } else {
          linksBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public Builder addLinks(
          int index, org.gof.demo.worldsrv.msg.Define.p_link value) {
        if (linksBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureLinksIsMutable();
          links_.add(index, value);
          onChanged();
        } else {
          linksBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public Builder addLinks(
          org.gof.demo.worldsrv.msg.Define.p_link.Builder builderForValue) {
        if (linksBuilder_ == null) {
          ensureLinksIsMutable();
          links_.add(builderForValue.build());
          onChanged();
        } else {
          linksBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public Builder addLinks(
          int index, org.gof.demo.worldsrv.msg.Define.p_link.Builder builderForValue) {
        if (linksBuilder_ == null) {
          ensureLinksIsMutable();
          links_.add(index, builderForValue.build());
          onChanged();
        } else {
          linksBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public Builder addAllLinks(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_link> values) {
        if (linksBuilder_ == null) {
          ensureLinksIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, links_);
          onChanged();
        } else {
          linksBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public Builder clearLinks() {
        if (linksBuilder_ == null) {
          links_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000010);
          onChanged();
        } else {
          linksBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public Builder removeLinks(int index) {
        if (linksBuilder_ == null) {
          ensureLinksIsMutable();
          links_.remove(index);
          onChanged();
        } else {
          linksBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_link.Builder getLinksBuilder(
          int index) {
        return getLinksFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_linkOrBuilder getLinksOrBuilder(
          int index) {
        if (linksBuilder_ == null) {
          return links_.get(index);  } else {
          return linksBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_linkOrBuilder> 
           getLinksOrBuilderList() {
        if (linksBuilder_ != null) {
          return linksBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(links_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_link.Builder addLinksBuilder() {
        return getLinksFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_link.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_link.Builder addLinksBuilder(
          int index) {
        return getLinksFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_link.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_link links = 5;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_link.Builder> 
           getLinksBuilderList() {
        return getLinksFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_link, org.gof.demo.worldsrv.msg.Define.p_link.Builder, org.gof.demo.worldsrv.msg.Define.p_linkOrBuilder> 
          getLinksFieldBuilder() {
        if (linksBuilder_ == null) {
          linksBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_link, org.gof.demo.worldsrv.msg.Define.p_link.Builder, org.gof.demo.worldsrv.msg.Define.p_linkOrBuilder>(
                  links_,
                  ((bitField0_ & 0x00000010) != 0),
                  getParentForChildren(),
                  isClean());
          links_ = null;
        }
        return linksBuilder_;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value_string> args_ =
        java.util.Collections.emptyList();
      private void ensureArgsIsMutable() {
        if (!((bitField0_ & 0x00000020) != 0)) {
          args_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value_string>(args_);
          bitField0_ |= 0x00000020;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value_string, org.gof.demo.worldsrv.msg.Define.p_key_value_string.Builder, org.gof.demo.worldsrv.msg.Define.p_key_value_stringOrBuilder> argsBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value_string> getArgsList() {
        if (argsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(args_);
        } else {
          return argsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public int getArgsCount() {
        if (argsBuilder_ == null) {
          return args_.size();
        } else {
          return argsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value_string getArgs(int index) {
        if (argsBuilder_ == null) {
          return args_.get(index);
        } else {
          return argsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public Builder setArgs(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value_string value) {
        if (argsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureArgsIsMutable();
          args_.set(index, value);
          onChanged();
        } else {
          argsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public Builder setArgs(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value_string.Builder builderForValue) {
        if (argsBuilder_ == null) {
          ensureArgsIsMutable();
          args_.set(index, builderForValue.build());
          onChanged();
        } else {
          argsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public Builder addArgs(org.gof.demo.worldsrv.msg.Define.p_key_value_string value) {
        if (argsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureArgsIsMutable();
          args_.add(value);
          onChanged();
        } else {
          argsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public Builder addArgs(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value_string value) {
        if (argsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureArgsIsMutable();
          args_.add(index, value);
          onChanged();
        } else {
          argsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public Builder addArgs(
          org.gof.demo.worldsrv.msg.Define.p_key_value_string.Builder builderForValue) {
        if (argsBuilder_ == null) {
          ensureArgsIsMutable();
          args_.add(builderForValue.build());
          onChanged();
        } else {
          argsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public Builder addArgs(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value_string.Builder builderForValue) {
        if (argsBuilder_ == null) {
          ensureArgsIsMutable();
          args_.add(index, builderForValue.build());
          onChanged();
        } else {
          argsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public Builder addAllArgs(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value_string> values) {
        if (argsBuilder_ == null) {
          ensureArgsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, args_);
          onChanged();
        } else {
          argsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public Builder clearArgs() {
        if (argsBuilder_ == null) {
          args_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000020);
          onChanged();
        } else {
          argsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public Builder removeArgs(int index) {
        if (argsBuilder_ == null) {
          ensureArgsIsMutable();
          args_.remove(index);
          onChanged();
        } else {
          argsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value_string.Builder getArgsBuilder(
          int index) {
        return getArgsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value_stringOrBuilder getArgsOrBuilder(
          int index) {
        if (argsBuilder_ == null) {
          return args_.get(index);  } else {
          return argsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_value_stringOrBuilder> 
           getArgsOrBuilderList() {
        if (argsBuilder_ != null) {
          return argsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(args_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value_string.Builder addArgsBuilder() {
        return getArgsFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value_string.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value_string.Builder addArgsBuilder(
          int index) {
        return getArgsFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value_string.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value_string args = 6;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value_string.Builder> 
           getArgsBuilderList() {
        return getArgsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value_string, org.gof.demo.worldsrv.msg.Define.p_key_value_string.Builder, org.gof.demo.worldsrv.msg.Define.p_key_value_stringOrBuilder> 
          getArgsFieldBuilder() {
        if (argsBuilder_ == null) {
          argsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value_string, org.gof.demo.worldsrv.msg.Define.p_key_value_string.Builder, org.gof.demo.worldsrv.msg.Define.p_key_value_stringOrBuilder>(
                  args_,
                  ((bitField0_ & 0x00000020) != 0),
                  getParentForChildren(),
                  isClean());
          args_ = null;
        }
        return argsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_message_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_message_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_message_c2s>
        PARSER = new com.google.protobuf.AbstractParser<chat_message_c2s>() {
      @java.lang.Override
      public chat_message_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_message_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_message_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.chat_message_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface chat_message_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_message_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 channel = 1;</code>
     * @return The channel.
     */
    int getChannel();

    /**
     * <code>uint64 target_id = 2;</code>
     * @return The targetId.
     */
    long getTargetId();

    /**
     * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
     * @return Whether the chatInfo field is set.
     */
    boolean hasChatInfo();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
     * @return The chatInfo.
     */
    org.gof.demo.worldsrv.msg.Define.p_chat getChatInfo();
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder getChatInfoOrBuilder();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_message_s2c}
   */
  public static final class chat_message_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_message_s2c)
      chat_message_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_message_s2c.newBuilder() to construct.
    private chat_message_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_message_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_message_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_message_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_message_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c.Builder.class);
    }

    private int bitField0_;
    public static final int CHANNEL_FIELD_NUMBER = 1;
    private int channel_ = 0;
    /**
     * <code>uint32 channel = 1;</code>
     * @return The channel.
     */
    @java.lang.Override
    public int getChannel() {
      return channel_;
    }

    public static final int TARGET_ID_FIELD_NUMBER = 2;
    private long targetId_ = 0L;
    /**
     * <code>uint64 target_id = 2;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    public static final int CHAT_INFO_FIELD_NUMBER = 3;
    private org.gof.demo.worldsrv.msg.Define.p_chat chatInfo_;
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
     * @return Whether the chatInfo field is set.
     */
    @java.lang.Override
    public boolean hasChatInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
     * @return The chatInfo.
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_chat getChatInfo() {
      return chatInfo_ == null ? org.gof.demo.worldsrv.msg.Define.p_chat.getDefaultInstance() : chatInfo_;
    }
    /**
     * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder getChatInfoOrBuilder() {
      return chatInfo_ == null ? org.gof.demo.worldsrv.msg.Define.p_chat.getDefaultInstance() : chatInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (channel_ != 0) {
        output.writeUInt32(1, channel_);
      }
      if (targetId_ != 0L) {
        output.writeUInt64(2, targetId_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(3, getChatInfo());
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (channel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, channel_);
      }
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, targetId_);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getChatInfo());
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c other = (org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c) obj;

      if (getChannel()
          != other.getChannel()) return false;
      if (getTargetId()
          != other.getTargetId()) return false;
      if (hasChatInfo() != other.hasChatInfo()) return false;
      if (hasChatInfo()) {
        if (!getChatInfo()
            .equals(other.getChatInfo())) return false;
      }
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
      hash = (53 * hash) + getChannel();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      if (hasChatInfo()) {
        hash = (37 * hash) + CHAT_INFO_FIELD_NUMBER;
        hash = (53 * hash) + getChatInfo().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_message_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_message_s2c)
        org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_message_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_message_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getChatInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        channel_ = 0;
        targetId_ = 0L;
        chatInfo_ = null;
        if (chatInfoBuilder_ != null) {
          chatInfoBuilder_.dispose();
          chatInfoBuilder_ = null;
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_message_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c build() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c result = new org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.channel_ = channel_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.targetId_ = targetId_;
        }
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.chatInfo_ = chatInfoBuilder_ == null
              ? chatInfo_
              : chatInfoBuilder_.build();
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ |= to_bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c.getDefaultInstance()) return this;
        if (other.getChannel() != 0) {
          setChannel(other.getChannel());
        }
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        if (other.hasChatInfo()) {
          mergeChatInfo(other.getChatInfo());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                channel_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                input.readMessage(
                    getChatInfoFieldBuilder().getBuilder(),
                    extensionRegistry);
                bitField0_ |= 0x00000004;
                break;
              } // case 26
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int channel_ ;
      /**
       * <code>uint32 channel = 1;</code>
       * @return The channel.
       */
      @java.lang.Override
      public int getChannel() {
        return channel_;
      }
      /**
       * <code>uint32 channel = 1;</code>
       * @param value The channel to set.
       * @return This builder for chaining.
       */
      public Builder setChannel(int value) {

        channel_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 channel = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channel_ = 0;
        onChanged();
        return this;
      }

      private long targetId_ ;
      /**
       * <code>uint64 target_id = 2;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <code>uint64 target_id = 2;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 target_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        targetId_ = 0L;
        onChanged();
        return this;
      }

      private org.gof.demo.worldsrv.msg.Define.p_chat chatInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_chat, org.gof.demo.worldsrv.msg.Define.p_chat.Builder, org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder> chatInfoBuilder_;
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
       * @return Whether the chatInfo field is set.
       */
      public boolean hasChatInfo() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
       * @return The chatInfo.
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat getChatInfo() {
        if (chatInfoBuilder_ == null) {
          return chatInfo_ == null ? org.gof.demo.worldsrv.msg.Define.p_chat.getDefaultInstance() : chatInfo_;
        } else {
          return chatInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
       */
      public Builder setChatInfo(org.gof.demo.worldsrv.msg.Define.p_chat value) {
        if (chatInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          chatInfo_ = value;
        } else {
          chatInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
       */
      public Builder setChatInfo(
          org.gof.demo.worldsrv.msg.Define.p_chat.Builder builderForValue) {
        if (chatInfoBuilder_ == null) {
          chatInfo_ = builderForValue.build();
        } else {
          chatInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
       */
      public Builder mergeChatInfo(org.gof.demo.worldsrv.msg.Define.p_chat value) {
        if (chatInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
            chatInfo_ != null &&
            chatInfo_ != org.gof.demo.worldsrv.msg.Define.p_chat.getDefaultInstance()) {
            getChatInfoBuilder().mergeFrom(value);
          } else {
            chatInfo_ = value;
          }
        } else {
          chatInfoBuilder_.mergeFrom(value);
        }
        if (chatInfo_ != null) {
          bitField0_ |= 0x00000004;
          onChanged();
        }
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
       */
      public Builder clearChatInfo() {
        bitField0_ = (bitField0_ & ~0x00000004);
        chatInfo_ = null;
        if (chatInfoBuilder_ != null) {
          chatInfoBuilder_.dispose();
          chatInfoBuilder_ = null;
        }
        onChanged();
        return this;
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat.Builder getChatInfoBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getChatInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder getChatInfoOrBuilder() {
        if (chatInfoBuilder_ != null) {
          return chatInfoBuilder_.getMessageOrBuilder();
        } else {
          return chatInfo_ == null ?
              org.gof.demo.worldsrv.msg.Define.p_chat.getDefaultInstance() : chatInfo_;
        }
      }
      /**
       * <code>.org.gof.demo.worldsrv.msg.p_chat chat_info = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_chat, org.gof.demo.worldsrv.msg.Define.p_chat.Builder, org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder> 
          getChatInfoFieldBuilder() {
        if (chatInfoBuilder_ == null) {
          chatInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_chat, org.gof.demo.worldsrv.msg.Define.p_chat.Builder, org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder>(
                  getChatInfo(),
                  getParentForChildren(),
                  isClean());
          chatInfo_ = null;
        }
        return chatInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_message_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_message_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_message_s2c>
        PARSER = new com.google.protobuf.AbstractParser<chat_message_s2c>() {
      @java.lang.Override
      public chat_message_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_message_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_message_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.chat_message_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface chat_history_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_history_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 channel = 1;</code>
     * @return The channel.
     */
    int getChannel();

    /**
     * <code>uint64 target_id = 2;</code>
     * @return The targetId.
     */
    long getTargetId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_history_c2s}
   */
  public static final class chat_history_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_history_c2s)
      chat_history_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_history_c2s.newBuilder() to construct.
    private chat_history_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_history_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_history_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s.class, org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s.Builder.class);
    }

    public static final int CHANNEL_FIELD_NUMBER = 1;
    private int channel_ = 0;
    /**
     * <code>uint32 channel = 1;</code>
     * @return The channel.
     */
    @java.lang.Override
    public int getChannel() {
      return channel_;
    }

    public static final int TARGET_ID_FIELD_NUMBER = 2;
    private long targetId_ = 0L;
    /**
     * <code>uint64 target_id = 2;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (channel_ != 0) {
        output.writeUInt32(1, channel_);
      }
      if (targetId_ != 0L) {
        output.writeUInt64(2, targetId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (channel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, channel_);
      }
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, targetId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s other = (org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s) obj;

      if (getChannel()
          != other.getChannel()) return false;
      if (getTargetId()
          != other.getTargetId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
      hash = (53 * hash) + getChannel();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_history_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_history_c2s)
        org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s.class, org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        channel_ = 0;
        targetId_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s build() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s result = new org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.channel_ = channel_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.targetId_ = targetId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s.getDefaultInstance()) return this;
        if (other.getChannel() != 0) {
          setChannel(other.getChannel());
        }
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                channel_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int channel_ ;
      /**
       * <code>uint32 channel = 1;</code>
       * @return The channel.
       */
      @java.lang.Override
      public int getChannel() {
        return channel_;
      }
      /**
       * <code>uint32 channel = 1;</code>
       * @param value The channel to set.
       * @return This builder for chaining.
       */
      public Builder setChannel(int value) {

        channel_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 channel = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channel_ = 0;
        onChanged();
        return this;
      }

      private long targetId_ ;
      /**
       * <code>uint64 target_id = 2;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <code>uint64 target_id = 2;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 target_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        targetId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_history_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_history_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_history_c2s>
        PARSER = new com.google.protobuf.AbstractParser<chat_history_c2s>() {
      @java.lang.Override
      public chat_history_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_history_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_history_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.chat_history_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface chat_history_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_history_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 channel = 1;</code>
     * @return The channel.
     */
    int getChannel();

    /**
     * <code>uint64 target_id = 2;</code>
     * @return The targetId.
     */
    long getTargetId();

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat> 
        getChatHistoryList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_chat getChatHistory(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
     */
    int getChatHistoryCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder> 
        getChatHistoryOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder getChatHistoryOrBuilder(
        int index);

    /**
     * <pre>
     * channel=私聊 时，是否被对方拉黑
     * </pre>
     *
     * <code>uint32 is_block = 4;</code>
     * @return The isBlock.
     */
    int getIsBlock();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_history_s2c}
   */
  public static final class chat_history_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_history_s2c)
      chat_history_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_history_s2c.newBuilder() to construct.
    private chat_history_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_history_s2c() {
      chatHistory_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_history_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c.Builder.class);
    }

    public static final int CHANNEL_FIELD_NUMBER = 1;
    private int channel_ = 0;
    /**
     * <code>uint32 channel = 1;</code>
     * @return The channel.
     */
    @java.lang.Override
    public int getChannel() {
      return channel_;
    }

    public static final int TARGET_ID_FIELD_NUMBER = 2;
    private long targetId_ = 0L;
    /**
     * <code>uint64 target_id = 2;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    public static final int CHAT_HISTORY_FIELD_NUMBER = 3;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat> chatHistory_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat> getChatHistoryList() {
      return chatHistory_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder> 
        getChatHistoryOrBuilderList() {
      return chatHistory_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
     */
    @java.lang.Override
    public int getChatHistoryCount() {
      return chatHistory_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_chat getChatHistory(int index) {
      return chatHistory_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder getChatHistoryOrBuilder(
        int index) {
      return chatHistory_.get(index);
    }

    public static final int IS_BLOCK_FIELD_NUMBER = 4;
    private int isBlock_ = 0;
    /**
     * <pre>
     * channel=私聊 时，是否被对方拉黑
     * </pre>
     *
     * <code>uint32 is_block = 4;</code>
     * @return The isBlock.
     */
    @java.lang.Override
    public int getIsBlock() {
      return isBlock_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (channel_ != 0) {
        output.writeUInt32(1, channel_);
      }
      if (targetId_ != 0L) {
        output.writeUInt64(2, targetId_);
      }
      for (int i = 0; i < chatHistory_.size(); i++) {
        output.writeMessage(3, chatHistory_.get(i));
      }
      if (isBlock_ != 0) {
        output.writeUInt32(4, isBlock_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (channel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, channel_);
      }
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(2, targetId_);
      }
      for (int i = 0; i < chatHistory_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, chatHistory_.get(i));
      }
      if (isBlock_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(4, isBlock_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c other = (org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c) obj;

      if (getChannel()
          != other.getChannel()) return false;
      if (getTargetId()
          != other.getTargetId()) return false;
      if (!getChatHistoryList()
          .equals(other.getChatHistoryList())) return false;
      if (getIsBlock()
          != other.getIsBlock()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + CHANNEL_FIELD_NUMBER;
      hash = (53 * hash) + getChannel();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      if (getChatHistoryCount() > 0) {
        hash = (37 * hash) + CHAT_HISTORY_FIELD_NUMBER;
        hash = (53 * hash) + getChatHistoryList().hashCode();
      }
      hash = (37 * hash) + IS_BLOCK_FIELD_NUMBER;
      hash = (53 * hash) + getIsBlock();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_history_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_history_s2c)
        org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        channel_ = 0;
        targetId_ = 0L;
        if (chatHistoryBuilder_ == null) {
          chatHistory_ = java.util.Collections.emptyList();
        } else {
          chatHistory_ = null;
          chatHistoryBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        isBlock_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c build() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c result = new org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c result) {
        if (chatHistoryBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            chatHistory_ = java.util.Collections.unmodifiableList(chatHistory_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.chatHistory_ = chatHistory_;
        } else {
          result.chatHistory_ = chatHistoryBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.channel_ = channel_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.targetId_ = targetId_;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.isBlock_ = isBlock_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c.getDefaultInstance()) return this;
        if (other.getChannel() != 0) {
          setChannel(other.getChannel());
        }
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        if (chatHistoryBuilder_ == null) {
          if (!other.chatHistory_.isEmpty()) {
            if (chatHistory_.isEmpty()) {
              chatHistory_ = other.chatHistory_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureChatHistoryIsMutable();
              chatHistory_.addAll(other.chatHistory_);
            }
            onChanged();
          }
        } else {
          if (!other.chatHistory_.isEmpty()) {
            if (chatHistoryBuilder_.isEmpty()) {
              chatHistoryBuilder_.dispose();
              chatHistoryBuilder_ = null;
              chatHistory_ = other.chatHistory_;
              bitField0_ = (bitField0_ & ~0x00000004);
              chatHistoryBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getChatHistoryFieldBuilder() : null;
            } else {
              chatHistoryBuilder_.addAllMessages(other.chatHistory_);
            }
          }
        }
        if (other.getIsBlock() != 0) {
          setIsBlock(other.getIsBlock());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                channel_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              case 26: {
                org.gof.demo.worldsrv.msg.Define.p_chat m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_chat.parser(),
                        extensionRegistry);
                if (chatHistoryBuilder_ == null) {
                  ensureChatHistoryIsMutable();
                  chatHistory_.add(m);
                } else {
                  chatHistoryBuilder_.addMessage(m);
                }
                break;
              } // case 26
              case 32: {
                isBlock_ = input.readUInt32();
                bitField0_ |= 0x00000008;
                break;
              } // case 32
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int channel_ ;
      /**
       * <code>uint32 channel = 1;</code>
       * @return The channel.
       */
      @java.lang.Override
      public int getChannel() {
        return channel_;
      }
      /**
       * <code>uint32 channel = 1;</code>
       * @param value The channel to set.
       * @return This builder for chaining.
       */
      public Builder setChannel(int value) {

        channel_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 channel = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannel() {
        bitField0_ = (bitField0_ & ~0x00000001);
        channel_ = 0;
        onChanged();
        return this;
      }

      private long targetId_ ;
      /**
       * <code>uint64 target_id = 2;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <code>uint64 target_id = 2;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 target_id = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        targetId_ = 0L;
        onChanged();
        return this;
      }

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat> chatHistory_ =
        java.util.Collections.emptyList();
      private void ensureChatHistoryIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          chatHistory_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_chat>(chatHistory_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_chat, org.gof.demo.worldsrv.msg.Define.p_chat.Builder, org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder> chatHistoryBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat> getChatHistoryList() {
        if (chatHistoryBuilder_ == null) {
          return java.util.Collections.unmodifiableList(chatHistory_);
        } else {
          return chatHistoryBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public int getChatHistoryCount() {
        if (chatHistoryBuilder_ == null) {
          return chatHistory_.size();
        } else {
          return chatHistoryBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat getChatHistory(int index) {
        if (chatHistoryBuilder_ == null) {
          return chatHistory_.get(index);
        } else {
          return chatHistoryBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public Builder setChatHistory(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat value) {
        if (chatHistoryBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatHistoryIsMutable();
          chatHistory_.set(index, value);
          onChanged();
        } else {
          chatHistoryBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public Builder setChatHistory(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat.Builder builderForValue) {
        if (chatHistoryBuilder_ == null) {
          ensureChatHistoryIsMutable();
          chatHistory_.set(index, builderForValue.build());
          onChanged();
        } else {
          chatHistoryBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public Builder addChatHistory(org.gof.demo.worldsrv.msg.Define.p_chat value) {
        if (chatHistoryBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatHistoryIsMutable();
          chatHistory_.add(value);
          onChanged();
        } else {
          chatHistoryBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public Builder addChatHistory(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat value) {
        if (chatHistoryBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureChatHistoryIsMutable();
          chatHistory_.add(index, value);
          onChanged();
        } else {
          chatHistoryBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public Builder addChatHistory(
          org.gof.demo.worldsrv.msg.Define.p_chat.Builder builderForValue) {
        if (chatHistoryBuilder_ == null) {
          ensureChatHistoryIsMutable();
          chatHistory_.add(builderForValue.build());
          onChanged();
        } else {
          chatHistoryBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public Builder addChatHistory(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat.Builder builderForValue) {
        if (chatHistoryBuilder_ == null) {
          ensureChatHistoryIsMutable();
          chatHistory_.add(index, builderForValue.build());
          onChanged();
        } else {
          chatHistoryBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public Builder addAllChatHistory(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_chat> values) {
        if (chatHistoryBuilder_ == null) {
          ensureChatHistoryIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, chatHistory_);
          onChanged();
        } else {
          chatHistoryBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public Builder clearChatHistory() {
        if (chatHistoryBuilder_ == null) {
          chatHistory_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          chatHistoryBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public Builder removeChatHistory(int index) {
        if (chatHistoryBuilder_ == null) {
          ensureChatHistoryIsMutable();
          chatHistory_.remove(index);
          onChanged();
        } else {
          chatHistoryBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat.Builder getChatHistoryBuilder(
          int index) {
        return getChatHistoryFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder getChatHistoryOrBuilder(
          int index) {
        if (chatHistoryBuilder_ == null) {
          return chatHistory_.get(index);  } else {
          return chatHistoryBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder> 
           getChatHistoryOrBuilderList() {
        if (chatHistoryBuilder_ != null) {
          return chatHistoryBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(chatHistory_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat.Builder addChatHistoryBuilder() {
        return getChatHistoryFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_chat.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat.Builder addChatHistoryBuilder(
          int index) {
        return getChatHistoryFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_chat.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat chat_history = 3;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat.Builder> 
           getChatHistoryBuilderList() {
        return getChatHistoryFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_chat, org.gof.demo.worldsrv.msg.Define.p_chat.Builder, org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder> 
          getChatHistoryFieldBuilder() {
        if (chatHistoryBuilder_ == null) {
          chatHistoryBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_chat, org.gof.demo.worldsrv.msg.Define.p_chat.Builder, org.gof.demo.worldsrv.msg.Define.p_chatOrBuilder>(
                  chatHistory_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          chatHistory_ = null;
        }
        return chatHistoryBuilder_;
      }

      private int isBlock_ ;
      /**
       * <pre>
       * channel=私聊 时，是否被对方拉黑
       * </pre>
       *
       * <code>uint32 is_block = 4;</code>
       * @return The isBlock.
       */
      @java.lang.Override
      public int getIsBlock() {
        return isBlock_;
      }
      /**
       * <pre>
       * channel=私聊 时，是否被对方拉黑
       * </pre>
       *
       * <code>uint32 is_block = 4;</code>
       * @param value The isBlock to set.
       * @return This builder for chaining.
       */
      public Builder setIsBlock(int value) {

        isBlock_ = value;
        bitField0_ |= 0x00000008;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * channel=私聊 时，是否被对方拉黑
       * </pre>
       *
       * <code>uint32 is_block = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsBlock() {
        bitField0_ = (bitField0_ & ~0x00000008);
        isBlock_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_history_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_history_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_history_s2c>
        PARSER = new com.google.protobuf.AbstractParser<chat_history_s2c>() {
      @java.lang.Override
      public chat_history_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_history_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_history_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.chat_history_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface chat_friend_info_list_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_friend_info_list_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated uint64 id = 1 [packed = false];</code>
     * @return A list containing the id.
     */
    java.util.List<java.lang.Long> getIdList();
    /**
     * <code>repeated uint64 id = 1 [packed = false];</code>
     * @return The count of id.
     */
    int getIdCount();
    /**
     * <code>repeated uint64 id = 1 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The id at the given index.
     */
    long getId(int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_friend_info_list_c2s}
   */
  public static final class chat_friend_info_list_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_friend_info_list_c2s)
      chat_friend_info_list_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_friend_info_list_c2s.newBuilder() to construct.
    private chat_friend_info_list_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_friend_info_list_c2s() {
      id_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_friend_info_list_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s.class, org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s.Builder.class);
    }

    public static final int ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.LongList id_ =
        emptyLongList();
    /**
     * <code>repeated uint64 id = 1 [packed = false];</code>
     * @return A list containing the id.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getIdList() {
      return id_;
    }
    /**
     * <code>repeated uint64 id = 1 [packed = false];</code>
     * @return The count of id.
     */
    public int getIdCount() {
      return id_.size();
    }
    /**
     * <code>repeated uint64 id = 1 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The id at the given index.
     */
    public long getId(int index) {
      return id_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < id_.size(); i++) {
        output.writeUInt64(1, id_.getLong(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < id_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt64SizeNoTag(id_.getLong(i));
        }
        size += dataSize;
        size += 1 * getIdList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s other = (org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s) obj;

      if (!getIdList()
          .equals(other.getIdList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getIdCount() > 0) {
        hash = (37 * hash) + ID_FIELD_NUMBER;
        hash = (53 * hash) + getIdList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_friend_info_list_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_friend_info_list_c2s)
        org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s.class, org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        id_ = emptyLongList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s build() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s result = new org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          id_.makeImmutable();
          result.id_ = id_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s.getDefaultInstance()) return this;
        if (!other.id_.isEmpty()) {
          if (id_.isEmpty()) {
            id_ = other.id_;
            id_.makeImmutable();
            bitField0_ |= 0x00000001;
          } else {
            ensureIdIsMutable();
            id_.addAll(other.id_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                long v = input.readUInt64();
                ensureIdIsMutable();
                id_.addLong(v);
                break;
              } // case 8
              case 10: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureIdIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  id_.addLong(input.readUInt64());
                }
                input.popLimit(limit);
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList id_ = emptyLongList();
      private void ensureIdIsMutable() {
        if (!id_.isModifiable()) {
          id_ = makeMutableCopy(id_);
        }
        bitField0_ |= 0x00000001;
      }
      /**
       * <code>repeated uint64 id = 1 [packed = false];</code>
       * @return A list containing the id.
       */
      public java.util.List<java.lang.Long>
          getIdList() {
        id_.makeImmutable();
        return id_;
      }
      /**
       * <code>repeated uint64 id = 1 [packed = false];</code>
       * @return The count of id.
       */
      public int getIdCount() {
        return id_.size();
      }
      /**
       * <code>repeated uint64 id = 1 [packed = false];</code>
       * @param index The index of the element to return.
       * @return The id at the given index.
       */
      public long getId(int index) {
        return id_.getLong(index);
      }
      /**
       * <code>repeated uint64 id = 1 [packed = false];</code>
       * @param index The index to set the value at.
       * @param value The id to set.
       * @return This builder for chaining.
       */
      public Builder setId(
          int index, long value) {

        ensureIdIsMutable();
        id_.setLong(index, value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 id = 1 [packed = false];</code>
       * @param value The id to add.
       * @return This builder for chaining.
       */
      public Builder addId(long value) {

        ensureIdIsMutable();
        id_.addLong(value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 id = 1 [packed = false];</code>
       * @param values The id to add.
       * @return This builder for chaining.
       */
      public Builder addAllId(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureIdIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, id_);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint64 id = 1 [packed = false];</code>
       * @return This builder for chaining.
       */
      public Builder clearId() {
        id_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_friend_info_list_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_friend_info_list_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_friend_info_list_c2s>
        PARSER = new com.google.protobuf.AbstractParser<chat_friend_info_list_c2s>() {
      @java.lang.Override
      public chat_friend_info_list_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_friend_info_list_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_friend_info_list_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface chat_friend_info_list_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_friend_info_list_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_friend> 
        getFriendInfoListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_chat_friend getFriendInfoList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
     */
    int getFriendInfoListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_chat_friendOrBuilder> 
        getFriendInfoListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_chat_friendOrBuilder getFriendInfoListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_friend_info_list_s2c}
   */
  public static final class chat_friend_info_list_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_friend_info_list_s2c)
      chat_friend_info_list_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_friend_info_list_s2c.newBuilder() to construct.
    private chat_friend_info_list_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_friend_info_list_s2c() {
      friendInfoList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_friend_info_list_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c.Builder.class);
    }

    public static final int FRIEND_INFO_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_friend> friendInfoList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_friend> getFriendInfoListList() {
      return friendInfoList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_chat_friendOrBuilder> 
        getFriendInfoListOrBuilderList() {
      return friendInfoList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
     */
    @java.lang.Override
    public int getFriendInfoListCount() {
      return friendInfoList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_chat_friend getFriendInfoList(int index) {
      return friendInfoList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_chat_friendOrBuilder getFriendInfoListOrBuilder(
        int index) {
      return friendInfoList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < friendInfoList_.size(); i++) {
        output.writeMessage(1, friendInfoList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < friendInfoList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, friendInfoList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c other = (org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c) obj;

      if (!getFriendInfoListList()
          .equals(other.getFriendInfoListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getFriendInfoListCount() > 0) {
        hash = (37 * hash) + FRIEND_INFO_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getFriendInfoListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_friend_info_list_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_friend_info_list_s2c)
        org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (friendInfoListBuilder_ == null) {
          friendInfoList_ = java.util.Collections.emptyList();
        } else {
          friendInfoList_ = null;
          friendInfoListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c build() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c result = new org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c result) {
        if (friendInfoListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            friendInfoList_ = java.util.Collections.unmodifiableList(friendInfoList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.friendInfoList_ = friendInfoList_;
        } else {
          result.friendInfoList_ = friendInfoListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c.getDefaultInstance()) return this;
        if (friendInfoListBuilder_ == null) {
          if (!other.friendInfoList_.isEmpty()) {
            if (friendInfoList_.isEmpty()) {
              friendInfoList_ = other.friendInfoList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureFriendInfoListIsMutable();
              friendInfoList_.addAll(other.friendInfoList_);
            }
            onChanged();
          }
        } else {
          if (!other.friendInfoList_.isEmpty()) {
            if (friendInfoListBuilder_.isEmpty()) {
              friendInfoListBuilder_.dispose();
              friendInfoListBuilder_ = null;
              friendInfoList_ = other.friendInfoList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              friendInfoListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getFriendInfoListFieldBuilder() : null;
            } else {
              friendInfoListBuilder_.addAllMessages(other.friendInfoList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_chat_friend m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_chat_friend.parser(),
                        extensionRegistry);
                if (friendInfoListBuilder_ == null) {
                  ensureFriendInfoListIsMutable();
                  friendInfoList_.add(m);
                } else {
                  friendInfoListBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_friend> friendInfoList_ =
        java.util.Collections.emptyList();
      private void ensureFriendInfoListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          friendInfoList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_chat_friend>(friendInfoList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_chat_friend, org.gof.demo.worldsrv.msg.Define.p_chat_friend.Builder, org.gof.demo.worldsrv.msg.Define.p_chat_friendOrBuilder> friendInfoListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_friend> getFriendInfoListList() {
        if (friendInfoListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(friendInfoList_);
        } else {
          return friendInfoListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public int getFriendInfoListCount() {
        if (friendInfoListBuilder_ == null) {
          return friendInfoList_.size();
        } else {
          return friendInfoListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_friend getFriendInfoList(int index) {
        if (friendInfoListBuilder_ == null) {
          return friendInfoList_.get(index);
        } else {
          return friendInfoListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public Builder setFriendInfoList(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat_friend value) {
        if (friendInfoListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFriendInfoListIsMutable();
          friendInfoList_.set(index, value);
          onChanged();
        } else {
          friendInfoListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public Builder setFriendInfoList(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat_friend.Builder builderForValue) {
        if (friendInfoListBuilder_ == null) {
          ensureFriendInfoListIsMutable();
          friendInfoList_.set(index, builderForValue.build());
          onChanged();
        } else {
          friendInfoListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public Builder addFriendInfoList(org.gof.demo.worldsrv.msg.Define.p_chat_friend value) {
        if (friendInfoListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFriendInfoListIsMutable();
          friendInfoList_.add(value);
          onChanged();
        } else {
          friendInfoListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public Builder addFriendInfoList(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat_friend value) {
        if (friendInfoListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureFriendInfoListIsMutable();
          friendInfoList_.add(index, value);
          onChanged();
        } else {
          friendInfoListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public Builder addFriendInfoList(
          org.gof.demo.worldsrv.msg.Define.p_chat_friend.Builder builderForValue) {
        if (friendInfoListBuilder_ == null) {
          ensureFriendInfoListIsMutable();
          friendInfoList_.add(builderForValue.build());
          onChanged();
        } else {
          friendInfoListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public Builder addFriendInfoList(
          int index, org.gof.demo.worldsrv.msg.Define.p_chat_friend.Builder builderForValue) {
        if (friendInfoListBuilder_ == null) {
          ensureFriendInfoListIsMutable();
          friendInfoList_.add(index, builderForValue.build());
          onChanged();
        } else {
          friendInfoListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public Builder addAllFriendInfoList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_chat_friend> values) {
        if (friendInfoListBuilder_ == null) {
          ensureFriendInfoListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, friendInfoList_);
          onChanged();
        } else {
          friendInfoListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public Builder clearFriendInfoList() {
        if (friendInfoListBuilder_ == null) {
          friendInfoList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          friendInfoListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public Builder removeFriendInfoList(int index) {
        if (friendInfoListBuilder_ == null) {
          ensureFriendInfoListIsMutable();
          friendInfoList_.remove(index);
          onChanged();
        } else {
          friendInfoListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_friend.Builder getFriendInfoListBuilder(
          int index) {
        return getFriendInfoListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_friendOrBuilder getFriendInfoListOrBuilder(
          int index) {
        if (friendInfoListBuilder_ == null) {
          return friendInfoList_.get(index);  } else {
          return friendInfoListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_chat_friendOrBuilder> 
           getFriendInfoListOrBuilderList() {
        if (friendInfoListBuilder_ != null) {
          return friendInfoListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(friendInfoList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_friend.Builder addFriendInfoListBuilder() {
        return getFriendInfoListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_chat_friend.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_chat_friend.Builder addFriendInfoListBuilder(
          int index) {
        return getFriendInfoListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_chat_friend.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_chat_friend friend_info_list = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_chat_friend.Builder> 
           getFriendInfoListBuilderList() {
        return getFriendInfoListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_chat_friend, org.gof.demo.worldsrv.msg.Define.p_chat_friend.Builder, org.gof.demo.worldsrv.msg.Define.p_chat_friendOrBuilder> 
          getFriendInfoListFieldBuilder() {
        if (friendInfoListBuilder_ == null) {
          friendInfoListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_chat_friend, org.gof.demo.worldsrv.msg.Define.p_chat_friend.Builder, org.gof.demo.worldsrv.msg.Define.p_chat_friendOrBuilder>(
                  friendInfoList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          friendInfoList_ = null;
        }
        return friendInfoListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_friend_info_list_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_friend_info_list_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_friend_info_list_s2c>
        PARSER = new com.google.protobuf.AbstractParser<chat_friend_info_list_s2c>() {
      @java.lang.Override
      public chat_friend_info_list_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_friend_info_list_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_friend_info_list_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.chat_friend_info_list_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface chat_history_read_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_history_read_c2s)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    long getTargetId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_history_read_c2s}
   */
  public static final class chat_history_read_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_history_read_c2s)
      chat_history_read_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_history_read_c2s.newBuilder() to construct.
    private chat_history_read_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_history_read_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_history_read_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_read_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_read_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s.class, org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s.Builder.class);
    }

    public static final int TARGET_ID_FIELD_NUMBER = 1;
    private long targetId_ = 0L;
    /**
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (targetId_ != 0L) {
        output.writeUInt64(1, targetId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, targetId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s other = (org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s) obj;

      if (getTargetId()
          != other.getTargetId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_history_read_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_history_read_c2s)
        org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_read_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_read_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s.class, org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        targetId_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_read_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s build() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s result = new org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.targetId_ = targetId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s.getDefaultInstance()) return this;
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long targetId_ ;
      /**
       * <code>uint64 target_id = 1;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <code>uint64 target_id = 1;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 target_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_history_read_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_history_read_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_history_read_c2s>
        PARSER = new com.google.protobuf.AbstractParser<chat_history_read_c2s>() {
      @java.lang.Override
      public chat_history_read_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_history_read_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_history_read_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface chat_history_read_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_history_read_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    long getTargetId();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_history_read_s2c}
   */
  public static final class chat_history_read_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_history_read_s2c)
      chat_history_read_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_history_read_s2c.newBuilder() to construct.
    private chat_history_read_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_history_read_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_history_read_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_read_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_read_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c.Builder.class);
    }

    public static final int TARGET_ID_FIELD_NUMBER = 1;
    private long targetId_ = 0L;
    /**
     * <code>uint64 target_id = 1;</code>
     * @return The targetId.
     */
    @java.lang.Override
    public long getTargetId() {
      return targetId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (targetId_ != 0L) {
        output.writeUInt64(1, targetId_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (targetId_ != 0L) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt64Size(1, targetId_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c other = (org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c) obj;

      if (getTargetId()
          != other.getTargetId()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TARGET_ID_FIELD_NUMBER;
      hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
          getTargetId());
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_history_read_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_history_read_s2c)
        org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_read_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_read_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        targetId_ = 0L;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_history_read_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c build() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c result = new org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.targetId_ = targetId_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c.getDefaultInstance()) return this;
        if (other.getTargetId() != 0L) {
          setTargetId(other.getTargetId());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                targetId_ = input.readUInt64();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private long targetId_ ;
      /**
       * <code>uint64 target_id = 1;</code>
       * @return The targetId.
       */
      @java.lang.Override
      public long getTargetId() {
        return targetId_;
      }
      /**
       * <code>uint64 target_id = 1;</code>
       * @param value The targetId to set.
       * @return This builder for chaining.
       */
      public Builder setTargetId(long value) {

        targetId_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint64 target_id = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        targetId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_history_read_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_history_read_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_history_read_s2c>
        PARSER = new com.google.protobuf.AbstractParser<chat_history_read_s2c>() {
      @java.lang.Override
      public chat_history_read_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_history_read_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_history_read_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.chat_history_read_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ban_chat_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.ban_chat_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.ban_chat_c2s}
   */
  public static final class ban_chat_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.ban_chat_c2s)
      ban_chat_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ban_chat_c2s.newBuilder() to construct.
    private ban_chat_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ban_chat_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ban_chat_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_ban_chat_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_ban_chat_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s.class, org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s other = (org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.ban_chat_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.ban_chat_c2s)
        org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_ban_chat_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_ban_chat_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s.class, org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_ban_chat_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s build() {
        org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s result = new org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.ban_chat_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.ban_chat_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ban_chat_c2s>
        PARSER = new com.google.protobuf.AbstractParser<ban_chat_c2s>() {
      @java.lang.Override
      public ban_chat_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ban_chat_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ban_chat_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.ban_chat_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ban_chat_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.ban_chat_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> 
        getRoleIdList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_value getRoleId(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
     */
    int getRoleIdCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getRoleIdOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getRoleIdOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.ban_chat_s2c}
   */
  public static final class ban_chat_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.ban_chat_s2c)
      ban_chat_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ban_chat_s2c.newBuilder() to construct.
    private ban_chat_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ban_chat_s2c() {
      roleId_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ban_chat_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_ban_chat_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_ban_chat_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c.Builder.class);
    }

    public static final int ROLE_ID_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> roleId_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getRoleIdList() {
      return roleId_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
        getRoleIdOrBuilderList() {
      return roleId_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
     */
    @java.lang.Override
    public int getRoleIdCount() {
      return roleId_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_value getRoleId(int index) {
      return roleId_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getRoleIdOrBuilder(
        int index) {
      return roleId_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < roleId_.size(); i++) {
        output.writeMessage(1, roleId_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < roleId_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, roleId_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c other = (org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c) obj;

      if (!getRoleIdList()
          .equals(other.getRoleIdList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getRoleIdCount() > 0) {
        hash = (37 * hash) + ROLE_ID_FIELD_NUMBER;
        hash = (53 * hash) + getRoleIdList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.ban_chat_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.ban_chat_s2c)
        org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_ban_chat_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_ban_chat_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (roleIdBuilder_ == null) {
          roleId_ = java.util.Collections.emptyList();
        } else {
          roleId_ = null;
          roleIdBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_ban_chat_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c build() {
        org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c result = new org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c result) {
        if (roleIdBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            roleId_ = java.util.Collections.unmodifiableList(roleId_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.roleId_ = roleId_;
        } else {
          result.roleId_ = roleIdBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c.getDefaultInstance()) return this;
        if (roleIdBuilder_ == null) {
          if (!other.roleId_.isEmpty()) {
            if (roleId_.isEmpty()) {
              roleId_ = other.roleId_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureRoleIdIsMutable();
              roleId_.addAll(other.roleId_);
            }
            onChanged();
          }
        } else {
          if (!other.roleId_.isEmpty()) {
            if (roleIdBuilder_.isEmpty()) {
              roleIdBuilder_.dispose();
              roleIdBuilder_ = null;
              roleId_ = other.roleId_;
              bitField0_ = (bitField0_ & ~0x00000001);
              roleIdBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getRoleIdFieldBuilder() : null;
            } else {
              roleIdBuilder_.addAllMessages(other.roleId_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 10: {
                org.gof.demo.worldsrv.msg.Define.p_key_value m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_key_value.parser(),
                        extensionRegistry);
                if (roleIdBuilder_ == null) {
                  ensureRoleIdIsMutable();
                  roleId_.add(m);
                } else {
                  roleIdBuilder_.addMessage(m);
                }
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> roleId_ =
        java.util.Collections.emptyList();
      private void ensureRoleIdIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          roleId_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_key_value>(roleId_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> roleIdBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value> getRoleIdList() {
        if (roleIdBuilder_ == null) {
          return java.util.Collections.unmodifiableList(roleId_);
        } else {
          return roleIdBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public int getRoleIdCount() {
        if (roleIdBuilder_ == null) {
          return roleId_.size();
        } else {
          return roleIdBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value getRoleId(int index) {
        if (roleIdBuilder_ == null) {
          return roleId_.get(index);
        } else {
          return roleIdBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public Builder setRoleId(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (roleIdBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleIdIsMutable();
          roleId_.set(index, value);
          onChanged();
        } else {
          roleIdBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public Builder setRoleId(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (roleIdBuilder_ == null) {
          ensureRoleIdIsMutable();
          roleId_.set(index, builderForValue.build());
          onChanged();
        } else {
          roleIdBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public Builder addRoleId(org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (roleIdBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleIdIsMutable();
          roleId_.add(value);
          onChanged();
        } else {
          roleIdBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public Builder addRoleId(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value value) {
        if (roleIdBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureRoleIdIsMutable();
          roleId_.add(index, value);
          onChanged();
        } else {
          roleIdBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public Builder addRoleId(
          org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (roleIdBuilder_ == null) {
          ensureRoleIdIsMutable();
          roleId_.add(builderForValue.build());
          onChanged();
        } else {
          roleIdBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public Builder addRoleId(
          int index, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder builderForValue) {
        if (roleIdBuilder_ == null) {
          ensureRoleIdIsMutable();
          roleId_.add(index, builderForValue.build());
          onChanged();
        } else {
          roleIdBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public Builder addAllRoleId(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_key_value> values) {
        if (roleIdBuilder_ == null) {
          ensureRoleIdIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, roleId_);
          onChanged();
        } else {
          roleIdBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public Builder clearRoleId() {
        if (roleIdBuilder_ == null) {
          roleId_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          roleIdBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public Builder removeRoleId(int index) {
        if (roleIdBuilder_ == null) {
          ensureRoleIdIsMutable();
          roleId_.remove(index);
          onChanged();
        } else {
          roleIdBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder getRoleIdBuilder(
          int index) {
        return getRoleIdFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder getRoleIdOrBuilder(
          int index) {
        if (roleIdBuilder_ == null) {
          return roleId_.get(index);  } else {
          return roleIdBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
           getRoleIdOrBuilderList() {
        if (roleIdBuilder_ != null) {
          return roleIdBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(roleId_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addRoleIdBuilder() {
        return getRoleIdFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_key_value.Builder addRoleIdBuilder(
          int index) {
        return getRoleIdFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_key_value.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_key_value role_id = 1;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_key_value.Builder> 
           getRoleIdBuilderList() {
        return getRoleIdFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder> 
          getRoleIdFieldBuilder() {
        if (roleIdBuilder_ == null) {
          roleIdBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_key_value, org.gof.demo.worldsrv.msg.Define.p_key_value.Builder, org.gof.demo.worldsrv.msg.Define.p_key_valueOrBuilder>(
                  roleId_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          roleId_ = null;
        }
        return roleIdBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.ban_chat_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.ban_chat_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<ban_chat_s2c>
        PARSER = new com.google.protobuf.AbstractParser<ban_chat_s2c>() {
      @java.lang.Override
      public ban_chat_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<ban_chat_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ban_chat_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.ban_chat_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface chat_channel_list_c2sOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_channel_list_c2s)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_channel_list_c2s}
   */
  public static final class chat_channel_list_c2s extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_channel_list_c2s)
      chat_channel_list_c2sOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_channel_list_c2s.newBuilder() to construct.
    private chat_channel_list_c2s(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_channel_list_c2s() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_channel_list_c2s();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_c2s_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_c2s_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s.class, org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s other = (org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s) obj;

      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_channel_list_c2s}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_channel_list_c2s)
        org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2sOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_c2s_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_c2s_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s.class, org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_c2s_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s build() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s result = new org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_channel_list_c2s)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_channel_list_c2s)
    private static final org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_channel_list_c2s>
        PARSER = new com.google.protobuf.AbstractParser<chat_channel_list_c2s>() {
      @java.lang.Override
      public chat_channel_list_c2s parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_channel_list_c2s> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_channel_list_c2s> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_c2s getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface chat_channel_list_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_channel_list_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated uint32 chat_channel_list = 1 [packed = false];</code>
     * @return A list containing the chatChannelList.
     */
    java.util.List<java.lang.Integer> getChatChannelListList();
    /**
     * <code>repeated uint32 chat_channel_list = 1 [packed = false];</code>
     * @return The count of chatChannelList.
     */
    int getChatChannelListCount();
    /**
     * <code>repeated uint32 chat_channel_list = 1 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The chatChannelList at the given index.
     */
    int getChatChannelList(int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_channel_list_s2c}
   */
  public static final class chat_channel_list_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_channel_list_s2c)
      chat_channel_list_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_channel_list_s2c.newBuilder() to construct.
    private chat_channel_list_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_channel_list_s2c() {
      chatChannelList_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_channel_list_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c.Builder.class);
    }

    public static final int CHAT_CHANNEL_LIST_FIELD_NUMBER = 1;
    @SuppressWarnings("serial")
    private com.google.protobuf.Internal.IntList chatChannelList_ =
        emptyIntList();
    /**
     * <code>repeated uint32 chat_channel_list = 1 [packed = false];</code>
     * @return A list containing the chatChannelList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getChatChannelListList() {
      return chatChannelList_;
    }
    /**
     * <code>repeated uint32 chat_channel_list = 1 [packed = false];</code>
     * @return The count of chatChannelList.
     */
    public int getChatChannelListCount() {
      return chatChannelList_.size();
    }
    /**
     * <code>repeated uint32 chat_channel_list = 1 [packed = false];</code>
     * @param index The index of the element to return.
     * @return The chatChannelList at the given index.
     */
    public int getChatChannelList(int index) {
      return chatChannelList_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < chatChannelList_.size(); i++) {
        output.writeUInt32(1, chatChannelList_.getInt(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < chatChannelList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeUInt32SizeNoTag(chatChannelList_.getInt(i));
        }
        size += dataSize;
        size += 1 * getChatChannelListList().size();
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c other = (org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c) obj;

      if (!getChatChannelListList()
          .equals(other.getChatChannelListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getChatChannelListCount() > 0) {
        hash = (37 * hash) + CHAT_CHANNEL_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getChatChannelListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_channel_list_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_channel_list_s2c)
        org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        chatChannelList_ = emptyIntList();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c build() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c result = new org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          chatChannelList_.makeImmutable();
          result.chatChannelList_ = chatChannelList_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c.getDefaultInstance()) return this;
        if (!other.chatChannelList_.isEmpty()) {
          if (chatChannelList_.isEmpty()) {
            chatChannelList_ = other.chatChannelList_;
            chatChannelList_.makeImmutable();
            bitField0_ |= 0x00000001;
          } else {
            ensureChatChannelListIsMutable();
            chatChannelList_.addAll(other.chatChannelList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                int v = input.readUInt32();
                ensureChatChannelListIsMutable();
                chatChannelList_.addInt(v);
                break;
              } // case 8
              case 10: {
                int length = input.readRawVarint32();
                int limit = input.pushLimit(length);
                ensureChatChannelListIsMutable();
                while (input.getBytesUntilLimit() > 0) {
                  chatChannelList_.addInt(input.readUInt32());
                }
                input.popLimit(limit);
                break;
              } // case 10
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList chatChannelList_ = emptyIntList();
      private void ensureChatChannelListIsMutable() {
        if (!chatChannelList_.isModifiable()) {
          chatChannelList_ = makeMutableCopy(chatChannelList_);
        }
        bitField0_ |= 0x00000001;
      }
      /**
       * <code>repeated uint32 chat_channel_list = 1 [packed = false];</code>
       * @return A list containing the chatChannelList.
       */
      public java.util.List<java.lang.Integer>
          getChatChannelListList() {
        chatChannelList_.makeImmutable();
        return chatChannelList_;
      }
      /**
       * <code>repeated uint32 chat_channel_list = 1 [packed = false];</code>
       * @return The count of chatChannelList.
       */
      public int getChatChannelListCount() {
        return chatChannelList_.size();
      }
      /**
       * <code>repeated uint32 chat_channel_list = 1 [packed = false];</code>
       * @param index The index of the element to return.
       * @return The chatChannelList at the given index.
       */
      public int getChatChannelList(int index) {
        return chatChannelList_.getInt(index);
      }
      /**
       * <code>repeated uint32 chat_channel_list = 1 [packed = false];</code>
       * @param index The index to set the value at.
       * @param value The chatChannelList to set.
       * @return This builder for chaining.
       */
      public Builder setChatChannelList(
          int index, int value) {

        ensureChatChannelListIsMutable();
        chatChannelList_.setInt(index, value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 chat_channel_list = 1 [packed = false];</code>
       * @param value The chatChannelList to add.
       * @return This builder for chaining.
       */
      public Builder addChatChannelList(int value) {

        ensureChatChannelListIsMutable();
        chatChannelList_.addInt(value);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 chat_channel_list = 1 [packed = false];</code>
       * @param values The chatChannelList to add.
       * @return This builder for chaining.
       */
      public Builder addAllChatChannelList(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureChatChannelListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, chatChannelList_);
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>repeated uint32 chat_channel_list = 1 [packed = false];</code>
       * @return This builder for chaining.
       */
      public Builder clearChatChannelList() {
        chatChannelList_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_channel_list_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_channel_list_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_channel_list_s2c>
        PARSER = new com.google.protobuf.AbstractParser<chat_channel_list_s2c>() {
      @java.lang.Override
      public chat_channel_list_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_channel_list_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_channel_list_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.chat_channel_list_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface chat_channel_change_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.chat_channel_change_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    int getType();

    /**
     * <code>uint32 chat_channel = 2;</code>
     * @return The chatChannel.
     */
    int getChatChannel();
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_channel_change_s2c}
   */
  public static final class chat_channel_change_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.chat_channel_change_s2c)
      chat_channel_change_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use chat_channel_change_s2c.newBuilder() to construct.
    private chat_channel_change_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private chat_channel_change_s2c() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new chat_channel_change_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_change_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_change_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c.Builder.class);
    }

    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_ = 0;
    /**
     * <code>uint32 type = 1;</code>
     * @return The type.
     */
    @java.lang.Override
    public int getType() {
      return type_;
    }

    public static final int CHAT_CHANNEL_FIELD_NUMBER = 2;
    private int chatChannel_ = 0;
    /**
     * <code>uint32 chat_channel = 2;</code>
     * @return The chatChannel.
     */
    @java.lang.Override
    public int getChatChannel() {
      return chatChannel_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (type_ != 0) {
        output.writeUInt32(1, type_);
      }
      if (chatChannel_ != 0) {
        output.writeUInt32(2, chatChannel_);
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (type_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(1, type_);
      }
      if (chatChannel_ != 0) {
        size += com.google.protobuf.CodedOutputStream
          .computeUInt32Size(2, chatChannel_);
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c other = (org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c) obj;

      if (getType()
          != other.getType()) return false;
      if (getChatChannel()
          != other.getChatChannel()) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (37 * hash) + TYPE_FIELD_NUMBER;
      hash = (53 * hash) + getType();
      hash = (37 * hash) + CHAT_CHANNEL_FIELD_NUMBER;
      hash = (53 * hash) + getChatChannel();
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.chat_channel_change_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.chat_channel_change_s2c)
        org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_change_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_change_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        type_ = 0;
        chatChannel_ = 0;
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_chat_channel_change_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c build() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c result = new org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c(this);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c result) {
        int from_bitField0_ = bitField0_;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.type_ = type_;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.chatChannel_ = chatChannel_;
        }
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c.getDefaultInstance()) return this;
        if (other.getType() != 0) {
          setType(other.getType());
        }
        if (other.getChatChannel() != 0) {
          setChatChannel(other.getChatChannel());
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 8: {
                type_ = input.readUInt32();
                bitField0_ |= 0x00000001;
                break;
              } // case 8
              case 16: {
                chatChannel_ = input.readUInt32();
                bitField0_ |= 0x00000002;
                break;
              } // case 16
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private int type_ ;
      /**
       * <code>uint32 type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public int getType() {
        return type_;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(int value) {

        type_ = value;
        bitField0_ |= 0x00000001;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int chatChannel_ ;
      /**
       * <code>uint32 chat_channel = 2;</code>
       * @return The chatChannel.
       */
      @java.lang.Override
      public int getChatChannel() {
        return chatChannel_;
      }
      /**
       * <code>uint32 chat_channel = 2;</code>
       * @param value The chatChannel to set.
       * @return This builder for chaining.
       */
      public Builder setChatChannel(int value) {

        chatChannel_ = value;
        bitField0_ |= 0x00000002;
        onChanged();
        return this;
      }
      /**
       * <code>uint32 chat_channel = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearChatChannel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        chatChannel_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.chat_channel_change_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.chat_channel_change_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<chat_channel_change_s2c>
        PARSER = new com.google.protobuf.AbstractParser<chat_channel_change_s2c>() {
      @java.lang.Override
      public chat_channel_change_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<chat_channel_change_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<chat_channel_change_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.chat_channel_change_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface emoji_info_s2cOrBuilder extends
      // @@protoc_insertion_point(interface_extends:org.gof.demo.worldsrv.msg.emoji_info_s2c)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
     */
    java.util.List<org.gof.demo.worldsrv.msg.Define.p_emoji> 
        getEmojiListList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_emoji getEmojiList(int index);
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
     */
    int getEmojiListCount();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
     */
    java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_emojiOrBuilder> 
        getEmojiListOrBuilderList();
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
     */
    org.gof.demo.worldsrv.msg.Define.p_emojiOrBuilder getEmojiListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code org.gof.demo.worldsrv.msg.emoji_info_s2c}
   */
  public static final class emoji_info_s2c extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:org.gof.demo.worldsrv.msg.emoji_info_s2c)
      emoji_info_s2cOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use emoji_info_s2c.newBuilder() to construct.
    private emoji_info_s2c(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private emoji_info_s2c() {
      emojiList_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new emoji_info_s2c();
    }

    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_emoji_info_s2c_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_emoji_info_s2c_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c.Builder.class);
    }

    public static final int EMOJI_LIST_FIELD_NUMBER = 2;
    @SuppressWarnings("serial")
    private java.util.List<org.gof.demo.worldsrv.msg.Define.p_emoji> emojiList_;
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<org.gof.demo.worldsrv.msg.Define.p_emoji> getEmojiListList() {
      return emojiList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_emojiOrBuilder> 
        getEmojiListOrBuilderList() {
      return emojiList_;
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
     */
    @java.lang.Override
    public int getEmojiListCount() {
      return emojiList_.size();
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_emoji getEmojiList(int index) {
      return emojiList_.get(index);
    }
    /**
     * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
     */
    @java.lang.Override
    public org.gof.demo.worldsrv.msg.Define.p_emojiOrBuilder getEmojiListOrBuilder(
        int index) {
      return emojiList_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < emojiList_.size(); i++) {
        output.writeMessage(2, emojiList_.get(i));
      }
      getUnknownFields().writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < emojiList_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, emojiList_.get(i));
      }
      size += getUnknownFields().getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c)) {
        return super.equals(obj);
      }
      org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c other = (org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c) obj;

      if (!getEmojiListList()
          .equals(other.getEmojiListList())) return false;
      if (!getUnknownFields().equals(other.getUnknownFields())) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getEmojiListCount() > 0) {
        hash = (37 * hash) + EMOJI_LIST_FIELD_NUMBER;
        hash = (53 * hash) + getEmojiListList().hashCode();
      }
      hash = (29 * hash) + getUnknownFields().hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code org.gof.demo.worldsrv.msg.emoji_info_s2c}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:org.gof.demo.worldsrv.msg.emoji_info_s2c)
        org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2cOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_emoji_info_s2c_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_emoji_info_s2c_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c.class, org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c.Builder.class);
      }

      // Construct using org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c.newBuilder()
      private Builder() {

      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);

      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        bitField0_ = 0;
        if (emojiListBuilder_ == null) {
          emojiList_ = java.util.Collections.emptyList();
        } else {
          emojiList_ = null;
          emojiListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.internal_static_org_gof_demo_worldsrv_msg_emoji_info_s2c_descriptor;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c getDefaultInstanceForType() {
        return org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c.getDefaultInstance();
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c build() {
        org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c buildPartial() {
        org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c result = new org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c(this);
        buildPartialRepeatedFields(result);
        if (bitField0_ != 0) { buildPartial0(result); }
        onBuilt();
        return result;
      }

      private void buildPartialRepeatedFields(org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c result) {
        if (emojiListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            emojiList_ = java.util.Collections.unmodifiableList(emojiList_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.emojiList_ = emojiList_;
        } else {
          result.emojiList_ = emojiListBuilder_.build();
        }
      }

      private void buildPartial0(org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c result) {
        int from_bitField0_ = bitField0_;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c) {
          return mergeFrom((org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c other) {
        if (other == org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c.getDefaultInstance()) return this;
        if (emojiListBuilder_ == null) {
          if (!other.emojiList_.isEmpty()) {
            if (emojiList_.isEmpty()) {
              emojiList_ = other.emojiList_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureEmojiListIsMutable();
              emojiList_.addAll(other.emojiList_);
            }
            onChanged();
          }
        } else {
          if (!other.emojiList_.isEmpty()) {
            if (emojiListBuilder_.isEmpty()) {
              emojiListBuilder_.dispose();
              emojiListBuilder_ = null;
              emojiList_ = other.emojiList_;
              bitField0_ = (bitField0_ & ~0x00000001);
              emojiListBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getEmojiListFieldBuilder() : null;
            } else {
              emojiListBuilder_.addAllMessages(other.emojiList_);
            }
          }
        }
        this.mergeUnknownFields(other.getUnknownFields());
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        if (extensionRegistry == null) {
          throw new java.lang.NullPointerException();
        }
        try {
          boolean done = false;
          while (!done) {
            int tag = input.readTag();
            switch (tag) {
              case 0:
                done = true;
                break;
              case 18: {
                org.gof.demo.worldsrv.msg.Define.p_emoji m =
                    input.readMessage(
                        org.gof.demo.worldsrv.msg.Define.p_emoji.parser(),
                        extensionRegistry);
                if (emojiListBuilder_ == null) {
                  ensureEmojiListIsMutable();
                  emojiList_.add(m);
                } else {
                  emojiListBuilder_.addMessage(m);
                }
                break;
              } // case 18
              default: {
                if (!super.parseUnknownField(input, extensionRegistry, tag)) {
                  done = true; // was an endgroup tag
                }
                break;
              } // default:
            } // switch (tag)
          } // while (!done)
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.unwrapIOException();
        } finally {
          onChanged();
        } // finally
        return this;
      }
      private int bitField0_;

      private java.util.List<org.gof.demo.worldsrv.msg.Define.p_emoji> emojiList_ =
        java.util.Collections.emptyList();
      private void ensureEmojiListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          emojiList_ = new java.util.ArrayList<org.gof.demo.worldsrv.msg.Define.p_emoji>(emojiList_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_emoji, org.gof.demo.worldsrv.msg.Define.p_emoji.Builder, org.gof.demo.worldsrv.msg.Define.p_emojiOrBuilder> emojiListBuilder_;

      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_emoji> getEmojiListList() {
        if (emojiListBuilder_ == null) {
          return java.util.Collections.unmodifiableList(emojiList_);
        } else {
          return emojiListBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public int getEmojiListCount() {
        if (emojiListBuilder_ == null) {
          return emojiList_.size();
        } else {
          return emojiListBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_emoji getEmojiList(int index) {
        if (emojiListBuilder_ == null) {
          return emojiList_.get(index);
        } else {
          return emojiListBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public Builder setEmojiList(
          int index, org.gof.demo.worldsrv.msg.Define.p_emoji value) {
        if (emojiListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEmojiListIsMutable();
          emojiList_.set(index, value);
          onChanged();
        } else {
          emojiListBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public Builder setEmojiList(
          int index, org.gof.demo.worldsrv.msg.Define.p_emoji.Builder builderForValue) {
        if (emojiListBuilder_ == null) {
          ensureEmojiListIsMutable();
          emojiList_.set(index, builderForValue.build());
          onChanged();
        } else {
          emojiListBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public Builder addEmojiList(org.gof.demo.worldsrv.msg.Define.p_emoji value) {
        if (emojiListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEmojiListIsMutable();
          emojiList_.add(value);
          onChanged();
        } else {
          emojiListBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public Builder addEmojiList(
          int index, org.gof.demo.worldsrv.msg.Define.p_emoji value) {
        if (emojiListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureEmojiListIsMutable();
          emojiList_.add(index, value);
          onChanged();
        } else {
          emojiListBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public Builder addEmojiList(
          org.gof.demo.worldsrv.msg.Define.p_emoji.Builder builderForValue) {
        if (emojiListBuilder_ == null) {
          ensureEmojiListIsMutable();
          emojiList_.add(builderForValue.build());
          onChanged();
        } else {
          emojiListBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public Builder addEmojiList(
          int index, org.gof.demo.worldsrv.msg.Define.p_emoji.Builder builderForValue) {
        if (emojiListBuilder_ == null) {
          ensureEmojiListIsMutable();
          emojiList_.add(index, builderForValue.build());
          onChanged();
        } else {
          emojiListBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public Builder addAllEmojiList(
          java.lang.Iterable<? extends org.gof.demo.worldsrv.msg.Define.p_emoji> values) {
        if (emojiListBuilder_ == null) {
          ensureEmojiListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, emojiList_);
          onChanged();
        } else {
          emojiListBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public Builder clearEmojiList() {
        if (emojiListBuilder_ == null) {
          emojiList_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          emojiListBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public Builder removeEmojiList(int index) {
        if (emojiListBuilder_ == null) {
          ensureEmojiListIsMutable();
          emojiList_.remove(index);
          onChanged();
        } else {
          emojiListBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_emoji.Builder getEmojiListBuilder(
          int index) {
        return getEmojiListFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_emojiOrBuilder getEmojiListOrBuilder(
          int index) {
        if (emojiListBuilder_ == null) {
          return emojiList_.get(index);  } else {
          return emojiListBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public java.util.List<? extends org.gof.demo.worldsrv.msg.Define.p_emojiOrBuilder> 
           getEmojiListOrBuilderList() {
        if (emojiListBuilder_ != null) {
          return emojiListBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(emojiList_);
        }
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_emoji.Builder addEmojiListBuilder() {
        return getEmojiListFieldBuilder().addBuilder(
            org.gof.demo.worldsrv.msg.Define.p_emoji.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public org.gof.demo.worldsrv.msg.Define.p_emoji.Builder addEmojiListBuilder(
          int index) {
        return getEmojiListFieldBuilder().addBuilder(
            index, org.gof.demo.worldsrv.msg.Define.p_emoji.getDefaultInstance());
      }
      /**
       * <code>repeated .org.gof.demo.worldsrv.msg.p_emoji emoji_list = 2;</code>
       */
      public java.util.List<org.gof.demo.worldsrv.msg.Define.p_emoji.Builder> 
           getEmojiListBuilderList() {
        return getEmojiListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          org.gof.demo.worldsrv.msg.Define.p_emoji, org.gof.demo.worldsrv.msg.Define.p_emoji.Builder, org.gof.demo.worldsrv.msg.Define.p_emojiOrBuilder> 
          getEmojiListFieldBuilder() {
        if (emojiListBuilder_ == null) {
          emojiListBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              org.gof.demo.worldsrv.msg.Define.p_emoji, org.gof.demo.worldsrv.msg.Define.p_emoji.Builder, org.gof.demo.worldsrv.msg.Define.p_emojiOrBuilder>(
                  emojiList_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          emojiList_ = null;
        }
        return emojiListBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:org.gof.demo.worldsrv.msg.emoji_info_s2c)
    }

    // @@protoc_insertion_point(class_scope:org.gof.demo.worldsrv.msg.emoji_info_s2c)
    private static final org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c();
    }

    public static org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    private static final com.google.protobuf.Parser<emoji_info_s2c>
        PARSER = new com.google.protobuf.AbstractParser<emoji_info_s2c>() {
      @java.lang.Override
      public emoji_info_s2c parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        Builder builder = newBuilder();
        try {
          builder.mergeFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          throw e.setUnfinishedMessage(builder.buildPartial());
        } catch (com.google.protobuf.UninitializedMessageException e) {
          throw e.asInvalidProtocolBufferException().setUnfinishedMessage(builder.buildPartial());
        } catch (java.io.IOException e) {
          throw new com.google.protobuf.InvalidProtocolBufferException(e)
              .setUnfinishedMessage(builder.buildPartial());
        }
        return builder.buildPartial();
      }
    };

    public static com.google.protobuf.Parser<emoji_info_s2c> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<emoji_info_s2c> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public org.gof.demo.worldsrv.msg.MsgChat.emoji_info_s2c getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_message_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_message_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_message_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_message_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_history_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_history_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_history_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_history_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_history_read_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_history_read_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_history_read_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_history_read_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_ban_chat_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_ban_chat_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_ban_chat_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_ban_chat_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_c2s_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_c2s_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_chat_channel_change_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_chat_channel_change_s2c_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_org_gof_demo_worldsrv_msg_emoji_info_s2c_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_org_gof_demo_worldsrv_msg_emoji_info_s2c_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\016msg.chat.proto\022\031org.gof.demo.worldsrv." +
      "msg\032\roptions.proto\032\014define.proto\"\323\001\n\020cha" +
      "t_message_c2s\022\017\n\007channel\030\001 \001(\r\022\021\n\ttarget" +
      "_id\030\002 \001(\004\022\024\n\014content_type\030\003 \001(\r\022\017\n\007conte" +
      "nt\030\004 \001(\t\0220\n\005links\030\005 \003(\0132!.org.gof.demo.w" +
      "orldsrv.msg.p_link\022;\n\004args\030\006 \003(\0132-.org.g" +
      "of.demo.worldsrv.msg.p_key_value_string:" +
      "\005\210\303\032\201\014\"s\n\020chat_message_s2c\022\017\n\007channel\030\001 " +
      "\001(\r\022\021\n\ttarget_id\030\002 \001(\004\0224\n\tchat_info\030\003 \001(" +
      "\0132!.org.gof.demo.worldsrv.msg.p_chat:\005\210\303" +
      "\032\201\014\"=\n\020chat_history_c2s\022\017\n\007channel\030\001 \001(\r" +
      "\022\021\n\ttarget_id\030\002 \001(\004:\005\210\303\032\202\014\"\210\001\n\020chat_hist" +
      "ory_s2c\022\017\n\007channel\030\001 \001(\r\022\021\n\ttarget_id\030\002 " +
      "\001(\004\0227\n\014chat_history\030\003 \003(\0132!.org.gof.demo" +
      ".worldsrv.msg.p_chat\022\020\n\010is_block\030\004 \001(\r:\005" +
      "\210\303\032\202\014\"2\n\031chat_friend_info_list_c2s\022\016\n\002id" +
      "\030\001 \003(\004B\002\020\000:\005\210\303\032\203\014\"f\n\031chat_friend_info_li" +
      "st_s2c\022B\n\020friend_info_list\030\001 \003(\0132(.org.g" +
      "of.demo.worldsrv.msg.p_chat_friend:\005\210\303\032\203" +
      "\014\"1\n\025chat_history_read_c2s\022\021\n\ttarget_id\030" +
      "\001 \001(\004:\005\210\303\032\204\014\"1\n\025chat_history_read_s2c\022\021\n" +
      "\ttarget_id\030\001 \001(\004:\005\210\303\032\204\014\"\025\n\014ban_chat_c2s:" +
      "\005\210\303\032\205\014\"N\n\014ban_chat_s2c\0227\n\007role_id\030\001 \003(\0132" +
      "&.org.gof.demo.worldsrv.msg.p_key_value:" +
      "\005\210\303\032\205\014\"\036\n\025chat_channel_list_c2s:\005\210\303\032\206\014\"=" +
      "\n\025chat_channel_list_s2c\022\035\n\021chat_channel_" +
      "list\030\001 \003(\rB\002\020\000:\005\210\303\032\206\014\"D\n\027chat_channel_ch" +
      "ange_s2c\022\014\n\004type\030\001 \001(\r\022\024\n\014chat_channel\030\002" +
      " \001(\r:\005\210\303\032\207\014\"P\n\016emoji_info_s2c\0226\n\nemoji_l" +
      "ist\030\002 \003(\0132\".org.gof.demo.worldsrv.msg.p_" +
      "emoji:\006\210\303\032\310\304\002b\006proto3"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          org.gof.demo.worldsrv.msg.Options.getDescriptor(),
          org.gof.demo.worldsrv.msg.Define.getDescriptor(),
        });
    internal_static_org_gof_demo_worldsrv_msg_chat_message_c2s_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_org_gof_demo_worldsrv_msg_chat_message_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_message_c2s_descriptor,
        new java.lang.String[] { "Channel", "TargetId", "ContentType", "Content", "Links", "Args", });
    internal_static_org_gof_demo_worldsrv_msg_chat_message_s2c_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_org_gof_demo_worldsrv_msg_chat_message_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_message_s2c_descriptor,
        new java.lang.String[] { "Channel", "TargetId", "ChatInfo", });
    internal_static_org_gof_demo_worldsrv_msg_chat_history_c2s_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_org_gof_demo_worldsrv_msg_chat_history_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_history_c2s_descriptor,
        new java.lang.String[] { "Channel", "TargetId", });
    internal_static_org_gof_demo_worldsrv_msg_chat_history_s2c_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_org_gof_demo_worldsrv_msg_chat_history_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_history_s2c_descriptor,
        new java.lang.String[] { "Channel", "TargetId", "ChatHistory", "IsBlock", });
    internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_c2s_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_c2s_descriptor,
        new java.lang.String[] { "Id", });
    internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_s2c_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_friend_info_list_s2c_descriptor,
        new java.lang.String[] { "FriendInfoList", });
    internal_static_org_gof_demo_worldsrv_msg_chat_history_read_c2s_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_org_gof_demo_worldsrv_msg_chat_history_read_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_history_read_c2s_descriptor,
        new java.lang.String[] { "TargetId", });
    internal_static_org_gof_demo_worldsrv_msg_chat_history_read_s2c_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_org_gof_demo_worldsrv_msg_chat_history_read_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_history_read_s2c_descriptor,
        new java.lang.String[] { "TargetId", });
    internal_static_org_gof_demo_worldsrv_msg_ban_chat_c2s_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_org_gof_demo_worldsrv_msg_ban_chat_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_ban_chat_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_ban_chat_s2c_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_org_gof_demo_worldsrv_msg_ban_chat_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_ban_chat_s2c_descriptor,
        new java.lang.String[] { "RoleId", });
    internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_c2s_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_c2s_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_c2s_descriptor,
        new java.lang.String[] { });
    internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_s2c_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_channel_list_s2c_descriptor,
        new java.lang.String[] { "ChatChannelList", });
    internal_static_org_gof_demo_worldsrv_msg_chat_channel_change_s2c_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_org_gof_demo_worldsrv_msg_chat_channel_change_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_chat_channel_change_s2c_descriptor,
        new java.lang.String[] { "Type", "ChatChannel", });
    internal_static_org_gof_demo_worldsrv_msg_emoji_info_s2c_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_org_gof_demo_worldsrv_msg_emoji_info_s2c_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_org_gof_demo_worldsrv_msg_emoji_info_s2c_descriptor,
        new java.lang.String[] { "EmojiList", });
    com.google.protobuf.ExtensionRegistry registry =
        com.google.protobuf.ExtensionRegistry.newInstance();
    registry.add(org.gof.demo.worldsrv.msg.Options.msgid);
    com.google.protobuf.Descriptors.FileDescriptor
        .internalUpdateFileDescriptor(descriptor, registry);
    org.gof.demo.worldsrv.msg.Options.getDescriptor();
    org.gof.demo.worldsrv.msg.Define.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}
