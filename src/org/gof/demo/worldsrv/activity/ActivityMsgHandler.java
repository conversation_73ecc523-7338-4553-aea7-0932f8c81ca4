package org.gof.demo.worldsrv.activity;

import org.gof.core.support.ParamKey;
import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.msg.MsgIds;
import org.gof.demo.worldsrv.msg.MsgWarToken;
import org.gof.demo.worldsrv.share.ShareManager;

/**
 * 活动
 * <AUTHOR>
 * @Date 2024/6/12
 * @Param 
 */
public class ActivityMsgHandler {
    /**
    * 战争令牌信息C2S消息
    */
    @MsgReceiver(MsgAct.act_war_token_info_c2s.class)
    public void act_war_token_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        ActivityManager.inst().on_act_war_token_info_c2s(humanObj);
    }

    /**
     * 战争令牌任务奖励
     * @param task_id 任务ID
     */
    @MsgReceiver(MsgAct.act_war_token_task_reward_c2s.class)
    public void act_war_token_task_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_war_token_task_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_war_token_task_reward_c2s(humanObj, msg.getTaskId());
    }

    /**
     * 战争令牌等级奖励C2S消息
     * @param take_lev 等级
     * @param type 奖励类型
     */
    @MsgReceiver(MsgAct.act_war_token_lev_reward_c2s.class)
    public void act_war_token_lev_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_war_token_lev_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_war_token_lev_reward_c2s(humanObj, msg.getTakeLev(), msg.getType());
    }

    /**
     * 战争令牌追加奖励C2S消息
     */
    @MsgReceiver(MsgAct.act_war_token_add_reward_c2s.class)
    public void act_war_token_add_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        ActivityManager.inst().on_act_war_token_add_reward_c2s(humanObj);
    }

    /**
     * 战争令牌追加奖励C2S消息
     */
    @MsgReceiver(MsgWarToken.war_token_info_c2s.class)
    public void war_token_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgWarToken.war_token_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_war_token_info_c2s(humanObj,msg.getId());
    }


    /**
     * 战争令牌任务奖励
     * @param task_id 任务ID
     */
    @MsgReceiver(MsgWarToken.war_token_task_reward_c2s.class)
    public void war_token_task_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgWarToken.war_token_task_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_war_token_task_reward_c2s(humanObj,msg.getId(),msg.getTaskId());
    }

    /**
     * 战争令牌等级奖励C2S消息
     * @param take_lev 等级
     * @param type 奖励类型
     */
    @MsgReceiver(MsgWarToken.war_token_lev_reward_c2s.class)
    public void war_token_lev_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgWarToken.war_token_lev_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_war_token_lev_reward_c2s(humanObj,msg.getId(),msg.getTakeLev(),msg.getType());
    }

    /**
     * 战争令牌追加奖励C2S消息
     */
    @MsgReceiver(MsgWarToken.war_token_add_reward_c2s.class)
    public void war_token_add_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgWarToken.war_token_add_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_war_token_add_reward_c2s(humanObj,msg.getId());
    }

    /**
     * 战争令牌特殊奖励C2S消息
     * @param cfg_id 配置ID
     */
    @MsgReceiver(MsgWarToken.war_token_claim_special_reward_c2s.class)
    public void war_token_claim_special_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgWarToken.war_token_claim_special_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_war_token_claim_special_reward_c2s(humanObj,msg.getId(),msg.getCfgId());
    }

    /**
     * 购买战令等级
     * id 配置ID
     * num 数量
     */
    @MsgReceiver(MsgWarToken.war_token_buy_level_c2s.class)
    public void war_token_buy_lev_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgWarToken.war_token_buy_level_c2s msg = param.getMsg();
        ActivityManager.inst().on_war_token_buy_level_c2s(humanObj,msg.getId(), msg.getNum());
    }


    /**
     * 问卷信息C2S消息
     */
    @MsgReceiver(MsgAct.questionnaire_info_c2s.class)
    public void questionnaire_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_questionnaire_info_c2s(humanObj);
    }

    /**
     * 提交问卷C2S消息
     * @param ques_id 问卷ID
     * @param opt 选项
     */
    @MsgReceiver(MsgAct.questionnaire_take_c2s.class)
    public void questionnaire_take_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.questionnaire_take_c2s msg = param.getMsg();
        //ActivityManager.inst().on_questionnaire_take_c2s(humanObj, msg.getQuesId(), msg.getOpt());
    }

    /**
     * 活动列表C2S消息
     */
    @MsgReceiver(MsgAct.act_list_c2s.class)
    public void act_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        ActivityManager.inst().on_act_list_c2s(humanObj);
    }

    /**
     * 活动任务更新C2S消息
     * @param type 类型
     */
    @MsgReceiver(MsgAct.act_task_update_c2s.class)
    public void act_task_update_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_task_update_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_task_update_c2s(humanObj, msg.getType());
    }

    /**
     * 活动任务奖励C2S消息
     * @param type 类型
     * @param task_id 任务ID
     * @param group_id 组ID
     */
    @MsgReceiver(MsgAct.act_task_reward_c2s.class)
    public void act_task_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_task_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_task_reward_c2s(humanObj, msg.getType(), msg.getTaskId(), msg.getGroupId());
    }

    /**
     * 坐骑嘉年华信息C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_mount_carnival_info_c2s.class)
    public void act_mount_carnival_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_mount_carnival_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_mount_carnival_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 坐骑嘉年华抽奖C2S消息
     *
     * @param act_type 活动类型
     * @param type     抽奖类型
     */
    @MsgReceiver(MsgAct.act_mount_carnival_draw_c2s.class)
    public void act_mount_carnival_draw_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_mount_carnival_draw_c2s msg = param.getMsg();
        ActivityManager.inst().mount_carnival_draw(humanObj, msg.getActType(), msg.getType());
    }

    /**
     * 坐骑嘉年华累计奖励C2S消息
     * @param act_type 活动类型
     * @param id 奖励ID
     */
    @MsgReceiver(MsgAct.act_mount_carnival_count_reward_c2s.class)
    public void act_mount_carnival_count_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_mount_carnival_count_reward_c2s msg = param.getMsg();
        ActivityManager.inst().mount_carnival_count_reward(humanObj, msg.getActType(), msg.getId());
    }

    /**
     * 枕头信息C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_pillow_info_c2s.class)
    public void act_pillow_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_pillow_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_pillow_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 投掷枕头C2S消息
     * @param act_type 活动类型
     * @param times 次数
     */
    @MsgReceiver(MsgAct.act_pillow_throw_c2s.class)
    public void act_pillow_throw_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_pillow_throw_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_pillow_throw_c2s(humanObj, msg.getActType(), msg.getTimes());
    }

    /**
     * 领取枕头奖励C2S消息
     * @param act_type 活动类型
     * @param type 类型
     * @param id ID
     */
    @MsgReceiver(MsgAct.act_pillow_take_c2s.class)
    public void act_pillow_take_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_pillow_take_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_pillow_take_c2s(humanObj, msg.getActType(), msg.getType(), msg.getId());
    }

    /**
     * 活动登录信息C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_login_info_c2s.class)
    public void act_login_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_login_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_login_info(humanObj, msg.getActType());
    }

    /**
     * 活动登录奖励C2S消息
     * @param act_type 活动类型
     * @param day 天数
     */
    @MsgReceiver(MsgAct.act_login_reward_c2s.class)
    public void act_login_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_login_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_login_reward(humanObj, msg.getActType(), msg.getDay(), msg.getGear());
    }

    /**
     * 活动七夕战斗通行证信息C2S消息
     */
    @MsgReceiver(MsgAct.act_tanabata_battlepass_info_c2s.class)
    public void act_tanabata_battlepass_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_tanabata_battlepass_info_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_tanabata_battlepass_info_c2s(humanObj, msg);
    }

    /**
     * 活动七夕搜索队友C2S消息
     * @param type 类型
     */
    @MsgReceiver(MsgAct.act_tanabata_search_teammate_c2s.class)
    public void act_tanabata_search_teammate_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_tanabata_search_teammate_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_tanabata_search_teammate_c2s(humanObj, msg);
    }

    /**
     * 活动七夕请求队友信息C2S消息
     * @param role_id 角色ID
     */
    @MsgReceiver(MsgAct.act_tanabata_ask_teammate_c2s.class)
    public void act_tanabata_ask_teammate_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_tanabata_ask_teammate_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_tanabata_ask_teammate_c2s(humanObj, msg);
    }

    /**
     * 活动七夕请求队友列表C2S消息
     */
    @MsgReceiver(MsgAct.act_tanabata_ask_list_c2s.class)
    public void act_tanabata_ask_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_tanabata_ask_list_c2s(humanObj);
    }

    /**
     * 活动七夕处理队友请求C2S消息
     * @param role_id 角色ID
     * @param type 类型
     */
    @MsgReceiver(MsgAct.act_tanabata_deal_ask_c2s.class)
    public void act_tanabata_deal_ask_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_tanabata_deal_ask_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_tanabata_deal_ask_c2s(humanObj, msg.getRoleId(), msg.getType());
    }


    /**
     * 活动七夕领取奖励C2S消息
     */
    @MsgReceiver(MsgAct.act_tanabata_get_reward_c2s.class)
    public void act_tanabata_get_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_tanabata_get_reward_c2s(humanObj);
    }

    /**
     * 活动七夕礼物提示C2S消息
     *
     * @param gift_type 礼物类型
     */
    @MsgReceiver(MsgAct.act_tanabata_gift_tips_c2s.class)
    public void act_tanabata_gift_tips_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_tanabata_gift_tips_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_tanabata_gift_tips_c2s(humanObj, msg.getGiftType());
    }

    /**
     * 活动七夕搜索鲜花C2S消息
     * @param uid 用户ID
     */
    @MsgReceiver(MsgAct.act_tanabata_flower_search_c2s.class)
    public void act_tanabata_flower_search_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_tanabata_flower_search_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_tanabata_flower_search_c2s(humanObj, msg.getActType(), msg.getUid());
    }

    /**
     * 活动七夕赠送鲜花C2S消息
     * @param role_id 角色ID
     * @param flower_id 鲜花ID
     * @param num 数量
     */
    @MsgReceiver(MsgAct.act_tanabata_give_flower_c2s.class)
    public void act_tanabata_give_flower_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_tanabata_give_flower_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_tanabata_give_flower_c2s(humanObj, msg.getActType(), msg.getRoleId(), msg.getFlowerId(), msg.getNum());
    }

    /**
     * 活动七夕鲜花历史C2S消息
     * @param type 类型
     * @param page 页码
     */
    @MsgReceiver(MsgAct.act_tanabata_flower_history_c2s.class)
    public void act_tanabata_flower_history_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_tanabata_flower_history_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_tanabata_flower_history_c2s(humanObj, msg.getActType(), msg.getType(), msg.getPage());
    }

    /**
     * 活动策略标签信息C2S消息
     */
    @MsgReceiver(MsgAct.act_strategy_tab_info_c2s.class)
    public void act_strategy_tab_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_strategy_tab_info_c2s(humanObj);
    }

    /**
     * 活动策略标签职业选择C2S消息
     *
     * @param job 职业ID
     */
    @MsgReceiver(MsgAct.act_strategy_tab_job_c2s.class)
    public void act_strategy_tab_job_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_strategy_tab_job_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_strategy_tab_job_c2s(humanObj, msg.getJob());
    }

    /**
     * 活动策略标签装备信息C2S消息
     */
    @MsgReceiver(MsgAct.act_strategy_tab_equip_c2s.class)
    public void act_strategy_tab_equip_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_strategy_tab_equip_c2s(humanObj);
    }

    /**
     * 活动策略标签装备重铸C2S消息
     * @param equip_id 装备ID
     * @param attr_list 属性列表
     */
    @MsgReceiver(MsgAct.act_strategy_tab_equip_rebuild_c2s.class)
    public void act_strategy_tab_equip_rebuild_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_strategy_tab_equip_rebuild_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_strategy_tab_equip_rebuild_c2s(humanObj, msg.getEquipId(), msg.getAttrListList());
    }

    /**
     * 活动策略标签技能解锁C2S消息
     * @param skill_id 技能ID
     */
    @MsgReceiver(MsgAct.act_strategy_tab_skill_unlock_c2s.class)
    public void act_strategy_tab_skill_unlock_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_strategy_tab_skill_unlock_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_strategy_tab_skill_unlock_c2s(humanObj, msg.getSkillId());
    }

    /**
     * 活动策略标签技能C2S消息
     * @param pos_info 位置信息
     */
    @MsgReceiver(MsgAct.act_strategy_tab_skill_c2s.class)
    public void act_strategy_tab_skill_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_strategy_tab_skill_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_strategy_tab_skill_c2s(humanObj, msg.getPosInfoList());
    }

    /**
     * 活动策略标签宠物解锁C2S消息
     * @param pet_id 宠物ID
     */
    @MsgReceiver(MsgAct.act_strategy_tab_pet_unlock_c2s.class)
    public void act_strategy_tab_pet_unlock_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_strategy_tab_pet_unlock_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_strategy_tab_pet_unlock_c2s(humanObj, msg.getPetId());
    }

    /**
     * 活动策略标签宠物C2S消息
     * @param pos_info 位置信息
     */
    @MsgReceiver(MsgAct.act_strategy_tab_pet_c2s.class)
    public void act_strategy_tab_pet_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_strategy_tab_pet_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_strategy_tab_pet_c2s(humanObj, msg.getPosInfoList());
    }

    /**
     * 活动策略总战力信息C2S消息
     * @param type 类型
     */
    @MsgReceiver(MsgAct.act_strategy_total_sp_info_c2s.class)
    public void act_strategy_total_sp_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_strategy_total_sp_info_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_strategy_total_sp_info_c2s(humanObj, msg.getType());
    }

    /**
     * 活动策略关卡信息C2S消息
     */
    @MsgReceiver(MsgAct.act_strategy_dungeon_info_c2s.class)
    public void act_strategy_dungeon_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_strategy_dungeon_info_c2s(humanObj);
    }


    /**
     * 活动策略战斗通行证领取奖励C2S消息
     * @param battle_pass_id 战斗通行证ID
     */
    @MsgReceiver(MsgAct.act_strategy_battle_pass_claim_c2s.class)
    public void act_strategy_battle_pass_claim_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_strategy_battle_pass_claim_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_strategy_battle_pass_claim_c2s(humanObj, msg.getBattlePassId());
    }

    /**
     * 活动策略每小时奖励领取C2S消息
     */
    @MsgReceiver(MsgAct.act_strategy_hour_reward_claim_c2s.class)
    public void act_strategy_hour_reward_claim_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_strategy_hour_reward_claim_c2s(humanObj);
    }

    /**
     * 活动秋季猜谜信息C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_autumn_guess_info_c2s.class)
    public void act_autumn_guess_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_autumn_guess_info_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_autumn_guess_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动秋季猜谜选择C2S消息
     * @param act_type 活动类型
     * @param guess_id 猜谜ID
     * @param choose 选择
     */
    @MsgReceiver(MsgAct.act_autumn_guess_choose_c2s.class)
    public void act_autumn_guess_choose_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_autumn_guess_choose_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_autumn_guess_choose_c2s(humanObj, msg.getActType(), msg.getGuessId(), msg.getChoose());
    }

    /**
     * 活动秋季猜谜选择C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_autumn_pig_info_c2s.class)
    public void act_autumn_pig_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_autumn_pig_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_autumn_pig_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动秋季找猪C2S消息
     * @param act_type 活动类型
     * @param pig_id 猪ID
     */
    @MsgReceiver(MsgAct.act_autumn_pig_find_c2s.class)
    public void act_autumn_pig_find_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_autumn_pig_find_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_autumn_pig_find_c2s(humanObj, msg.getActType(), msg.getPigId());
    }

    /**
     * 活动秋季愤怒的小鸟信息C2S消息
     */
    @MsgReceiver(MsgAct.act_autumn_angry_bird_info_c2s.class)
    public void act_autumn_angry_bird_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_autumn_angry_bird_info_c2s(humanObj);
    }

    /**
     * 活动秋季愤怒的小鸟射击C2S消息
     * @param shot_type 射击类型
     * @param shot_list 射击列表
     */
    @MsgReceiver(MsgAct.act_autumn_angry_bird_shot_c2s.class)
    public void act_autumn_angry_bird_shot_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_autumn_angry_bird_shot_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_autumn_angry_bird_shot_c2s(humanObj, msg.getShotType(), msg.getShotListList());
    }

    /**
     * 活动秋季愤怒的小鸟积分C2S消息
     */
    @MsgReceiver(MsgAct.act_autumn_angry_bird_point_c2s.class)
    public void act_autumn_angry_bird_point_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_autumn_angry_bird_point_c2s(humanObj);
    }

    /**
     * 活动秋季愤怒的小鸟历史记录C2S消息
     */
    @MsgReceiver(MsgAct.act_autumn_angry_bird_history_c2s.class)
    public void act_autumn_angry_bird_history_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_autumn_angry_bird_history_c2s(humanObj);
    }

    /**
     * 活动卡牌信息C2S消息
     */
    @MsgReceiver(MsgAct.act_card_info_c2s.class)
    public void act_card_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_card_info_c2s(humanObj);
    }

    /**
     * 活动卡牌请求C2S消息
     * @param card_id 卡牌ID
     * @param type 类型
     * @param role_id 角色ID
     */
    @MsgReceiver(MsgAct.act_card_ask_c2s.class)
    public void act_card_ask_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_card_ask_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_card_ask_c2s(humanObj, msg.getCardId(), msg.getType(), msg.getRoleId());
    }

    /**
     * 活动卡牌发送C2S消息
     * @param card_id 卡牌ID
     * @param type 类型
     * @param role_id 角色ID
     * @param id ID
     */
    @MsgReceiver(MsgAct.act_card_send_c2s.class)
    public void act_card_send_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_card_send_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_card_send_c2s(humanObj, msg.getCardId(), msg.getType(), msg.getRoleId(), msg.getId());
    }

    /**
     * 活动卡牌接收C2S消息
     * @param id ID
     */
    @MsgReceiver(MsgAct.act_card_recv_c2s.class)
    public void act_card_recv_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_card_recv_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_card_recv_c2s(humanObj, msg.getId());
    }

    /**
     * 活动卡牌聊天状态C2S消息
     */
    @MsgReceiver(MsgAct.act_card_chat_status_c2s.class)
    public void act_card_chat_status_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_card_chat_status_c2s(humanObj);
    }

    /**
     * 活动卡牌搜索角色C2S消息
     * @param content 内容
     */
    @MsgReceiver(MsgAct.act_card_search_role_c2s.class)
    public void act_card_search_role_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_card_search_role_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_card_search_role_c2s(humanObj, msg.getContent());
    }

    /**
     * 活动坐骑狂欢选择C2S消息
     * @param act_type 活动类型
     * @param drop_id 掉落ID
     * @param choose 选择
     */
    @MsgReceiver(MsgAct.act_mount_carnival_choose_c2s.class)
    public void act_mount_carnival_choose_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_mount_carnival_choose_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_mount_carnival_choose_c2s(humanObj, msg.getActType(), msg.getDropId(), msg.getChoose());
    }

    /**
     * 活动万圣节竞技场信息C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_halloween_arena_info_c2s.class)
    public void act_halloween_arena_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_arena_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_halloween_arena_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动万圣节竞技场战斗C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_halloween_arena_combat_c2s.class)
    public void act_halloween_arena_combat_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_arena_combat_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_halloween_arena_combat_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动万圣节竞技场结果C2S消息
     * @param act_type 活动类型
     * @param chapter 章节
     * @param result 结果
     */
    @MsgReceiver(MsgAct.act_halloween_arena_result_c2s.class)
    public void act_halloween_arena_result_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_arena_result_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_halloween_arena_result_c2s(humanObj, msg.getActType(), msg.getResult());
    }

    /**
     * 活动万圣节竞技恶作剧C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_halloween_arena_trick_c2s.class)
    public void act_halloween_arena_trick_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_arena_trick_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_halloween_arena_trick_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动万圣节竞技选择增益C2S消息
     * @param act_type 活动类型
     * @param choose_buff 选择的增益
     */
    @MsgReceiver(MsgAct.act_halloween_arena_choose_buff_c2s.class)
    public void act_halloween_arena_choose_buff_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_arena_choose_buff_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_halloween_arena_choose_buff_c2s(humanObj, msg.getActType(), msg.getChooseBuff());
    }

    @MsgReceiver(MsgAct.act_halloween_arena_buy_buff_c2s.class)
    public void act_halloween_arena_buy_buff_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_arena_buy_buff_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_halloween_arena_buy_buff_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct.act_halloween_arena_buy_pass_c2s.class)
    public void act_halloween_arena_buy_pass_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_arena_buy_pass_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_halloween_arena_buy_pass_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动万圣节3消匹配信息C2S消息
     */
    @MsgReceiver(MsgAct.act_halloween_match_3_info_c2s.class)
    public void act_halloween_match_3_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_match_3_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_halloween_match_3_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动万圣节3消匹配结果C2S消息
     * @param result 结果
     * @param chapter_id 章节ID
     * @param step 步数
     */
    @MsgReceiver(MsgAct.act_halloween_match_3_result_c2s.class)
    public void act_halloween_match_3_result_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_match_3_result_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_halloween_match_3_result_c2s(humanObj, msg.getActType(), msg.getResult(), msg.getChapterId(), msg.getStep());
    }

    /**
     * 活动万圣节团购信息C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_halloween_group_buy_info_c2s.class)
    public void act_halloween_group_buy_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_group_buy_info_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_halloween_group_buy_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动万圣节团购聊天状态C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_halloween_group_buy_chat_status_c2s.class)
    public void act_halloween_group_buy_chat_status_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_group_buy_chat_status_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_halloween_group_buy_chat_status_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动万圣节团购开始C2S消息
     * @param act_type 活动类型
     * @param type 类型
     * @param group_buy_id 团购ID
     */
    @MsgReceiver(MsgAct.act_halloween_group_buy_start_c2s.class)
    public void act_halloween_group_buy_start_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_group_buy_start_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_halloween_group_buy_start_c2s(humanObj, msg.getActType(), msg.getType(), msg.getGroupBuyId());
    }

    /**
     * 活动万圣节团购退出C2S消息
     * @param act_type 活动类型
     * @param group_buy_id 团购ID
     */
    @MsgReceiver(MsgAct.act_halloween_group_buy_quit_c2s.class)
    public void act_halloween_group_buy_quit_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_group_buy_quit_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_halloween_group_buy_quit_c2s(humanObj, msg.getActType(), msg.getGroupBuyId());
    }

    /**
     * 活动万圣节团购分享C2S消息
     * @param act_type 活动类型
     * @param group_buy_id 团购ID
     */
    @MsgReceiver(MsgAct.act_halloween_group_buy_share_c2s.class)
    public void act_halloween_group_buy_share_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_group_buy_share_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_halloween_group_buy_share_c2s(humanObj, msg.getActType(), msg.getGroupBuyId());
    }

    /**
     * 活动万圣节团购数据C2S消息
     * @param act_type 活动类型
     * @param type 类型
     * @param group_buy_id 团购ID
     */
    @MsgReceiver(MsgAct.act_halloween_group_buy_data_c2s.class)
    public void act_halloween_group_buy_data_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_group_buy_data_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_halloween_group_buy_data_c2s(humanObj, msg.getActType(), msg.getType(), msg.getGroupBuyId());
    }

    /**
     * 活动万圣节团购套餐数据C2S消息
     * @param act_type 活动类型
     * @param cfg_id 配置ID
     */
    @MsgReceiver(MsgAct.act_halloween_group_bundle_data_c2s.class)
    public void act_halloween_group_bundle_data_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_halloween_group_bundle_data_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_halloween_group_bundle_data_c2s(humanObj, msg.getActType(), msg.getCfgId());
    }

    /**
     * 活动策略折扣商城信息C2S消息
     */
    @MsgReceiver(MsgAct.act_strategy_discount_mall_info_c2s.class)
    public void act_strategy_discount_mall_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_strategy_discount_mall_info_c2s(humanObj);
    }

    /**
     * 活动策略折扣商城选择C2S消息
     * @param type 类型
     * @param cfg_id 配置ID
     */
    @MsgReceiver(MsgAct.act_strategy_discount_mall_choose_c2s.class)
    public void act_strategy_discount_mall_choose_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_strategy_discount_mall_choose_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_strategy_discount_mall_choose_c2s(humanObj, msg.getType(), msg.getCfgId());
    }

    /**
     * 活动矿山信息C2S消息
     */
    @MsgReceiver(MsgAct.act_mining_info_c2s.class)
    public void act_mining_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_mining_info_c2s(humanObj);
    }

    /**
     * 活动矿山结果C2S消息
     * @param id ID
     * @param round 回合
     * @param finish 完成
     * @param gold 金币
     * @param gift 礼物
     * @param shovel_list 铲子列表
     */
    @MsgReceiver(MsgAct.act_mining_result_c2s.class)
    public void act_mining_result_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_mining_result_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_mining_result_c2s(humanObj, msg.getId(), msg.getRound(), msg.getFinish(), msg.getGold(), msg.getGift(), msg.getShovelListList());
    }

    /**
     * 活动矿山继承C2S消息
     * @param id ID
     */
    @MsgReceiver(MsgAct.act_mining_inherit_c2s.class)
    public void act_mining_inherit_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_mining_inherit_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_mining_inherit_c2s(humanObj, msg.getId());
    }

    /**
     * 活动矿山结算C2S消息
     * @param id ID
     */
    @MsgReceiver(MsgAct.act_mining_settle_c2s.class)
    public void act_mining_settle_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_mining_settle_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_mining_settle_c2s(humanObj, msg.getId());
    }

    /**
     * 活动拼图信息C2S消息
     */
    @MsgReceiver(MsgAct.act_jigsaw_info_c2s.class)
    public void act_jigsaw_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_jigsaw_info_c2s(humanObj);
    }

    /**
     * 活动拼图请求C2S消息
     * @param card_id 卡片ID
     * @param type 类型
     * @param role_id 角色ID
     */
    @MsgReceiver(MsgAct.act_jigsaw_ask_c2s.class)
    public void act_jigsaw_ask_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_jigsaw_ask_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_jigsaw_ask_c2s(humanObj, msg.getCardId(), msg.getType(), msg.getRoleId());
    }

    /**
     * 活动拼图发送C2S消息
     * @param card_id 卡片ID
     * @param type 类型
     * @param role_id 角色ID
     * @param id ID
     */
    @MsgReceiver(MsgAct.act_jigsaw_send_c2s.class)
    public void act_jigsaw_send_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_jigsaw_send_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_jigsaw_send_c2s(humanObj, msg.getCardId(), msg.getType(), msg.getRoleId(), msg.getId());
    }

    /**
     * 活动拼图接收C2S消息
     * @param id ID
     */
    @MsgReceiver(MsgAct.act_jigsaw_recv_c2s.class)
    public void act_jigsaw_recv_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_jigsaw_recv_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_jigsaw_recv_c2s(humanObj, msg.getId());
    }

    /**
     * 活动拼图聊天状态C2S消息
     */
    @MsgReceiver(MsgAct.act_jigsaw_chat_status_c2s.class)
    public void act_jigsaw_chat_status_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_jigsaw_chat_status_c2s(humanObj);
    }

    /**
     * 活动拼图搜索角色C2S消息
     * @param content 内容
     */
    @MsgReceiver(MsgAct.act_jigsaw_search_role_c2s.class)
    public void act_jigsaw_search_role_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_jigsaw_search_role_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_jigsaw_search_role_c2s(humanObj, msg.getContent());
    }

    /**
     * 活动宝箱塔信息C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_box_tower_info_c2s.class)
    public void act_box_tower_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_box_tower_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_box_tower_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动宝箱塔开启C2S消息
     * @param act_type 活动类型
     * @param choose 选择
     */
    @MsgReceiver(MsgAct.act_box_tower_open_c2s.class)
    public void act_box_tower_open_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_box_tower_open_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_box_tower_open_c2s(humanObj, msg.getActType(), msg.getChoose());
    }

    /**
     * 活动宝箱塔下一层C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_box_tower_next_c2s.class)
    public void act_box_tower_next_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_box_tower_next_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_box_tower_next_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动宝箱塔自动下一层C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_box_tower_auto_next_c2s.class)
    public void act_box_tower_auto_next_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_box_tower_auto_next_c2s msg = param.getMsg();
//        ActivityManager.inst().on_act_box_tower_auto_next_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动拯救地鼠信息C2S消息
     *
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_tang_mole_info_c2s.class)
    public void act_tang_mole_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_tang_mole_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_tang_mole_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动拯救地鼠选择锤子C2S消息
     * @param act_type 活动类型
     * @param choose_hammer 选择的锤子
     */
    @MsgReceiver(MsgAct.act_tang_mole_choose_hammer_c2s.class)
    public void act_tang_mole_choose_hammer_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_tang_mole_choose_hammer_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_tang_mole_choose_hammer_c2s(humanObj, msg.getActType(), msg.getChooseHammer());
    }

    /**
     * 活动拯救地鼠开始挑战C2S消息
     * @param act_type 活动类型
     * @param chapter_id 章节ID
     */
    @MsgReceiver(MsgAct.act_tang_mole_start_c2s.class)
    public void act_tang_mole_start_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_tang_mole_start_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_tang_mole_start_c2s(humanObj, msg.getActType(), msg.getChapterId());
    }

    /**
     * 活动拯救地鼠结果C2S消息
     * @param act_type 活动类型
     * @param chapter_id 章节ID
     * @param result 结果
     * @param hp 生命值
     * @param kill_list 击杀列表
     * @param ultimate_cnt 终极技能次数
     */
    @MsgReceiver(MsgAct.act_tang_mole_result_c2s.class)
    public void act_tang_mole_result_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_tang_mole_result_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_tang_mole_result_c2s(humanObj, msg.getActType(), msg.getChapterId(), msg.getResult(), msg.getHp(), msg.getKillListList(), msg.getUltimateCnt());
    }

    /**
     * 活动七日试炼信息C2S消息
     * @param type 类型
     */
    @MsgReceiver(MsgAct.act_seven_trial_info_c2s.class)
    public void act_seven_trial_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_seven_trial_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_seven_trial_info_c2s(humanObj, msg.getType());
    }


    /**
     * 活动拯救地鼠领取奖励C2S消息
     * @param type 类型
     */
    @MsgReceiver(MsgAct.act_seven_trial_claim_c2s.class)
    public void act_seven_trial_claim_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_seven_trial_claim_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_seven_trial_claim_c2s(humanObj, msg.getType());
    }

    /**
     * 活动七日试炼愤怒的小鸟信息C2S消息
     */
    @MsgReceiver(MsgAct.act_seven_trial_angry_bird_info_c2s.class)
    public void act_seven_trial_angry_bird_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_seven_trial_angry_bird_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_seven_trial_angry_bird_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动七日试炼愤怒的小鸟结果C2S消息
     * @param result 结果
     * @param chapter_id 章节ID
     * @param step 步数
     */
    @MsgReceiver(MsgAct.act_seven_trial_angry_bird_result_c2s.class)
    public void act_seven_trial_angry_bird_result_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_seven_trial_angry_bird_result_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_seven_trial_angry_bird_result_c2s(humanObj, msg.getActType(), msg.getChapterId(), msg.getResult(), msg.getStep());
    }

    /**
     * 开启十次宝箱塔C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_box_tower_open_fifty_c2s.class)
    public void act_box_tower_open_fifty_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_box_tower_open_fifty_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_box_tower_open_fifty_c2s(humanObj, msg.getActType());
    }

    /**
     * 开启十次宝箱塔C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_box_tower_open_ten_c2s.class)
    public void act_box_tower_open_ten_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_box_tower_open_ten_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_box_tower_open_ten_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动逆战信息C2S消息
     */
    @MsgReceiver(MsgAct.act_reverse_war_info_c2s.class)
    public void act_reverse_war_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_reverse_war_info_c2s(humanObj);
    }

    /**
     * 活动逆战章节通关C2S消息
     * @param chapter 章节
     * @param star 星级
     */
    @MsgReceiver(MsgAct.act_reverse_war_chapter_pass_c2s.class)
    public void act_reverse_war_chapter_pass_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_reverse_war_chapter_pass_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_reverse_war_chapter_pass_c2s(humanObj, msg.getChapter(), msg.getStar());
    }

    /**
     * 活动逆战怪物解锁C2S消息
     * @param monster_id 怪物ID
     */
    @MsgReceiver(MsgAct.act_reverse_war_monster_unlock_c2s.class)
    public void act_reverse_war_monster_unlock_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_reverse_war_monster_unlock_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_reverse_war_monster_unlock_c2s(humanObj, msg.getMonsterId());
    }

    /**
     * 活动逆战怪物装备C2S消息
     * @param monster_list 怪物列表
     */
    @MsgReceiver(MsgAct.act_reverse_war_monster_equip_c2s.class)
    public void act_reverse_war_monster_equip_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_reverse_war_monster_equip_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_reverse_war_monster_equip_c2s(humanObj, msg.getMonsterList());
    }

    /**
     * 活动逆战领取每日奖励C2S消息
     *
     * @param id ID
     */
    @MsgReceiver(MsgAct.act_reverse_war_get_day_reward_c2s.class)
    public void act_reverse_war_get_day_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_reverse_war_get_day_reward_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_reverse_war_get_day_reward_c2s(humanObj, msg.getId());
    }

    /**
     * 活动逆战领取时间奖励C2S消息
     */
    @MsgReceiver(MsgAct.act_reverse_war_get_time_reward_c2s.class)
    public void act_reverse_war_get_time_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_reverse_war_get_time_reward_c2s(humanObj);
    }

    /**
     * 活动打破金蛋信息C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_break_gold_egg_info_c2s.class)
    public void act_break_gold_egg_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_break_gold_egg_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_break_gold_egg_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动打破金蛋砸蛋C2S消息
     * @param act_type 活动类型
     * @param pos 位置
     */
    @MsgReceiver(MsgAct.act_break_gold_egg_break_c2s.class)
    public void act_break_gold_egg_break_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_break_gold_egg_break_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_break_gold_egg_break_c2s(humanObj, msg.getActType(), msg.getPos());
    }

    /**
     * 活动打破金蛋选择奖励C2S消息
     * @param act_type 活动类型
     * @param goods_id 物品ID
     */
    @MsgReceiver(MsgAct.act_break_gold_egg_choose_c2s.class)
    public void act_break_gold_egg_choose_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_break_gold_egg_choose_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_break_gold_egg_choose_c2s(humanObj, msg.getActType(), msg.getGoodsId());
    }

    /**
     * 活动打破金蛋下一轮C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_break_gold_egg_next_c2s.class)
    public void act_break_gold_egg_next_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_break_gold_egg_next_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_break_gold_egg_next_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动小游戏信息C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_mini_game_info_c2s.class)
    public void act_mini_game_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_mini_game_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_mini_game_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动小游戏开始C2S消息
     *
     * @param act_type   活动类型
     * @param chapter_id 章节ID
     */
    @MsgReceiver(MsgAct.act_mini_game_start_c2s.class)
    public void act_mini_game_start_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_mini_game_start_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_mini_game_start_c2s(humanObj, msg.getActType(), msg.getChapterId());
    }

    /**
     * 活动小游戏结果C2S消息
     * @param act_type 活动类型
     * @param chapter_id 章节ID
     * @param result 结果
     */
    @MsgReceiver(MsgAct.act_mini_game_result_c2s.class)
    public void act_mini_game_result_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_mini_game_result_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_mini_game_result_c2s(humanObj, msg.getActType(), msg.getChapterId(), msg.getResult());
    }

    /**
     * 活动圣诞礼物角色信息C2S消息
     * @param content 内容
     */
    @MsgReceiver(MsgAct.act_christmas_gift_role_info_c2s.class)
    public void act_christmas_gift_role_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_christmas_gift_role_info_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_christmas_gift_role_info_c2s(humanObj, msg.getContent());
    }

    /**
     * 活动圣诞礼物礼物列表C2S消息
     * @param act_type 活动类型
     * @param page 页数
     */
    @MsgReceiver(MsgAct.act_christmas_gift_gift_list_c2s.class)
    public void act_christmas_gift_gift_list_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_christmas_gift_gift_list_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_christmas_gift_gift_list_c2s(humanObj, msg.getActType(), msg.getPage());
    }

    /**
     * 活动圣诞礼物发送礼物C2S消息
     * @param act_type 活动类型
     * @param role_id 角色ID
     * @param cfg_id 配置ID
     * @param num 数量
     */
    @MsgReceiver(MsgAct.act_christmas_gift_send_c2s.class)
    public void act_christmas_gift_send_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_christmas_gift_send_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_christmas_gift_send_c2s(humanObj, msg.getActType(), msg.getRoleId(), msg.getCfgId(), msg.getNum());
    }

    /**
     * 活动圣诞礼物打开礼物C2S消息
     * @param act_type 活动类型
     * @param id ID
     */
    @MsgReceiver(MsgAct.act_christmas_gift_open_c2s.class)
    public void act_christmas_gift_open_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_christmas_gift_open_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_christmas_gift_open_c2s(humanObj, msg.getActType(), msg.getId());
    }

    /**
     * 活动圣诞礼物分享C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_christmas_gift_share_c2s.class)
    public void act_christmas_gift_share_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_christmas_gift_share_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_christmas_gift_share_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动金蛋计数奖励C2S消息
     *
     * @param act_type  活动类型
     * @param reward_id 奖励ID
     */
    @MsgReceiver(MsgAct.act_gold_egg_count_reward_c2s.class)
    public void act_gold_egg_count_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_gold_egg_count_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_gold_egg_count_reward_c2s(humanObj, msg.getActType(), msg.getRewardId());
    }

    /**
     * 活动邀请码信息C2S消息
     */
    @MsgReceiver(MsgAct.act_code_invite_info_c2s.class)
    public void act_code_invite_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_code_invite_info_c2s(humanObj);
    }

    /**
     * 活动邀请码绑定C2S消息
     * @param code 邀请码
     */
    @MsgReceiver(MsgAct.act_code_invite_bind_c2s.class)
    public void act_code_invite_bind_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_code_invite_bind_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_code_invite_bind_c2s(humanObj, msg.getCode());
    }

    /**
     * 活动邀请码分享C2S消息
     */
    @MsgReceiver(MsgAct.act_code_invite_share_c2s.class)
    public void act_code_invite_share_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_code_invite_share_c2s(humanObj);
    }

    /**
     * 活动八王宴信息C2S消息
     */
    @MsgReceiver(MsgAct.act_bawang_info_c2s.class)
    public void act_bawang_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_bawang_info_c2s(humanObj);
    }

    /**
     * 活动八王宴领取奖励C2S消息
     * @param id 奖励ID
     */
    @MsgReceiver(MsgAct.act_bawang_get_reward_c2s.class)
    public void act_bawang_get_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_bawang_get_reward_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_bawang_get_reward_c2s(humanObj, msg.getId());
    }

    /**
     * 活动新年团圆C2S消息
     */
    @MsgReceiver(MsgAct.act_newyear_dinner_c2s.class)
    public void act_newyear_dinner_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_newyear_dinner_c2s(humanObj);
    }

    /**
     * 活动新年团圆制作C2S消息
     * @param config_id 配置ID
     * @param food_list 食物列表
     */
    @MsgReceiver(MsgAct.act_newyear_dinner_make_c2s.class)
    public void act_newyear_dinner_make_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_newyear_dinner_make_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_newyear_dinner_make_c2s(humanObj, msg.getConfigId(), msg.getFoodListList());
    }

    /**
     * 活动新年团圆享用C2S消息
     */
    @MsgReceiver(MsgAct.act_newyear_dinner_enjoy_c2s.class)
    public void act_newyear_dinner_enjoy_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_newyear_dinner_enjoy_c2s(humanObj);
    }

    /**
     * 活动集福信息C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_collect_blessing_c2s.class)
    public void act_collect_blessing_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_collect_blessing_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_collect_blessing_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动集福请求C2S消息
     * @param act_type 活动类型
     * @param card_id 卡片ID
     * @param type 类型
     * @param role_id 角色ID
     */
    @MsgReceiver(MsgAct.act_collect_blessing_ask_c2s.class)
    public void act_collect_blessing_ask_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_collect_blessing_ask_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_collect_blessing_ask_c2s(humanObj, msg.getActType(), msg.getCardId(), msg.getType(),
//                msg.getRoleId());
    }

    /**
     * 活动集福发送C2S消息
     * @param act_type 活动类型
     * @param card_id 卡片ID
     * @param type 类型
     * @param role_id 角色ID
     * @param id ID
     */
    @MsgReceiver(MsgAct.act_collect_blessing_send_c2s.class)
    public void act_collect_blessing_send_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_collect_blessing_send_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_collect_blessing_send_c2s(humanObj, msg.getActType(), msg.getCardId(), msg.getType(),
//                msg.getRoleId(), msg.getId());
    }

    /**
     * 活动集福接收C2S消息
     * @param act_type 活动类型
     * @param id ID
     */
    @MsgReceiver(MsgAct.act_collect_blessing_recv_c2s.class)
    public void act_collect_blessing_recv_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_collect_blessing_recv_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_collect_blessing_recv_c2s(humanObj, msg.getActType(), msg.getId());
    }

    /**
     * 活动集福展示C2S消息
     *
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_collect_five_blessing_show_c2s.class)
    public void act_collect_five_blessing_show_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_collect_five_blessing_show_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_collect_five_blessing_show_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动集福幸运C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_collect_five_blessing_luck_c2s.class)
    public void act_collect_five_blessing_luck_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_collect_five_blessing_luck_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_collect_five_blessing_luck_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动情人节角色信息C2S消息
     */
    @MsgReceiver(MsgAct.act_valentine_role_info_c2s.class)
    public void act_valentine_role_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_valentine_role_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_valentine_role_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动幸运猫信息C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_lucky_cat_info_c2s.class)
    public void act_lucky_cat_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_lucky_cat_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_lucky_cat_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动幸运猫C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_lucky_cat_c2s.class)
    public void act_lucky_cat_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_lucky_cat_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_lucky_cat_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动幸运猫报告C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_lucky_cat_report_c2s.class)
    public void act_lucky_cat_report_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_lucky_cat_report_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_lucky_cat_report_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动周卡信息C2S消息
     */
    @MsgReceiver(MsgAct.act_week_card_info_c2s.class)
    public void act_week_card_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        ActivityManager.inst().on_act_week_card_info_c2s(humanObj);
    }

    /**
     * 活动周卡奖励C2S消息
     * @param reward_id 奖励ID
     */
    @MsgReceiver(MsgAct.act_week_card_reward_c2s.class)
    public void act_week_card_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_week_card_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_week_card_reward_c2s(humanObj, msg.getRewardId());
    }

    /**
     * 活动支付返利信息C2S消息
     */
    @MsgReceiver(MsgAct.act_pay_rebate_info_c2s.class)
    public void act_pay_rebate_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_pay_rebate_info_c2s(humanObj);
    }

    /**
     * 活动领取支付返利奖励C2S消息
     * @param reward_id 奖励ID
     */
    @MsgReceiver(MsgAct.act_pay_rebate_get_reward_c2s.class)
    public void act_pay_rebate_get_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_pay_rebate_get_reward_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_pay_rebate_get_reward_c2s(humanObj, msg.getRewardId());
    }

    /**
     * 活动青蛙鸟信息C2S消息
     */
    @MsgReceiver(MsgAct.act_frog_bird_info_c2s.class)
    public void act_frog_bird_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_frog_bird_info_c2s(humanObj);
    }

    /**
     * 活动青蛙鸟开始C2S消息
     */
    @MsgReceiver(MsgAct.act_frog_bird_start_c2s.class)
    public void act_frog_bird_start_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_frog_bird_start_c2s(humanObj);
    }

    /**
     * 活动青蛙鸟结果C2S消息
     * @param score 分数
     */
    @MsgReceiver(MsgAct.act_frog_bird_result_c2s.class)
    public void act_frog_bird_result_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_frog_bird_result_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_frog_bird_result_c2s(humanObj, msg.getScore());
    }

    /**
     * 活动服务器拼图信息C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_server_puzzle_info_c2s.class)
    public void act_server_puzzle_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_server_puzzle_info_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_server_puzzle_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动服务器拼图使用道具C2S消息
     * @param num 数量
     */
    @MsgReceiver(MsgAct.act_server_puzzle_use_goods_c2s.class)
    public void act_server_puzzle_use_goods_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_server_puzzle_use_goods_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_server_puzzle_use_goods_c2s(humanObj, msg.getNum());
    }

    /**
     * 活动服务器拼图领取奖励C2S消息
     * @param progress 进度
     */
    @MsgReceiver(MsgAct.act_server_puzzle_claim_reward_c2s.class)
    public void act_server_puzzle_claim_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_server_puzzle_claim_reward_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_server_puzzle_claim_reward_c2s(humanObj, msg.getProgress());
    }

    /**
     * 活动青蛙猫信息C2S消息
     * @param act_type 活动类型
     */
    @MsgReceiver(MsgAct.act_frog_cat_info_c2s.class)
    public void act_frog_cat_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_frog_cat_info_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_frog_cat_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动青蛙猫购买C2S消息
     */
    @MsgReceiver(MsgAct.act_frog_cat_buy_c2s.class)
    public void act_frog_cat_buy_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_frog_cat_buy_c2s(humanObj);
    }

    /**
     * 活动青蛙猫领取奖励C2S消息
     */
    @MsgReceiver(MsgAct.act_frog_cat_claim_c2s.class)
    public void act_frog_cat_claim_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_frog_cat_claim_c2s(humanObj);
    }

    /**
     * 活动偶像物语C2S消息
     */
    @MsgReceiver(MsgAct.act_idol_story_c2s.class)
    public void act_idol_story_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        //ActivityManager.inst().on_act_idol_story_c2s(humanObj);
    }


    /**
     * 活动偶像更换偶像C2S消息
     * @param idol_id 偶像ID
     */
    @MsgReceiver(MsgAct.act_idol_change_idol_c2s.class)
    public void act_idol_change_idol_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_idol_change_idol_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_idol_change_idol_c2s(humanObj, msg.getIdolId());
    }

    /**
     * 活动偶像故事领取奖励C2S消息
     * @param story_id 故事ID
     */
    @MsgReceiver(MsgAct.act_idol_story_reward_c2s.class)
    public void act_idol_story_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_idol_story_reward_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_idol_story_reward_c2s(humanObj, msg.getStoryId());
    }

    /**
     * 活动偶像抽奖C2S消息
     * @param call_id 抽奖ID
     */
    @MsgReceiver(MsgAct.act_idol_pick_c2s.class)
    public void act_idol_pick_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_idol_pick_c2s msg = param.getMsg();
        //ActivityManager.inst().on_act_idol_pick_c2s(humanObj, msg.getCallId());
    }

    @MsgReceiver(MsgAct.act_sns_share_reward_c2s.class)
    public void act_sns_share_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        ShareManager.inst()._msg_act_sns_share_reward_c2s(humanObj);
    }

    @MsgReceiver(MsgAct.act_broadcast_c2s.class)
    public void act_broadcast_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_broadcast_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_broadcast_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct2.act_share_game_info_c2s.class)
    public void act_share_game_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_share_game_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_share_game_info_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct2.act_share_game_result_c2s.class)
    public void act_share_game_result_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_share_game_result_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_share_game_result_c2s(humanObj, msg.getActType(), msg.getScore());
    }

    /**
     * 黄金塔活动信息
     */
    @MsgReceiver(MsgAct2.act_golden_tower_info_c2s.class)
    public void act_golden_tower_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_golden_tower_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_golden_tower_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 黄金塔抽奖
     * act_type 活动类型
     * draw_type 抽奖类型
     */
    @MsgReceiver(MsgAct2.act_golden_tower_draw_c2s.class)
    public void act_golden_tower_draw_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_golden_tower_draw_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_golden_tower_draw_c2s(humanObj, msg.getActType(), msg.getDrawType());
    }

    /**
     * 黄金塔选择奖励
     * act_type 活动类型
     * reward_id 奖励ID
     * replace_id 替换ID
     */
    @MsgReceiver(MsgAct2.act_golden_tower_choose_c2s.class)
    public void act_golden_tower_choose_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_golden_tower_choose_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_golden_tower_choose_c2s(humanObj, msg.getActType(), msg.getRewardId(), msg.getReplaceId());
    }

    /**
     * 黄金塔累计奖励
     * act_type 活动类型
     * reward_id 奖励ID
     */
    @MsgReceiver(MsgAct2.act_golden_tower_count_reward_c2s.class)
    public void act_golden_tower_count_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_golden_tower_count_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_golden_tower_count_reward_c2s(humanObj, msg.getActType(), msg.getRewardId());
    }

    @MsgReceiver(MsgAct2.act_cross_boss_info_c2s.class)
    public void act_cross_boss_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_cross_boss_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_cross_boss_info_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct2.act_cross_boss_throw_c2s.class)
    public void act_cross_boss_throw_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_cross_boss_throw_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_cross_boss_throw_c2s(humanObj, msg.getActType(), msg.getCnt());
    }

    @MsgReceiver(MsgAct2.act_cross_boss_claim_c2s.class)
    public void act_cross_boss_claim_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_cross_boss_claim_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_cross_boss_claim_c2s(humanObj, msg.getActType(), msg.getCfgId());
    }

    @MsgReceiver(MsgAct2.act_cross_boss_buy_c2s.class)
    public void act_cross_boss_buy_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_cross_boss_buy_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_cross_boss_buy_c2s(humanObj, msg.getActType(), msg.getCnt());
    }

    @MsgReceiver(MsgAct2.act_cross_boss_claim_box_info_c2s.class)
    public void act_cross_boss_claim_box_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_cross_boss_claim_box_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_cross_boss_claim_box_info_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct2.act_cross_boss_claim_box_c2s.class)
    public void act_cross_boss_claim_box_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_cross_boss_claim_box_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_cross_boss_claim_box_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct.act_day_pay_get_reward_c2s.class)
    public void act_day_pay_get_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_day_pay_get_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_day_pay_reward(humanObj, msg.getActType(), msg.getRewardIndex());
    }

    @MsgReceiver(MsgAct.act_day_pay_choose_reward_c2s.class)
    public void act_day_pay_choose_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_day_pay_choose_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_day_pay_choose_reward(humanObj, msg.getActType(), msg.getChooseIndex());
    }

    @MsgReceiver(MsgAct.act_legion_info_c2s.class)
    public void act_legion_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_legion_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_legion_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 公会充值活动信息请求
     */
    @MsgReceiver(MsgAct2.act_guild_pay_info_c2s.class)
    public void act_guild_pay_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_guild_pay_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_guild_pay_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 公会充值活动奖励领取请求
     */
    @MsgReceiver(MsgAct2.act_guild_pay_get_reward_c2s.class)
    public void act_guild_pay_get_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_guild_pay_get_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_guild_pay_get_reward_c2s(humanObj, msg.getActType(), msg.getRewardId());
    }

    @MsgReceiver(MsgAct.act_legion_buff_active_c2s.class)
    public void act_legion_buff_active_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_legion_buff_active_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_legion_buff_active_c2s(humanObj, msg.getActType(), msg.getBuff());
    }

    @MsgReceiver(MsgAct.act_legion_merge_c2s.class)
    public void act_legion_merge_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_legion_merge_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_legion_merge_c2s(humanObj, msg.getActType(), msg.getMonstersList(), msg.getGridsList(), msg.getStep());
    }

    @MsgReceiver(MsgAct.act_legion_invasion_c2s.class)
    public void act_legion_invasion_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_legion_invasion_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_legion_invasion_c2s(humanObj, msg.getActType(), msg.getGear());
    }

    @MsgReceiver(MsgAct.act_legion_collection_reward_c2s.class)
    public void act_legion_collection_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_legion_collection_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_legion_collection_reward_c2s(humanObj, msg.getActType(), msg.getMonsterId());
    }

    @MsgReceiver(MsgAct.act_legion_energy_c2s.class)
    public void act_legion_energy_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_legion_energy_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_legion_energy_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct.act_legion_buff_use_c2s.class)
    public void act_legion_buff_use_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_legion_buff_use_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_legion_buff_use_c2s(humanObj, msg.getActType(), msg.getBuff(), msg.getNum());
    }

    /**
     * 活动大富翁信息C2S消息
     */
    @MsgReceiver(MsgAct2.act_monopoly_info_c2s.class)
    public void act_monopoly_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_monopoly_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_monopoly_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动大富翁掷骰子C2S消息
     */
    @MsgReceiver(MsgAct2.act_monopoly_dice_c2s.class)
    public void act_monopoly_dice_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_monopoly_dice_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_monopoly_dice_c2s(humanObj, msg.getActType(), msg.getOpType(), msg.getDiceNum());
    }

    /**
     * 活动大富翁双倍C2S消息
     */
    @MsgReceiver(MsgAct2.act_monopoly_double_c2s.class)
    public void act_monopoly_double_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_monopoly_double_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_monopoly_double_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动大富翁圈数奖励C2S消息
     */
    @MsgReceiver(MsgAct2.act_monopoly_circle_reward_c2s.class)
    public void act_monopoly_circle_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_monopoly_circle_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_monopoly_circle_reward_c2s(humanObj, msg.getActType(), msg.getRewardId());
    }

    /**
     * 获取卡皮巴拉信息C2S消息
     */
    @MsgReceiver(MsgAct2.act_slime_info_c2s.class)
    public void act_slime_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_slime_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_slime_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 进入关卡C2S消息
     */
    @MsgReceiver(MsgAct2.act_slime_enter_stage_c2s.class)
    public void act_slime_enter_stage_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_slime_enter_stage_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_slime_enter_stage_c2s(humanObj, msg.getActType(), msg.getStageId()
        );
    }

    /**
     * 结束关卡C2S消息
     */
    @MsgReceiver(MsgAct2.act_slime_finish_stage_c2s.class)
    public void act_slime_finish_stage_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_slime_finish_stage_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_slime_finish_stage_c2s(humanObj, msg.getActType(), msg.getStageId(), msg.getResult());
    }

    /**
     * 关卡进度上报C2S消息
     */
    @MsgReceiver(MsgAct2.act_slime_stage_progress_c2s.class)
    public void act_slime_stage_progress_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_slime_stage_progress_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_slime_stage_progress_c2s(humanObj, msg.getActType(), msg.getEventInfo());
    }

    /**
     * 天赋升级C2S消息
     */
    @MsgReceiver(MsgAct2.act_slime_talent_upgrade_c2s.class)
    public void act_slime_talent_upgrade_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_slime_talent_upgrade_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_slime_talent_upgrade_c2s(humanObj, msg.getActType(), msg.getAttrType()
        );
    }

    /**
     * 大吉转盘抽奖C2S消息
     */
    @MsgReceiver(MsgAct2.act_slime_wheel_draw_c2s.class)
    public void act_slime_wheel_draw_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_slime_wheel_draw_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_slime_wheel_draw_c2s(humanObj, msg.getActType());
    }

    /**
     * 史莱姆扫荡关卡C2S消息
     */
    @MsgReceiver(MsgAct2.act_slime_sweep_c2s.class)
    public void act_slime_sweep_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_slime_sweep_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_slime_sweep_c2s(humanObj, msg.getActType(), msg.getStageId(), msg.getCount(), msg.getItemList(), msg.getBigLuckNum());
    }

    /**
     * 史莱姆融合数据C2S消息
     */
    @MsgReceiver(MsgAct2.act_slime_mix_info_c2s.class)
    public void act_slime_mix_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_slime_mix_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_slime_mix_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 史莱姆融合合成C2S消息
     */
    @MsgReceiver(MsgAct2.act_slime_mix_c2s.class)
    public void act_slime_mix_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_slime_mix_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_slime_mix_c2s(humanObj, msg.getActType(), msg.getMixType());
    }

    /**
     * 体力值刷新C2S消息
     */
    @MsgReceiver(MsgAct2.act_slime_refresh_c2s.class)
    public void act_slime_refresh_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_slime_refresh_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_slime_refresh_c2s(humanObj,msg.getActType());
    }

    /**
     * 技能洗炼C2S消息
     */
    @MsgReceiver(MsgAct2.act_slime_skill_refine_c2s.class)
    public void act_slime_skill_refine_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_slime_skill_refine_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_slime_skill_refine_c2s(humanObj, msg.getActType(), msg.getPos());
    }

    /**
     * 技能选择C2S消息
     */
    @MsgReceiver(MsgAct2.act_slime_skill_choose_c2s.class)
    public void act_slime_skill_choose_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_slime_skill_choose_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_slime_skill_choose_c2s(humanObj, msg.getActType(), msg.getPos(), msg.getIsReplace());
    }

    /**
     * 大闹魔王城活动信息
     */
    @MsgReceiver(MsgAct2.act_castle_info_c2s.class)
    public void act_castle_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_castle_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_castle_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 大闹魔王城练丹请求
     */
    @MsgReceiver(MsgAct2.act_castle_gamble_c2s.class)
    public void act_castle_refine_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_castle_gamble_c2s msg = param.getMsg();
        ActivityManager.inst().act_castle_gamble_c2s(humanObj, msg.getActType(), msg.getIsTen());
    }

    /**
     * 大闹魔王城领取元素奖励
     */
    @MsgReceiver(MsgAct2.act_castle_element_reward_c2s.class)
    public void act_castle_element_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_castle_element_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_castle_element_reward_c2s(humanObj, msg.getActType(), msg.getElementType(), msg.getPos());
    }
    /**
     * 体力值刷新
     */
    @MsgReceiver(MsgAct2.act_stamina_refresh_c2s.class)
    public void act_stamina_refresh_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_stamina_refresh_c2s msg = param.getMsg();
        ActivityManager.inst().act_stamina_refresh_c2s(humanObj, msg.getActType());
    }

    /**
     * 大闹魔王城移动
     */
    @MsgReceiver(MsgAct2.act_castle_move_c2s.class)
    public void act_castle_move_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_castle_move_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_castle_move_c2s(humanObj, msg.getActType(), msg.getLayer(), msg.getPos());
    }

    /**
     * 大闹魔王城竞赛操做
     */
    @MsgReceiver(MsgAct2.act_castle_race_op_c2s.class)
    public void act_castle_race_op_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_castle_race_op_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_castle_race_op_c2s(humanObj, msg.getActType(), msg.getOp());
    }

    /**
     * 大闹魔王城竞赛结果
     */
    @MsgReceiver(MsgAct2.act_castle_race_result_c2s.class)
    public void act_castle_race_result_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_castle_race_result_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_castle_race_result_c2s(humanObj, msg.getActType(), msg.getIsWin());
    }

    /**
     * 转剑打造C2S消息
     */
    @MsgReceiver(MsgAct2.act_dungeon_craft_info_c2s.class)
    public void act_dungeon_craft_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_craft_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_dungeon_craft_info_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct2.act_dungeon_craft_c2s.class)
    public void act_dungeon_craft_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_craft_c2s msg = param.getMsg();
        if (!humanObj.isMsgIdCD(MsgIds.act_dungeon_craft_c2s, ParamKey.msgCd1)) {
            return;
        }
        ActivityManager.inst().on_act_dungeon_craft_c2s(humanObj, msg.getActType(), msg.getCraftType(), msg.getNum());
    }

    @MsgReceiver(MsgAct.act_refresh_gift_info_c2s.class)
    public void act_refresh_gift_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_refresh_gift_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_refresh_gift_info_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct.act_refresh_gift_refresh_c2s.class)
    public void act_refresh_gift_refresh_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_refresh_gift_refresh_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_refresh_gift_refresh_c2s(humanObj, msg.getActType(), msg.getPaySn());
    }

    @MsgReceiver(MsgAct.act_refresh_gift_reward_c2s.class)
    public void act_refresh_gift_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_refresh_gift_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_refresh_gift_reward_c2s(humanObj, msg.getActType(), msg.getPaySn(), msg.getRewardIndex());
    }

    /**
     * 领取活动日历每日免费礼包
     * @param param
     */
    @MsgReceiver(MsgAct.act_calendar_reward_c2s.class)
    public void _msg_act_calendar_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        ActivityManager.inst()._msg_act_calendar_reward_c2s(humanObj);
    }

    /**
     * 转生魔剑信息总览
     */
    @MsgReceiver(MsgAct2.act_dungeon_info_c2s.class)
    public void _msg_act_dungeon_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_info_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 转生魔剑培养
     */
    @MsgReceiver(MsgAct2.act_dungeon_cultivate_c2s.class)
    public void _msg_act_dungeon_cultivate_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_cultivate_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_cultivate_c2s(humanObj, msg.getActType(), msg.getAttrType());
    }

    /**
     * 转生魔剑锻造
     */
    @MsgReceiver(MsgAct2.act_dungeon_forge_c2s.class)
    public void _msg_act_dungeon_forge_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_forge_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_forge_c2s(humanObj, msg.getActType(), msg.getType());
    }

    /**
     * 转生魔剑装备升级
     */
    @MsgReceiver(MsgAct2.act_dungeon_equip_upgrade_c2s.class)
    public void _msg_act_dungeon_equip_upgrade_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_equip_upgrade_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_equip_upgrade_c2s(humanObj, msg.getActType(), msg.getEquipId(), msg.getCostCrystal());
    }

    /**
     * 转生魔剑消耗锻造进度领取矿石
     */
    @MsgReceiver(MsgAct2.act_dungeon_receive_ore_c2s.class)
    public void _msg_act_dungeon_receive_ore_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_receive_ore_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_receive_ore_c2s(humanObj, msg.getActType());
    }

    /**
     * 转生魔剑烹饪
     */
    @MsgReceiver(MsgAct2.act_dungeon_cook_c2s.class)
    public void _msg_act_dungeon_cook_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_cook_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_cook_c2s(humanObj, msg.getActType(), msg.getFoodId(), msg.getFoodNum());
    }

    /**
     * 转生魔剑扫荡
     */
    @MsgReceiver(MsgAct2.act_dungeon_sweep_c2s.class)
    public void _msg_act_dungeon_sweep_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_sweep_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_sweep_c2s(humanObj, msg.getActType(), msg.getChapterId(), msg.getSweepNum());
    }

    /**
     * 转生魔剑进入场景
     */
    @MsgReceiver(MsgAct2.act_dungeon_enter_scene_c2s.class)
    public void _msg_act_dungeon_enter_scene_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_enter_scene_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_enter_scene_c2s(humanObj, msg.getActType(), msg.getChapterId(), msg.getCarryInfoList());
    }

    /**
     * 转生魔剑探索
     */
    @MsgReceiver(MsgAct2.act_dungeon_explore_c2s.class)
    public void _msg_act_dungeon_explore_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_explore_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_explore_c2s(humanObj, msg.getActType(), msg.getChapterId(), msg.getTargetPos(), msg.getSourceCavePos());
    }

    /**
     * 转生魔剑触发事件
     */
    @MsgReceiver(MsgAct2.act_dungeon_trigger_event_c2s.class)
    public void _msg_act_dungeon_trigger_event_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_trigger_event_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_trigger_event_c2s(humanObj, msg.getActType(), msg.getChapterId(),
                msg.getTargetPos(), msg.getParam(), msg.getSourceCavePos());
    }

    /**
     * 转生魔剑战斗
     */
    @MsgReceiver(MsgAct2.act_dungeon_battle_c2s.class)
    public void _msg_act_dungeon_battle_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_battle_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_battle_c2s(humanObj, msg.getActType(), msg.getChapterId(), msg.getTargetPos(), msg.getSourceCavePos());
    }

    /**
     * 转生魔剑使用物品
     */
    @MsgReceiver(MsgAct2.act_dungeon_use_item_c2s.class)
    public void _msg_act_dungeon_use_item_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_use_item_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_use_item_c2s(humanObj, msg.getActType(), msg.getChapterId(), msg.getItemIndex(),
                msg.getTargetPos(), msg.getSourceCavePos());
    }

    /**
     * 转生魔剑吞噬魔石增益信息
     */
    @MsgReceiver(MsgAct2.act_dungeon_devour_stone_info_c2s.class)
    public void _msg_act_dungeon_devour_stone_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_devour_stone_info_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_devour_stone_info_c2s(humanObj, msg.getActType(), msg.getChapterId());
    }

    /**
     * 转生魔剑吞噬魔石操作
     */
    @MsgReceiver(MsgAct2.act_dungeon_devour_stone_op_c2s.class)
    public void _msg_act_dungeon_devour_stone_op_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_devour_stone_op_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_devour_stone_op_c2s(humanObj, msg.getActType(), msg.getChapterId(), msg.getType(), msg.getChooseIndex());
    }

    /**
     * 转生魔剑主动结算
     */
    @MsgReceiver(MsgAct2.act_dungeon_settle_c2s.class)
    public void _msg_act_dungeon_settle_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_dungeon_settle_c2s msg = param.getMsg();
        ActivityManager.inst()._msg_act_dungeon_settle_c2s(humanObj, msg.getActType(), msg.getChapterId());
    }
	
	@MsgReceiver(MsgAct.act_camp_info_c2s.class)
    public void act_camp_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_camp_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_camp_info_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct.act_camp_join_c2s.class)
    public void act_camp_join_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_camp_join_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_camp_join_c2s(humanObj, msg.getActType(), msg.getCamp());
    }

    // region 合成大西瓜活动消息处理
    /**
     * 合成大西瓜活动信息
     */
    @MsgReceiver(MsgAct2.act_fruit_merge_info_c2s.class)
    public void act_fruit_merge_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_fruit_merge_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_fruit_merge_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 合成大西瓜结束游戏
     */
    @MsgReceiver(MsgAct2.act_fruit_merge_end_c2s.class)
    public void act_fruit_merge_end_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_fruit_merge_end_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_fruit_merge_end_c2s(humanObj, msg.getActType(), msg.getCurrScore());
    }

    /**
     * 合成大西瓜消耗体力
     */
    @MsgReceiver(MsgAct2.act_fruit_merge_stamina_cost_c2s.class)
    public void act_fruit_merge_stamina_cost_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_fruit_merge_stamina_cost_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_fruit_merge_stamina_cost_c2s(humanObj, msg.getActType(), msg.getTotalCost());
    }

    /**
     * 合成大西瓜使用道具
     */
    @MsgReceiver(MsgAct2.act_fruit_merge_use_item_c2s.class)
    public void act_fruit_merge_use_item_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_fruit_merge_use_item_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_fruit_merge_use_item_c2s(humanObj, msg.getActType(), msg.getItemSn());
    }

    /**
     * 合成大西瓜上报状态
     */
    @MsgReceiver(MsgAct2.act_fruit_merge_state_c2s.class)
    public void act_fruit_merge_state_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_fruit_merge_state_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_fruit_merge_state_c2s(humanObj, msg.getActType(), msg.getMergeFruitList(), msg.getState());
    }

    /**
     * 合成大西瓜扫荡
     */
    @MsgReceiver(MsgAct2.act_fruit_sweep_c2s.class)
    public void act_fruit_sweep_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_fruit_sweep_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_fruit_sweep_c2s(humanObj, msg.getActType());
    }
    // endregion

    @MsgReceiver(MsgAct.act_camp_support_c2s.class)
    public void act_camp_support_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_camp_support_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_camp_support_c2s(humanObj, msg.getActType(), msg.getCnt());
    }

    @MsgReceiver(MsgAct.act_camp_reward_c2s.class)
    public void act_camp_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_camp_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_camp_reward_c2s(humanObj, msg.getActType(), msg.getRewardIndex());
    }

    @MsgReceiver(MsgAct.act_skin_try_info_c2s.class)
    public void act_skin_try_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_skin_try_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_skin_try_info_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct.act_skin_try_use_c2s.class)
    public void act_skin_try_use_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_skin_try_use_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_skin_try_use_c2s(humanObj, msg.getActType(), msg.getUse());
    }

    @MsgReceiver(MsgAct2.act_card_eliminate_info_c2s.class)
    public void act_card_eliminate_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_card_eliminate_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_card_eliminate_info_c2s(humanObj, msg.getActType());
    }
    @MsgReceiver(MsgAct2.act_card_eliminate_start_c2s.class)
    public void act_card_eliminate_start_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_card_eliminate_start_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_card_eliminate_start_c2s(humanObj, msg.getActType());
    }
    @MsgReceiver(MsgAct2.act_card_eliminate_flip_c2s.class)
    public void act_card_eliminate_flip_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_card_eliminate_flip_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_card_eliminate_flip_c2s(humanObj, msg.getActType(), msg.getPos());
    }

    @MsgReceiver(MsgAct2.act_card_eliminate_round_complete_c2s.class)
    public void act_card_eliminate_round_complete_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_card_eliminate_round_complete_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_card_eliminate_round_complete_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct2.act_card_eliminate_select_buff_c2s.class)
    public void act_card_eliminate_select_buff_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_card_eliminate_select_buff_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_card_eliminate_select_buff_c2s(humanObj, msg.getActType(), msg.getBuffId());
    }
    @MsgReceiver(MsgAct2.act_card_eliminate_game_over_c2s.class)
    public void act_card_eliminate_game_over_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_card_eliminate_game_over_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_card_eliminate_game_over_c2s(humanObj, msg.getActType());
    }
    @MsgReceiver(MsgAct2.act_card_buff_refresh_c2s.class)
    public void act_card_buff_refresh_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_card_buff_refresh_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_card_buff_refresh_c2s(humanObj, msg.getActType());
    }

    // 无职研究活动消息处理
    @MsgReceiver(MsgAct2.act_wuzhi_research_info_c2s.class)
    public void act_wuzhi_research_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_wuzhi_research_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_wuzhi_research_info_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct2.act_wuzhi_research_dig_c2s.class)
    public void act_wuzhi_research_dig_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_wuzhi_research_dig_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_wuzhi_research_dig_c2s(humanObj, msg.getActType(), msg.getTileId());
    }

    @MsgReceiver(MsgAct2.act_wuzhi_research_appraise_c2s.class)
    public void act_wuzhi_research_appraise_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_wuzhi_research_appraise_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_wuzhi_research_appraise_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct2.act_wuzhi_research_draw_grand_reward_c2s.class)
    public void act_wuzhi_research_draw_grand_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_wuzhi_research_draw_grand_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_wuzhi_research_draw_grand_reward_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct2.act_wuzhi_research_claim_reward_c2s.class)
    public void act_wuzhi_research_claim_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_wuzhi_research_claim_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_wuzhi_research_claim_reward_c2s(humanObj, msg.getActType(), msg.getId());
    }

    @MsgReceiver(MsgAct2.act_wuzhi_research_set_options_c2s.class)
    public void act_wuzhi_research_set_options_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_wuzhi_research_set_options_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_wuzhi_research_set_options_c2s(humanObj, msg.getActType(), msg.getMode(), msg.getAutoAppraisal());
    }

    @MsgReceiver(MsgAct2.act_wuzhi_research_one_click_c2s.class)
    public void act_wuzhi_research_one_click_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_wuzhi_research_one_click_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_wuzhi_research_one_click_c2s(humanObj, msg.getActType(), msg.getMode(), msg.getAutoAppraisal());
    }

    @MsgReceiver(MsgAct.act_group_gift_c2s.class)
    public void act_group_gift_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_group_gift_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_group_gift_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct.act_group_gift_opt_c2s.class)
    public void act_group_gift_opt_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_group_gift_opt_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_group_gift_opt_c2s(humanObj, msg.getActType(), msg.getOpt(), msg.getSn(), msg.getId());
    }

    @MsgReceiver(MsgAct.act_group_gift_reward_c2s.class)
    public void act_group_gift_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_group_gift_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_group_gift_reward_c2s(humanObj, msg.getActType());
    }
    /**
     * 活动双倍抽奖信息C2S消息
     */
    @MsgReceiver(MsgAct.act_double_draw_info_c2s.class)
    public void act_double_draw_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_double_draw_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_double_draw_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 活动双倍抽奖C2S消息
     */
    @MsgReceiver(MsgAct.act_double_draw_draw_c2s.class)
    public void act_double_draw_draw_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_double_draw_draw_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_double_draw_draw_c2s(humanObj, msg.getActType(), msg.getType());
    }

    /**
     * 活动双倍抽奖次数奖励C2S消息
     */
    @MsgReceiver(MsgAct.act_double_draw_count_reward_c2s.class)
    public void act_double_draw_count_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_double_draw_count_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_double_draw_count_reward_c2s(humanObj, msg.getActType(), msg.getCfgId());
    }

    /**
     * 活动双倍抽奖选择C2S消息
     */
    @MsgReceiver(MsgAct.act_double_draw_choose_c2s.class)
    public void act_double_draw_choose_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_double_draw_choose_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_double_draw_choose_c2s(humanObj, msg.getActType(), msg.getCfgId(), msg.getChooseId());
    }

    /**
     * 全服提交道具活动信息C2S消息
     */
    @MsgReceiver(MsgAct2.act_cohesion_info_c2s.class)
    public void act_cohesion_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_cohesion_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_cohesion_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 全服提交道具活动领取奖励C2S消息
     */
    @MsgReceiver(MsgAct2.act_cohesion_get_reward_c2s.class)
    public void act_cohesion_get_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_cohesion_get_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_cohesion_get_reward_c2s(humanObj, msg.getActType(), msg.getRewardId());
    }

    @MsgReceiver(MsgAct.act_accumulate_score_info_c2s.class)
    public void act_accumulate_score_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_accumulate_score_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_accumulate_score_info_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct.act_airdrop_gift_info_c2s.class)
    public void act_airdrop_gift_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_airdrop_gift_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_airdrop_gift_info_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct.act_airdrop_gift_draw_c2s.class)
    public void act_airdrop_gift_draw_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_airdrop_gift_draw_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_airdrop_gift_draw_c2s(humanObj, msg.getActType(), msg.getDrawTye());
    }

    @MsgReceiver(MsgAct.act_airdrop_gift_open_c2s.class)
    public void act_airdrop_gift_open_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_airdrop_gift_open_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_airdrop_gift_open_c2s(humanObj, msg.getActType(), msg.getPos());
    }

    @MsgReceiver(MsgAct.act_airdrop_gift_del_c2s.class)
    public void act_airdrop_gift_del_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_airdrop_gift_del_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_airdrop_gift_del_c2s(humanObj, msg.getActType(), msg.getPos());
    }

    @MsgReceiver(MsgAct.act_info_c2s.class)
    public void act_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_info_c2s(humanObj, msg.getActType());
    }

    @MsgReceiver(MsgAct.act_black_market_buy_c2s.class)
    public void act_black_market_buy_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct.act_black_market_buy_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_black_market_buy_c2s(humanObj, msg.getActType(), msg.getSn(), msg.getNum());
    }

    /**
     * 无职联动-好感活动信息请求
     */
    @MsgReceiver(MsgAct2.act_wuzhi_love_info_c2s.class)
    public void act_wuzhi_love_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_wuzhi_love_info_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_wuzhi_love_info_c2s(humanObj, msg.getActType());
    }

    /**
     * 无职联动-选择角色
     */
    @MsgReceiver(MsgAct2.act_wuzhi_love_select_char_c2s.class)
    public void act_wuzhi_love_select_char_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_wuzhi_love_select_char_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_wuzhi_love_select_char_c2s(humanObj, msg.getActType(), msg.getCharId());
    }

    /**
     * 无职联动-合成手办
     */
    @MsgReceiver(MsgAct2.act_wuzhi_love_compose_figure_c2s.class)
    public void act_wuzhi_love_compose_figure_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_wuzhi_love_compose_figure_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_wuzhi_love_compose_figure_c2s(humanObj, msg.getActType());
    }

    /**
     * 无职联动-赠送礼物
     */
    @MsgReceiver(MsgAct2.act_wuzhi_love_give_gift_c2s.class)
    public void act_wuzhi_love_give_gift_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_wuzhi_love_give_gift_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_wuzhi_love_give_gift_c2s(humanObj, msg.getActType(), msg.getCharId(), msg.getItemSnNumList());
    }

    /**
     * 无职联动-领取好感等级奖励
     */
    @MsgReceiver(MsgAct2.act_wuzhi_love_claim_level_reward_c2s.class)
    public void act_wuzhi_love_claim_level_reward_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgAct2.act_wuzhi_love_claim_level_reward_c2s msg = param.getMsg();
        ActivityManager.inst().on_act_wuzhi_love_claim_level_reward_c2s(humanObj, msg.getActType(), msg.getCharId(), msg.getLevel());
    }

    // 每日任务奖励领取现在由通用任务系统处理
}
