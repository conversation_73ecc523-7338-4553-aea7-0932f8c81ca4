package org.gof.demo.worldsrv.activity.calculator;

import io.vertx.core.json.JsonArray;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityManager;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlBoxTowerData;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ActivityControlBoxTower extends AbstractActivityControl{
    private static final int BOX_TYPE_BIG = 2;
    private static final int CHOOSE_BIG = 0;
    private static final int CHOOSE_LEFT = 1;
    private static final int CHOOSE_RIGHT = 2;
    private static final int STEP_NORMAL = 1;
    private static final int STEP_BIG = 2;
    private static final int STEP_NEXT = 3;
    private static ActivityControlBoxTower instance = new ActivityControlBoxTower(0);

    public static ActivityControlBoxTower getInstance(int type) {
        instance.type = type;
        return instance;
    }

    public ActivityControlBoxTower(int type) {
        super(type);
    }

    private boolean checkBigPrize(ControlBoxTowerData controlData, ConfBoxTowerLevel conf) {
        // 未达到最小抽数要求，直接返回false
        if (conf.limited != 0 && controlData.cnt < conf.limited) {
            return false;
        }
        
        // 达到保底次数，必定中奖
        if (conf.guaranteed != 0 && controlData.cnt >= conf.guaranteed) {
            return true;
        }
        
        // 在最小抽数和保底之间，按原概率随机
        return Utils.random(10000) < conf.big_prize_prob;
    }

    private void handleMultiDraw(HumanObject humanObj, int drawCount, boolean isFifty) {
        // 获取活动控制数据
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }

        // 获取活动时间配置
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.game.error("活动时间配置不存在，活动id:{},type={},round={}", data.getActControlData().getActivityType(), type, data.getActControlData().getRound());
            return;
        }

        // 获取控制数据
        ControlBoxTowerData controlData = (ControlBoxTowerData) data.getControlData();
        if (controlData.type != STEP_NORMAL) {
            return;
        }

        // 如果是50连抽,检查层数要求
        if (isFifty && controlData.level < ConfGlobal.get(ConfGlobalKey.BoxTowerLevelDraw).value) {
            return;
        }

        // 获取宝箱等级配置
        ConfBoxTowerLevel confBoxTowerLevel = GlobalConfVal.getBoxTowerLevel(type, confTerm.group_id, controlData.level);
        if (confBoxTowerLevel == null || confBoxTowerLevel.item == null || confBoxTowerLevel.item.length < 2) {
            Log.game.error("宝箱活动配置错误，活动id:{},type={},round={},level={}", data.getActControlData().getActivityType(), type, data.getActControlData().getRound(), controlData.level);
            return;
        }

        // 检查道具数量是否足够
        if (!ProduceManager.inst().canCostProduce(humanObj, confBoxTowerLevel.item[0], confBoxTowerLevel.item[1] * drawCount).success) {
            return;
        }

        // 预先计算抽中大奖的位置
        int bigPrizeIndex = -1;
        for (int i = 0; i < drawCount; i++) {
            ++controlData.cnt;
            if(checkBigPrize(controlData, confBoxTowerLevel)){
                bigPrizeIndex = i;
                break;
            }
        }

        // 实际抽奖次数(如果有大奖则到大奖位置为止，否则为drawCount)
        int actualDraws = bigPrizeIndex >= 0 ? (bigPrizeIndex + 1) : drawCount;


        // 一次性扣除实际消耗
        if(!ProduceManager.inst().costItem(humanObj, confBoxTowerLevel.item[0], confBoxTowerLevel.item[1] * actualDraws, MoneyItemLogKey.宝箱活动).success){
            return;
        }
        // 收集抽奖结果
        List<Define.p_box_tower> boxResults = new ArrayList<>();
        // 累计奖励
        Map<Integer, Integer> totalRewards = new HashMap<>();

        // 执行抽奖
        for (int i = 0; i < actualDraws; i++) {
            // 随机选择左边或右边
            int choose = Utils.random(CHOOSE_LEFT,CHOOSE_RIGHT+1);

            // 获取奖励内容但不立即发放
            Map<Integer, Integer> currentReward = getRewardWithoutGiving(controlData, confTerm, choose);

            // 如果是大奖轮次
            if(i == bigPrizeIndex){
                //进入大奖阶段
                controlData.type = STEP_BIG;
                controlData.choose = choose;

                // 添加到结果列表
                Define.p_box_tower.Builder boxResult = Define.p_box_tower.newBuilder();
                boxResult.setChoose(choose);
                boxResult.setBoxId(controlData.boxId);
                boxResult.setLeftReward(controlData.leftReward);
                boxResult.setRightReward(controlData.rightReward);
                boxResults.add(boxResult.build());
                if(confTerm.reward != null && confTerm.reward.length > 0) {
                    Utils.intArrToIntMap(totalRewards, confTerm.reward);
                }
                break; // 抽到大奖后结束抽奖
            } else {
                // 合并普通奖励
                currentReward.forEach((key, value) ->
                        totalRewards.merge(key, value, Integer::sum));

                controlData.choose = choose;

                // 添加到结果列表
                Define.p_box_tower.Builder boxResult = Define.p_box_tower.newBuilder();
                boxResult.setChoose(choose);
                boxResult.setBoxId(controlData.boxId);
                boxResult.setLeftReward(controlData.leftReward);
                boxResult.setRightReward(controlData.rightReward);
                boxResults.add(boxResult.build());
            }
            // 更新下一次抽奖的boxId
            int index = Utils.randomByWeight2D(confBoxTowerLevel.box_prob,1);
            controlData.boxId = confBoxTowerLevel.box_prob[index][0];
        }
        data.updateControlData();
        // 一次性发放所有累计的普通奖励
        if (!totalRewards.isEmpty()) {
            ProduceManager.inst().produceAdd(humanObj, totalRewards, MoneyItemLogKey.宝箱活动);
        }

        // 发送消息
        if (!boxResults.isEmpty()) {
            if (isFifty) {
                MsgAct.act_box_tower_open_fifty_s2c.Builder msgBuilder = MsgAct.act_box_tower_open_fifty_s2c.newBuilder();
                msgBuilder.setActType(type);
                msgBuilder.addAllBoxList(boxResults);
                humanObj.sendMsg(msgBuilder.build());
            } else {
                MsgAct.act_box_tower_open_ten_s2c.Builder msgBuilder = MsgAct.act_box_tower_open_ten_s2c.newBuilder();
                msgBuilder.setActType(type);
                msgBuilder.addAllBoxList(boxResults);
                humanObj.sendMsg(msgBuilder.build());
            }
        }

        on_act_box_tower_info_c2s(humanObj);
    }

    // 新增方法：获取奖励但不立即发放
    private Map<Integer, Integer> getRewardWithoutGiving(ControlBoxTowerData controlData, ConfActivityTerm confTerm, int choose) {
        ConfBoxTowerBox confBoxTowerBox = ConfBoxTowerBox.get(controlData.boxId);
        if (confBoxTowerBox == null) {
            Log.game.error("宝箱配置不存在，type={},boxId={}", type, controlData.boxId);
            return new HashMap<>();
        }

        Map<Integer,Integer> dropItemLeft = ProduceManager.inst().getDropMap(confBoxTowerBox.output_id);
        Map<Integer,Integer> dropItemRight = ProduceManager.inst().getDropMap(confBoxTowerBox.output_id);
        if(dropItemLeft.isEmpty() || dropItemRight.isEmpty()){
            Log.game.error("宝箱活动掉落为空，type={},level={},boxId={}", type, controlData.level, controlData.boxId);
            return new HashMap<>();
        }

        Define.p_reward.Builder rewardBuilderLeft = Define.p_reward.newBuilder();
        for (Map.Entry<Integer, Integer> entry : dropItemLeft.entrySet()) {
            rewardBuilderLeft.setGtid(entry.getKey());
            rewardBuilderLeft.setNum(entry.getValue());
            break;
        }

        Define.p_reward.Builder rewardBuilderRight = Define.p_reward.newBuilder();
        for (Map.Entry<Integer, Integer> entry : dropItemRight.entrySet()) {
            rewardBuilderRight.setGtid(entry.getKey());
            rewardBuilderRight.setNum(entry.getValue());
            break;
        }

        int[][] extraReward = confTerm.reward;
        if(extraReward != null && extraReward.length > 0) {
            Utils.intArrToIntMap(dropItemLeft, extraReward);
            Utils.intArrToIntMap(dropItemRight, extraReward);
        }

        controlData.leftReward = rewardBuilderLeft.build();
        controlData.rightReward = rewardBuilderRight.build();

        // 返回选中的奖励
        return choose == CHOOSE_LEFT ? dropItemLeft : dropItemRight;
    }

    public void on_act_box_tower_open_ten_c2s(HumanObject humanObj) {
        handleMultiDraw(humanObj, 10, false);
    }

    public void on_act_box_tower_open_fifty_c2s(HumanObject humanObj) {
        handleMultiDraw(humanObj, 50, true);
    }


    public void on_act_box_tower_open_c2s(HumanObject humanObj, int choose) {
        // 获取活动控制数据
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type,data.getActControlData().getRound());
        if(confTerm == null){
            Log.game.error("活动时间配置不存在，活动id:{},type={},round={}", data.getActControlData().getActivityType(), type, data.getActControlData().getRound());
            return;
        }
        ControlBoxTowerData controlData = (ControlBoxTowerData) data.getControlData();
        ConfBoxTowerLevel confBoxTowerLevel = GlobalConfVal.getBoxTowerLevel(type, confTerm.group_id, controlData.level);
        if(confBoxTowerLevel == null || confBoxTowerLevel.item == null || confBoxTowerLevel.item.length < 2){
            Log.game.error("宝箱活动配置错误，活动id:{},type={},round={},level={}", data.getActControlData().getActivityType(), type, data.getActControlData().getRound(), controlData.level);
            return;
        }
        if(choose == 0 && controlData.type == STEP_BIG){
            if(!ProduceManager.inst().checkAndCostItem(humanObj, confBoxTowerLevel.item[0], confBoxTowerLevel.item[1], MoneyItemLogKey.宝箱活动).success){
                return ;
            }

            Map<Integer, Integer> rewardMap = Utils.intArrToIntMap(confBoxTowerLevel.grand_prize);
            if(confTerm.reward != null && confTerm.reward.length > 0) {
                Utils.intArrToIntMap(rewardMap, confTerm.reward);
            }
            ProduceManager.inst().produceAdd(humanObj, rewardMap, MoneyItemLogKey.宝箱活动);
//            Log.game.info("大奖开出了：{}", confBoxTowerLevel.grand_prize[0]);
            controlData.type = STEP_NEXT;
            controlData.choose = choose;
            ++controlData.cnt;
            Define.p_reward.Builder rewardBuilderLeft = Define.p_reward.newBuilder();
            rewardBuilderLeft.setGtid(confBoxTowerLevel.grand_prize[0]);
            rewardBuilderLeft.setNum(confBoxTowerLevel.grand_prize[1]);
            controlData.leftReward = rewardBuilderLeft.build();
            data.updateControlData();
            checkAndBroadCast(humanObj, data.getActControlData(), confBoxTowerLevel);
            send_box_tower_open_s2c(humanObj, controlData);
            on_act_box_tower_info_c2s(humanObj);
            return;
        }
        if(controlData.type != STEP_NORMAL){
            on_act_box_tower_info_c2s(humanObj);
            return;
        }
        if(!ProduceManager.inst().checkAndCostItem(humanObj, confBoxTowerLevel.item[0], confBoxTowerLevel.item[1], MoneyItemLogKey.宝箱活动).success){
            return ;
        }
        ++controlData.cnt;
        if(checkBigPrize(controlData, confBoxTowerLevel)){
            //进入大奖阶段
            giveReward(humanObj, controlData, confTerm, choose);
            controlData.type = STEP_BIG;
            controlData.choose = choose;
        } else {
            if(confBoxTowerLevel.box_prob == null || confBoxTowerLevel.box_prob.length == 0){
                Log.game.error("宝箱概率配置为空，confBoxTowerLevel.sn={},humanId={}", confBoxTowerLevel.sn, humanObj.getHumanId());
                return;
            }
            giveReward(humanObj, controlData, confTerm, choose);
            controlData.choose = choose;
        }
        
        // 更新下一次抽奖的boxId
        int index = Utils.randomByWeight2D(confBoxTowerLevel.box_prob,1);
        controlData.boxId = confBoxTowerLevel.box_prob[index][0];

        data.updateControlData();
        send_box_tower_open_s2c(humanObj, controlData);
        on_act_box_tower_info_c2s(humanObj);
    }

    private void send_box_tower_open_s2c(HumanObject humanObj, ControlBoxTowerData controlData){
        MsgAct.act_box_tower_open_s2c.Builder msg = MsgAct.act_box_tower_open_s2c.newBuilder();
        msg.setActType(type);
        msg.setChoose(controlData.choose);
        msg.setLeftReward(controlData.leftReward);
        msg.setRightReward(controlData.rightReward);
        humanObj.sendMsg(msg);
    }

    public void checkAndBroadCast(HumanObject humanObj, ActControlData actControlData, ConfBoxTowerLevel conf){
        if(conf.is_broadcast != 1){
            return;
        }
        Define.p_key_string.Builder builder = Define.p_key_string.newBuilder();
        builder.setS(humanObj.getHuman().getName());
        builder.setK(conf.grand_prize[0]);
        String broadcast = Utils.toProtoString(builder.build());
        ActivityManager.inst().addActivityBroadcast(humanObj, actControlData, broadcast);
    }

    private void giveReward(HumanObject humanObj, ControlBoxTowerData controlData, ConfActivityTerm confTerm,  int choose) {
        // 获取奖励内容
        Map<Integer, Integer> reward = getRewardWithoutGiving(controlData, confTerm, choose);

        // 发放奖励
        if (!reward.isEmpty()) {
            ProduceManager.inst().produceAdd(humanObj, reward, MoneyItemLogKey.宝箱活动);
        }
    }

    public void on_act_box_tower_next_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.game.error("活动时间配置不存在，活动id:{},type={},round={}", data.getActControlData().getActivityType(), type, data.getActControlData().getRound());
            return;
        }
        ControlBoxTowerData controlData = (ControlBoxTowerData) data.getControlData();
        if(controlData.type != STEP_NEXT){
            return;
        }
        controlData.type = STEP_NORMAL;
        ++controlData.level;
        data.updateControlData();
        on_act_box_tower_info_c2s(humanObj);
    }

    public void on_act_box_tower_auto_next_c2s(HumanObject humanObj) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type, data.getActControlData().getRound());
        if (confTerm == null) {
            Log.game.error("活动时间配置不存在，活动id:{},type={},round={}", data.getActControlData().getActivityType(), type, data.getActControlData().getRound());
            return;
        }
        ControlBoxTowerData controlData = (ControlBoxTowerData) data.getControlData();
        controlData.autoNext = controlData.autoNext == 0 ? 1 : 0;
        MsgAct.act_box_tower_auto_next_s2c.Builder builder = MsgAct.act_box_tower_auto_next_s2c.newBuilder();
        builder.setActType(type);
        builder.setAutoNext(controlData.autoNext);
        humanObj.sendMsg(builder);
    }


    public void on_act_box_tower_info_c2s(HumanObject humanObj) {
        // 获取活动控制数据
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if (data == null) {
            return;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(type,data.getActControlData().getRound());
        if(confTerm == null){
            Log.game.error("活动时间配置不存在，活动id:{},type={},round={}", data.getActControlData().getActivityType(), type, data.getActControlData().getRound());
            return;
        }
        ControlBoxTowerData controlData = (ControlBoxTowerData) data.getControlData();
        ConfBoxTowerLevel confBoxTowerLevel = GlobalConfVal.getBoxTowerLevel(type, confTerm.group_id, controlData.level);
        if(confBoxTowerLevel == null){
            return;
        }

        // 构造协议对象
        MsgAct.act_box_tower_info_s2c.Builder msg = MsgAct.act_box_tower_info_s2c.newBuilder();
        msg.setActType(type);
        msg.setId(confBoxTowerLevel.sn);
        msg.setType(controlData.type);
        msg.setAutoNext(controlData.autoNext);
        msg.setBoxId(controlData.boxId);
        msg.setChoose(controlData.choose);
        if(controlData.leftReward != null){
            msg.setLeftReward(controlData.leftReward);
        }
        if (controlData.rightReward != null){
            msg.setRightReward(controlData.rightReward);
        }
        msg.setCnt(controlData.cnt);

        String redisKey = Utils.createStr("{}.{}.{}.{}", RedisKeys.actBroadcast, humanObj.getHuman().getServerId(), type, data.getActControlData().getRound());
        RedisTools.getListRange(EntityManager.getRedisClient(), redisKey, 0, -1, res -> {
            if (res.failed()) {
                humanObj.sendMsg(msg.build());
                return;
            }
            JsonArray jsonArray = res.result();
            if (jsonArray == null || jsonArray.isEmpty()) {
                humanObj.sendMsg(msg);
                return;
            }
            for (int i = 0; i < jsonArray.size(); i++){
                String protoBufStr = jsonArray.getString(i);
                Define.p_key_string luckyMsg = Utils.fromProtoString(protoBufStr,Define.p_key_string.class);
                msg.addLuckyList(luckyMsg);
            }
            humanObj.sendMsg(msg.build());
        });
    }

}
