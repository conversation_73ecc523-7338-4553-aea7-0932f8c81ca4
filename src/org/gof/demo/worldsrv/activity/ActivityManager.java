package org.gof.demo.worldsrv.activity;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.Node;
import org.gof.core.Port;
import org.gof.core.PortPulseQueue;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.*;
import org.gof.core.support.observer.Listener;
import org.gof.demo.distr.cross.CrossManager;
import org.gof.demo.distr.cross.domain.CrossPoint;
import org.gof.demo.distr.cross.domain.CrossType;
import org.gof.demo.worldsrv.activity.calculator.*;
import org.gof.demo.worldsrv.activity.data.ActivityControlData;
import org.gof.demo.worldsrv.activity.data.ActivityControlObjectData;
import org.gof.demo.worldsrv.activity.data.controldata.ControlDungeonData;
import org.gof.demo.worldsrv.blackMarket.BlackMarketCrossServiceProxy;
import org.gof.demo.worldsrv.character.HumanDailyResetInfo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.*;
import org.gof.demo.worldsrv.entity.ActControlData;
import org.gof.demo.worldsrv.entity.GroupGift;
import org.gof.demo.worldsrv.entity.PocketLine;
import org.gof.demo.worldsrv.global.GlobalConfVal;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.human.RedPointConstants;
import org.gof.demo.worldsrv.inform.Inform;
import org.gof.demo.worldsrv.instance.InstanceConstants;
import org.gof.demo.worldsrv.instance.InstanceManager;
import org.gof.demo.worldsrv.item.ItemConstants;
import org.gof.demo.worldsrv.item.ItemManager;
import org.gof.demo.worldsrv.mail.MailManager;
import org.gof.demo.worldsrv.mall.MallManager;
import org.gof.demo.worldsrv.mall.PayMallTypeKey;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.msg.MsgError;
import org.gof.demo.worldsrv.pocketLine.Pocket;
import org.gof.demo.worldsrv.pocketLine.PocketLineEventSubKey;
import org.gof.demo.worldsrv.pocketLine.PocketLineManager;
import org.gof.demo.worldsrv.produce.ProduceManager;
import org.gof.demo.worldsrv.produce.ProduceVo;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.support.ReasonResult;
import org.gof.demo.worldsrv.support.Util;
import org.gof.demo.worldsrv.support.enumKey.ConfGlobalKey;
import org.gof.demo.worldsrv.support.enumKey.DailyResetTypeKey;
import org.gof.demo.worldsrv.support.enumKey.MoneyItemLogKey;
import org.gof.demo.worldsrv.support.observer.EventKey;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.task.type.TaskVO;
import org.gof.demo.worldsrv.task.type.activityData.ActivityTaskVO;

import java.util.*;


public class ActivityManager extends ManagerBase {
    public static ActivityManager inst() {
        return inst(ActivityManager.class);
    }

    @Listener(EventKey.HUMAN_RESET_ZERO)
    public void _listener_HUMAN_LOGIN(Param param) {
        HumanObject humanObj = param.get("humanObj");
    }

    /**
     * 每周一0点
     * @param param
     */
    @Listener(EventKey.HUMAN_RESET_WEEK_ZERO)
    public void _listener_HUMAN_RESET_WEEK_ZERO(Param param) {
        HumanObject humanObj = param.get("humanObj");
        IActivityControl control = ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_33);
        if(control == null) {
            return;
        }
        Log.crossWar.info("玩家={} {} 周一0点更新跨服战活动状态", humanObj.id, humanObj.name);
        control.sendActivityData(humanObj);
    }

    @Listener(EventKey.ACTIVITY_ADD_PROGRESS)
    public void _listener_ACTIVITY_ADD_PROGRESS(Param param) {
        HumanObject humanObj = param.get("humanObj");
        Map<Integer, ActivityControlObjectData> controlDataMap = humanObj.operation.activityControlData.getControlObjectDataMap();
        for (Integer actType : controlDataMap.keySet()) {
            ActivityControlObjectData data = controlDataMap.get(actType);
            if (!isActivityOpen(humanObj, actType) || data == null) {
                continue;// 活动没开或者没活动数据，直接跳过
            }
            IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
            if (control == null) {
                continue;// 没有活动控制类，直接跳过
            }
            control.handleEvent(humanObj, EventKey.ACTIVITY_ADD_PROGRESS, param);
        }
    }


    private void initActivityData(HumanObject humanObj, List<ActivityVo> openVoList) {
        for (ActivityVo vo : openVoList) {
            ConfActivityControl conf = ConfActivityControl.get(vo.activitySn);
            if (conf == null) {
                Log.game.error("活动配置不存在，活动sn:{}", vo.activitySn);
                continue;
            }
            IActivityControl control =  ActivityControlTypeFactory.getTypeData(conf.type);
            if(control == null) {
                Log.game.error("类型不存在，活动sn:{}, type={}", vo.activitySn, conf.type);
                continue;
            }
            control.initActivityData(humanObj, conf, vo);
        }

    }

    public void dailyResetActivityData(HumanObject humanObj) {
        Map<Integer,ActivityControlObjectData> controlObjectDataMap = humanObj.operation.activityControlData.getControlObjectDataMap();
        Iterator<Map.Entry<Integer, ActivityControlObjectData>> iterator = controlObjectDataMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, ActivityControlObjectData> entry = iterator.next();
            ActivityControlObjectData data = entry.getValue();
            ActControlData actData = data.getActControlData();
            if(!isActivityOpen(humanObj, actData.getActivityType())) {
                continue;
            }
            AbstractActivityControl activityControl = (AbstractActivityControl) ActivityControlTypeFactory.getTypeData(data.getActControlData().getActivityType());
            activityControl.dailyResetActivityData(humanObj);
        }
    }

    public boolean dailyResetActivityData(HumanObject humanObj, int type) {
        AbstractActivityControl activityControl = (AbstractActivityControl) ActivityControlTypeFactory.getTypeData(type);
        if(activityControl != null){
            activityControl.dailyResetActivityData(humanObj);
            return true;
        }
        return false;
    }

    public boolean isActivityOpen(HumanObject humanObj, int type) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if(data == null) {
            return false;
        }
        if(data.getActControlData().getState() == EActivityType.STATE_ENDSHOW) {
            return false;
        }
        return true;
    }

    public boolean isActivityOpenOrShow(HumanObject humanObj, int type) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if(data == null) {
            return false;
        }
        return true;
    }

    public boolean isActivityOpen(HumanObject humanObj, int type, int roundMin, int roundMax) {
        for (int activitySn : humanObj.openActivitySnList) {
            ConfActivityControl conf = ConfActivityControl.get(activitySn);
            if(conf == null) {
                Log.game.info("玩家{}获取活动ConfActivityControl失败sn={}", humanObj.id, activitySn);
                continue;
            }
            if(conf.type == type) {
                ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
                if(data == null) {
                    return false;
                }
                if(data.getActControlData().getRound() >= roundMin && data.getActControlData().getRound() <= roundMax) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isActivityOpenOrShow(HumanObject humanObj, int type, int roundMin, int roundMax) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if(data == null) {
            return false;
        }
        if(data.getActControlData().getRound() >= roundMin && data.getActControlData().getRound() <= roundMax) {
            return true;
        }
        return false;
    }

    public boolean isActivityOpenAndUnlock(HumanObject humanObj, int type, int roundMin, int roundMax) {
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(type);
        if(data == null) {
            return false;
        }
        if(data.getActControlData().getRound() < roundMin || data.getActControlData().getRound() > roundMax) {
            return false;
        }
        ConfActivityControl conf = ConfActivityControl.get(data.getActControlData().getActivitySn());
        if (conf == null || !humanObj.isModUnlock(conf.newfunctionID)) {
            return false;
        }
        return true;
    }

    public int getTaskState(int taskState){
        if(taskState == TaskConditionTypeKey.TASK_STATUS_已完成){
            return EActivityType.TASK_CAN_GET;
        }else if(taskState == TaskConditionTypeKey.TASK_STATUS_已领取奖励){
            return EActivityType.TASK_HAD_GET;
        }
        return EActivityType.TASK_NORMAL;
    }


    /**
     * 战争令牌信息C2S消息
     */
    public void on_act_war_token_info_c2s(HumanObject humanObj) {
        ActivityControlWarToken control = (ActivityControlWarToken) ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_2100);
        if(control == null) {
            return;
        }
        control.on_act_war_token_info_c2s(humanObj);
    }

    public Define.p_war_token_task.Builder buildWarTokenTask(TaskVO vo, ConfWartokenTask conf, int resetTime) {
        Define.p_war_token_task.Builder task = Define.p_war_token_task.newBuilder();
        task.setTaskId(vo.taskSn);
        task.setType(conf.type);
        task.setState(ActivityManager.inst().getTaskState(vo.getStatus()));
        task.setCount(vo.getPlan());
        task.setResetType(conf.reset);
        task.setResetTime(resetTime);
        task.setAddExp(conf.reward);
        return task;
    }

    public Define.p_war_token_task.Builder buildWarTokenTask(TaskVO vo, ConfBattlePassTask conf, int resetTime) {
        Define.p_war_token_task.Builder task = Define.p_war_token_task.newBuilder();
        task.setTaskId(vo.taskSn);
        task.setType(conf.type);
        task.setState(ActivityManager.inst().getTaskState(vo.getStatus()));
        task.setCount(vo.getPlan());
        task.setResetType(conf.reset);
        task.setResetTime(resetTime);
        task.setAddExp(conf.reward);
        return task;
    }

    /**
     * 战争令牌任务奖励
     * @param taskId 任务ID
     */
    public void on_act_war_token_task_reward_c2s(HumanObject humanObj, int taskId) {
        ActivityControlWarToken control = (ActivityControlWarToken)ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_2100);
        if(control == null) {
            return;
        }
        control.on_act_war_token_task_reward_c2s(humanObj, taskId);
    }

    /**
     * 战争令牌等级奖励
     * @param takeLev 等级
     * @param rewardType 奖励类型
     */
    public void on_act_war_token_lev_reward_c2s(HumanObject humanObj, int takeLev, int rewardType) {
        ActivityControlWarToken control = (ActivityControlWarToken) ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_2100);
        if(control == null) {
            return;
        }
        control.on_act_war_token_lev_reward_c2s(humanObj, takeLev, rewardType);
    }

    /**
     * 战争令牌追加奖励
     */
    public void on_act_war_token_add_reward_c2s(HumanObject humanObj) {
        ActivityControlWarToken control = (ActivityControlWarToken) ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_2100);
        if(control == null) {
            return;
        }
        control.on_act_war_token_add_reward_c2s(humanObj);
    }

    @Listener(EventKey.HUMAN_LOGIN_FINISH)
    public void onLoginFinish(Param param) {
        HumanObject humanObj = param.get("humanObj");
        handleOffineActClose(humanObj);
        sendActivityOpen(humanObj);
        // 推活动日历信息
        sendMsg_calendar_info_s2c(humanObj);
    }

    /**
     * 处理玩家参与了活动并且生成活动数据，活动关闭时刻玩家不在线的情况，这个时候补走一遍活动关闭逻辑
     */
    private void handleOffineActClose(HumanObject humanObj) {
        ActivityControlData controlData = humanObj.operation.activityControlData;
        if (controlData == null) {
            return;
        }
        // 1.收集已关闭的活动
        int currTime = Utils.getTimeSec();
        List<ActControlData> closeActList = new ArrayList<>();
        List<ActControlData> endShowActList = new ArrayList<>();
        Map<Integer, ActivityControlObjectData> controlDataMap = controlData.getControlObjectDataMap();
        for (Map.Entry<Integer, ActivityControlObjectData> entry : controlDataMap.entrySet()) {
            ActivityControlObjectData data = entry.getValue();
            ActControlData actData = data.getActControlData();
            if (actData.getCloseTime() <= currTime) {
                closeActList.add(actData);
                ConfActivityControl conf = ConfActivityControl.get(actData.getActivitySn());
                if (conf == null) {
                    endShowActList.add(actData);
                    Log.activity.error("玩家登陆后处理残留的已关闭活动重走活动关闭逻辑时，获取活动配置失败，humanId={}, 活动sn={}", humanObj.id, actData.getActivitySn());
                    continue;
                }
                if(actData.getCloseTime() + conf.showTime <= currTime){
                    endShowActList.add(actData);
                }
            }
        }
        // 2.重走活动关闭逻辑
        for (ActControlData actData : closeActList) {
            int sn = actData.getActivitySn();
            int actType = actData.getActivityType();
            ConfActivityControl conf = ConfActivityControl.get(sn);
            if (conf == null) {
                Log.activity.error("玩家登陆后处理残留的已关闭活动重走活动关闭逻辑时，获取活动配置失败，humanId={}, 活动sn={}", humanObj.id, sn);
                continue;
            }
            IActivityControl icontrol = ActivityControlTypeFactory.getTypeData(actType);
            icontrol.onActivityClose(humanObj, conf);
            if(endShowActList.contains(actData)){
                icontrol.onActivityEndShow(humanObj, conf);
            }
        }
    }

    public void sendActivityOpen(HumanObject humanObj) {
       ActivityControlServiceProxy proxy = ActivityControlServiceProxy.newInstance();
       proxy.getOpenActivityList(humanObj.getHuman().getServerId());
       proxy.listenResult(this::_result_getOpenActivityList, "humanObj",humanObj);
    }

    private void _result_getOpenActivityList(Param result, Param context) {
        HumanObject humanObj = context.get("humanObj");
        List<ActivityVo> openVoList = result.get("openVoList");
        for(ActivityVo vo : openVoList){
            if(vo.isShowPeriod){
                humanObj.showActivitySnList.add(vo.activitySn);
            }else {
                humanObj.openActivitySnList.add(vo.activitySn);
            }
        }
        if(Config.DATA_DEBUG){
            Log.activity.info("=====humanId={}, openActivitySnList={}, openActivitySnList={}", humanObj.id, openVoList, humanObj.openActivitySnList);
        }
        initActivityData(humanObj,openVoList);

        //每日登录活动记录进度
        List<Integer> activityTypeList = ActivityControlTypeFactory.getTypeList(ActivityControlLogin.class);
        for (int type : activityTypeList) {
            ActivityControlLogin control = ActivityControlLogin.getInstance(type);
            control.onHumanLogin(humanObj);
        }
        on_act_list_c2s(humanObj);
        Map<Integer, ActivityControlObjectData> controlObjectDataMap = humanObj.operation.activityControlData.getControlObjectDataMap();
        for (Map.Entry<Integer, ActivityControlObjectData> entry : controlObjectDataMap.entrySet()) {
            ActivityControlObjectData data = entry.getValue();
            int actSn = data.getActControlData().getActivitySn();
            if(!humanObj.openActivitySnList.contains(actSn) && !humanObj.showActivitySnList.contains(actSn)){
                if(Config.DATA_DEBUG){
                    Log.game.info("玩家{}不显示活动{}", humanObj.id, actSn);
                }
                continue;
            }
            ConfActivityControl conf = ConfActivityControl.get(actSn);
            if(!humanObj.isModUnlock(conf.newfunctionID)){
                if(Config.DATA_DEBUG){
                    Log.game.info("玩家{}功能未解锁 活动sn{}, fun={}", humanObj.id, actSn, conf.newfunctionID);
                }
                continue;
            }
            AbstractActivityControl activityControl = (AbstractActivityControl) ActivityControlTypeFactory.getTypeData(data.getActControlData().getActivityType());
            activityControl.sendActivityData(humanObj);
        }
        if(Config.DATA_DEBUG){
            Log.game.info("玩家{}活动开启列表{}, vo={}", humanObj.id, humanObj.openActivitySnList, openVoList);
        }

        MallManager.inst().sendMsg_pay_mall_info_s2c(humanObj, PayMallTypeKey.PAY_Type_7);
        MallManager.inst().sendAndAddActivityConditionPayMallInfo(humanObj);
        // 单独处理充值代办
        PocketLineManager.inst().loadPocketLinePay(humanObj);
    }

    @Listener(EventKey.ACTIVITY_OPEN)
    public void onActivityOpen(Param param) {
        HumanObject humanObj = param.get("humanObj");
        ActivityVo activityVo = param.get("activityVo");
        initActivityData(humanObj, Arrays.asList(activityVo));
        ConfActivityControl conf = ConfActivityControl.get(activityVo.activitySn);
        if(conf == null) {
            Log.game.error("玩家{}获取活动ConfActivityControl失败sn={}", humanObj.id, activityVo.activitySn);
            return;
        }
        IActivityControl iActivityControl =  ActivityControlTypeFactory.getTypeData(conf.type);
        if(iActivityControl == null){
            Log.game.error("玩家{}获取活动IActivi tyControl失败type={}", humanObj.id, conf.type);
            return;
        }
        iActivityControl.sendActivityData(humanObj);
        Log.game.error("玩家{}活动开启sn={}", humanObj.id, activityVo.activitySn);
        MallManager.inst().sendActivityPayMallUpdate(humanObj,conf.type);
    }

    public void onActivityClose(HumanObject humanObj, int activitySn) {
        ConfActivityControl conf = ConfActivityControl.get(activitySn);
        if(conf == null) {
            Log.game.error("玩家{}获取活动ConfActivityControl失败sn={}", humanObj.id, activitySn);
            return;
        }
        IActivityControl iActivityControl =  ActivityControlTypeFactory.getTypeData(conf.type);
        if(iActivityControl == null){
            Log.game.error("玩家{}获取活动IActivityControl失败type={}", humanObj.id, conf.type);
            return;
        }
        iActivityControl.onActivityClose(humanObj, conf);
    }

    public void onActivityEndShow(HumanObject humanObj, int activitySn){
        ConfActivityControl conf = ConfActivityControl.get(activitySn);
        if(conf == null) {
            Log.game.error("玩家{}获取活动ConfActivityControl失败sn={}", humanObj.id, activitySn);
            return;
        }
        IActivityControl iActivityControl =  ActivityControlTypeFactory.getTypeData(conf.type);
        if(iActivityControl == null){
            Log.game.error("玩家{}获取活动IActivityControl失败type={}", humanObj.id, conf.type);
            return;
        }
        iActivityControl.onActivityEndShow(humanObj, conf);
    }

    public void onActivityCloseAndEndShow(HumanObject humanObj, int activitySn){
        ConfActivityControl conf = ConfActivityControl.get(activitySn);
        if(conf == null) {
            Log.game.error("玩家{}获取活动ConfActivityControl失败sn={}", humanObj.id, activitySn);
            return;
        }
        IActivityControl iActivityControl =  ActivityControlTypeFactory.getTypeData(conf.type);
        if(iActivityControl == null){
            Log.game.error("玩家{}获取活动IActivityControl失败type={}", humanObj.id, conf.type);
            return;
        }
        iActivityControl.onActivityClose(humanObj, conf);
        iActivityControl.onActivityEndShow(humanObj, conf);
    }

    public void on_act_list_c2s(HumanObject humanObj) {
        if (humanObj.openActivitySnList.isEmpty() && humanObj.showActivitySnList.isEmpty()) {
            return;
        }
        MsgAct.act_list_s2c.Builder msg = MsgAct.act_list_s2c.newBuilder();
        Set<Integer> allActivitySns = new HashSet<>();
        allActivitySns.addAll(humanObj.openActivitySnList);
        allActivitySns.addAll(humanObj.showActivitySnList);

        for (int sn : allActivitySns) {
            ConfActivityControl conf = ConfActivityControl.get(sn);
            if (conf == null) {
                Log.game.error("玩家{}获取活动ConfActivityControl失败sn={}", humanObj.id, sn);
                continue;
            }
            if (!humanObj.isModUnlock(conf.newfunctionID)) {
                continue;
            }
            if (S.isSelectLog) {
                Log.temp.info("===humanId={}, activitySn={}, modUnlock={}, activityType={}", humanObj.id, sn, conf.newfunctionID, conf.type);
            }
            IActivityControl control = ActivityControlTypeFactory.getTypeData(conf.type);
            Define.p_act.Builder act = control.toActivityData(humanObj);
            if (act != null) {
                msg.addList(act);
            }
        }
        if (S.isSelectLog) {
            Log.temp.info("===humanId={}, msg={}", humanObj.id, msg);
        }
        humanObj.sendMsg(msg);
    }
    /**
     * 检查活动是否关闭或进入展示期
     */
    public void checkActivityCloseOrEndShow(HumanObject humanObj) {
        // 一次遍历处理所有活动状态
        Map<Integer, Integer> activityStatusMap = checkActivityStatus(humanObj);

        // 处理不同状态的活动
        for (Map.Entry<Integer, Integer> entry : activityStatusMap.entrySet()) {
            int actId = entry.getKey();
            int status = entry.getValue();

            if (status == EActivityType.STATE_NULL) {
                // 完全结束的活动
                ActivityManager.inst().onActivityCloseAndEndShow(humanObj, actId);
                if (humanObj.openActivitySnList.contains(actId)) {
                    humanObj.openActivitySnList.remove(actId);
                    if(humanObj.openHumanActivitySnList.contains(actId)){
                        humanObj.openHumanActivitySnList.remove(actId);
                    }
                }
                if (humanObj.showActivitySnList.contains(actId)) {
                    ActivityManager.inst().onActivityEndShow(humanObj, actId);
                    humanObj.showActivitySnList.remove(actId);
                }
            } else if (status == EActivityType.STATE_ENDSHOW) {
                // 进入展示期的活动
                ActivityManager.inst().onActivityClose(humanObj, actId);
                if (humanObj.openActivitySnList.contains(actId)) {
                    humanObj.openActivitySnList.remove(actId);
                    if (humanObj.openHumanActivitySnList.contains(actId)) {
                        humanObj.openHumanActivitySnList.remove(actId);
                    }
                }
                if(!humanObj.showActivitySnList.contains(actId)){
                    humanObj.showActivitySnList.add(actId);
                }
            }
        }
    }

    /**
     * 检查所有活动状态，返回需要处理的活动及其状态
     * @return Map<活动ID, 状态码>，状态码：0-完全结束，3-进入展示期
     */
    public Map<Integer, Integer> checkActivityStatus(HumanObject humanObj) {
        long now = Utils.getTimeSec();
        Map<Integer, Integer> activityStatusMap = new HashMap<>();
        Map<Integer, ActivityControlObjectData> controlObjectDataMap = humanObj.operation.activityControlData.getControlObjectDataMap();
        // 遍历所有活动数据
        for (Map.Entry<Integer, ActivityControlObjectData> entry : controlObjectDataMap.entrySet()) {
            ActivityControlObjectData objectData = entry.getValue();

            if (objectData == null || objectData.getActControlData() == null) {
                continue;
            }

            int actSn = objectData.getActControlData().getActivitySn();
            ConfActivityControl conf = ConfActivityControl.get(actSn);
            if (conf == null) {
                continue;
            }

            int closeTime = objectData.getActControlData().getCloseTime();

            // 检查活动状态
            if (closeTime <= now) {
                // 活动已结束
                if (conf.showTime > 0) {
                    // 有展示期
                    int showEndTime = closeTime + conf.showTime;

                    if (now >= showEndTime) {
                        // 展示期也结束了，完全结束
                        activityStatusMap.put(actSn, EActivityType.STATE_NULL);
                    } else {
                        // 刚进入展示期
                        activityStatusMap.put(actSn, EActivityType.STATE_ENDSHOW);
                    }
                } else {
                    // 没有展示期，直接完全结束
                    activityStatusMap.put(actSn, EActivityType.STATE_NULL);
                }
            }
        }

        return activityStatusMap;
    }

    public int getActivityTermSn(int activityType, int round){
        ConfActivityTerm conf = getActivityTerm(activityType, round);
        if(conf != null){
            return conf.sn;
        }
        return 0;
    }

    public ConfActivityTerm getActivityTerm(int activityType, int round){
        return GlobalConfVal.getConfActivityTerm(activityType, round);
    }

    public boolean isActivityGroupPerfectReward(HumanObject humanObj, int actGroup) {
        Map<Integer, Integer> rewardMap = Utils.jsonToMapIntInt(humanObj.getHumanExtInfo().getActGroupInfoMap());
        rewardMap.entrySet().removeIf(entry -> entry.getValue() < Port.getTime()/Time.SEC);
        return rewardMap.containsKey(actGroup);
    }

    public void addActivityGroupPerfectReward(HumanObject humanObj, int actGroup, int endTime){
        Map<Integer,Integer> rewardMap = Utils.jsonToMapIntInt(humanObj.getHumanExtInfo().getActGroupInfoMap());
        rewardMap.put(actGroup, endTime);
        humanObj.getHumanExtInfo().setActGroupInfoMap(Utils.mapIntIntToJSON(rewardMap));
//        humanObj.getHumanExtInfo().update();
    }


    public Define.p_act_task.Builder to_p_act_task(ActivityTaskVO vo) {
        Define.p_act_task.Builder task = Define.p_act_task.newBuilder();
        task.setTaskId(vo.getTaskSn());
        task.setGroupId(vo.getGroupSn());
        task.setCount(vo.getPlan());
        task.setState(getTaskState(vo.getStatus()));
        return task;
    }

    public Define.p_act_task.Builder to_p_act_task_group(int group, Map<Integer,Integer> perfectMap) {
        Define.p_act_task.Builder groupTask = Define.p_act_task.newBuilder();
        groupTask.setGroupId(group);
        groupTask.setTaskId(0);
        int perfectFlag = perfectMap.getOrDefault(group, 0);
        groupTask.setState(perfectFlag);
        if(perfectFlag == EActivityType.TASK_NORMAL){
            groupTask.setCount(0);
        }else{
            groupTask.setCount(1);
        }
        return groupTask;
    }

    public boolean isAllTasksCompletedInGroup(Map<Integer, ActivityTaskVO> taskVOMap, int groupSn) {
        for (ActivityTaskVO taskVO : taskVOMap.values()) {
            if (taskVO.getGroupSn() == groupSn
                    && (taskVO.getStatus() == TaskConditionTypeKey.TASK_STATUS_进行中 || taskVO.getStatus() == TaskConditionTypeKey.TASK_STATUS_0)) {
                return false;
            }
        }
        return true;
    }

    public void addActivityBroadcast(HumanObject humanObj, ActControlData actControlData, String report) {
        int size = 20;
        int serverId = humanObj.getHuman().getServerId();
        int type = actControlData.getActivityType();
        int round = actControlData.getRound();
        String key = Utils.createStr("{}.{}.{}.{}", RedisKeys.actBroadcast, serverId, type, round);

        RedisTools.getLen(EntityManager.redisClient, key,res->{
            if(res.failed()){
                Log.game.error("addActivityBroadcast getLen failed, key={}, err={}", key, res.cause().getMessage());
                return;
            }
            Long listSize = res.result() == null ? 0L : res.result();
            RedisTools.pushToList(EntityManager.redisClient, key, report);
            if (listSize >= size) {
                RedisTools.ltrim(EntityManager.redisClient, key, 0, size - 1);
            }

            if (listSize <= 0) {
                int expireTime = (int)(actControlData.getCloseTime() - Port.getTime() / Time.SEC);
                RedisTools.expire(EntityManager.redisClient, key, expireTime);
            }

        });
    }

    /**
     * 返回活动播报
     */
    public void on_act_broadcast_c2s(HumanObject humanObj, int actType) {
        if (!ActivityManager.inst().isActivityOpen(humanObj, actType)) {
            return;
        }
        if (ActivityControlTypeFactory.blackMarketSet.contains(actType)) {
            // 黑市的播报走跨服逻辑
            getBlackMarketBroadCast(humanObj, actType);
            return;
        }
        ActivityControlObjectData controlData = humanObj.operation.activityControlData.getControlObjectData(actType);
        ActControlData actControlData = controlData.getActControlData();

        int serverId = humanObj.getHuman().getServerId();
        int type = actControlData.getActivityType();
        int round = actControlData.getRound();
        String key = Utils.createStr("{}.{}.{}.{}", RedisKeys.actBroadcast, serverId, type, round);

        List<String> reportList = Utils.getRedisList(key);
        if (reportList.isEmpty()) {
            return;
        }
        MsgAct.act_broadcast_s2c.Builder msg = MsgAct.act_broadcast_s2c.newBuilder();
        msg.setActType(type);
        msg.setOperationType(0);
        for (String report : reportList) {
            msg.addReportList(to_act_broadcast_report(Utils.toJSONObject(report)));
        }
        humanObj.sendMsg(msg);
    }

    public void getBlackMarketBroadCast(HumanObject humanObj, int actType) {
        int serverId = humanObj.getHuman().getServerId();
        int group = GlobalConfVal.getCrossServerIdGroup(CrossType.cross_black_market.getType(), serverId);
        if (group == 0) {
            Log.activity.error("[黑市]取不到跨服分组, type={}, serverId={}", CrossType.cross_black_market.getType(), serverId);
            return;
        }
        CrossManager.getInstance().callCrossFunc(CrossType.cross_black_market, serverId, handler -> {
            if (handler.failed()) {
                Log.activity.error("[黑市]查询跨服节点报错, serverId={}", serverId, handler.cause());
                return;
            }
            CrossPoint point = handler.result();
            BlackMarketCrossServiceProxy prx = BlackMarketCrossServiceProxy.newInstance(point.getNodeId());
            prx.getBlackMarketBroadCast(serverId, actType);
            prx.listenResult((result, context) -> {
                ReasonResult rr = result.get("result");
                if (!rr.success) {
                    Inform.user(humanObj.id, Inform.提示操作, rr);
                    return;
                }
                MsgAct.act_broadcast_s2c msg = result.get("msg");
                humanObj.sendMsg(msg);
            });
        });
    }

    public Define.p_act_broadcast_report.Builder to_act_broadcast_report(JSONObject json) {
        Define.p_act_broadcast_report.Builder p_builder = Define.p_act_broadcast_report.newBuilder();
        p_builder.setRoleId(json.getLongValue("id"));
        p_builder.setTargetName(json.getString("n"));
        p_builder.setValue(json.getIntValue("v"));
        p_builder.setTime(json.getIntValue("t"));
        p_builder.setPaySn(json.getIntValue("s"));
        p_builder.setIsHideName(Utils.intValue(json.get("hide")));
        return p_builder;
    }

    public void on_act_task_reward_c2s(HumanObject humanObj, int type, int taskId, int groupId) {
        AbstractActivityControl control = (AbstractActivityControl) ActivityControlTypeFactory.getTypeData(type);
        if(control == null) {
            return;
        }
        control.on_act_task_reward_c2s(humanObj, taskId, groupId);
    }

    /**
     * 活动进度
     */
    public void addActivityProgress(HumanObject humanObj, int conditionType, long progress, Object... objs) {
        addActivityProgress(humanObj, conditionType, progress, true, objs);
    }

    public void addActivityProgress(HumanObject humanObj, int conditionType, long progress, boolean isAddProgress, Object... objs) {
        Map<Integer, ActivityControlObjectData> controlObjectDataMap = humanObj.operation.activityControlData.getControlObjectDataMap();
        for (Map.Entry<Integer, ActivityControlObjectData> entry : controlObjectDataMap.entrySet()) {
            ActivityControlObjectData data = entry.getValue();
            ConfActivityControl conf = ConfActivityControl.get(data.getActControlData().getActivitySn());
            if(conf == null) {
                Log.game.error("玩家{}获取活动ConfActivityControl失败sn={}", humanObj.id, data.getActControlData().getActivitySn());
                continue;
            }
            ActControlData actData = data.getActControlData();
            if(actData.getState() == EActivityType.STATE_ENDSHOW) {
                continue;
            }
            AbstractActivityControl activityControl = (AbstractActivityControl) ActivityControlTypeFactory.getTypeData(data.getActControlData().getActivityType());
            activityControl.addActivityProgress(humanObj, conditionType, progress, isAddProgress, objs);
        }
    }

    /**
     * 坐骑嘉年华信息
     * @param actType 活动类型
     */
    public void on_act_mount_carnival_info_c2s(HumanObject humanObj, int actType) {
        ActivityControlMountDraw control = (ActivityControlMountDraw) ActivityControlTypeFactory.getTypeData(actType);
        ActivityControlObjectData data = humanObj.operation.activityControlData.getControlObjectData(actType);
        if(data == null) {
            return;
        }
        control.on_act_mount_carnival_info_c2s(humanObj);
    }


    /**
     * 坐骑嘉年华抽奖
     *
     * @param actType 活动类型
     * @param drawType     抽奖类型
     */
    public void mount_carnival_draw(HumanObject humanObj, int actType, int drawType) {
        ActivityControlMountDraw control = (ActivityControlMountDraw) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.mount_carnival_draw(humanObj, drawType);
    }

    /**
     * 坐骑嘉年华累计奖励
     * @param actType 活动类型
     * @param id 奖励ID
     */
    public void mount_carnival_count_reward(HumanObject humanObj, int actType, int id) {
        ActivityControlMountDraw control = (ActivityControlMountDraw) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.mount_carnival_count_reward(humanObj, id);
    }

    public void on_act_mount_carnival_choose_c2s(HumanObject humanObj, int actType, int dropId, int choose) {
        ActivityControlMountDraw control = (ActivityControlMountDraw) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_mount_carnival_choose_c2s(humanObj, dropId, choose);
    }

    public void on_act_autumn_pig_find_c2s(HumanObject humanObj, int actType, int pigId) {
        ActivityControlFestivalSearch control = (ActivityControlFestivalSearch) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_autumn_pig_find_c2s(humanObj, pigId);
    }

    public void on_act_break_gold_egg_info_c2s(HumanObject humanObj, int actType) {
        ActivityControlBreakEgg control = (ActivityControlBreakEgg) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_break_gold_egg_info_c2s(humanObj);
    }

    public void on_act_break_gold_egg_break_c2s(HumanObject humanObj, int actType, int pos) {
        ActivityControlBreakEgg control = (ActivityControlBreakEgg) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_break_gold_egg_break_c2s(humanObj, pos);
    }

    public void on_act_break_gold_egg_choose_c2s(HumanObject humanObj, int actType, int goodsId) {
        ActivityControlBreakEgg control = (ActivityControlBreakEgg) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            Inform.sendMsg_error(humanObj, 173);
            return;
        }
        control.on_act_break_gold_egg_choose_c2s(humanObj, goodsId);
    }

    public void on_act_break_gold_egg_next_c2s(HumanObject humanObj, int actType) {
        ActivityControlBreakEgg control = (ActivityControlBreakEgg) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_break_gold_egg_next_c2s(humanObj);
    }

    public void on_act_gold_egg_count_reward_c2s(HumanObject humanObj, int actType, int rewardId) {
        ActivityControlBreakEgg control = (ActivityControlBreakEgg) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_gold_egg_count_reward_c2s(humanObj, rewardId);
    }

    public void on_login_info(HumanObject humanObj, int actType) {
        IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
        if(control instanceof ActivityControlLogin) {
            ActivityControlLogin loginControl = (ActivityControlLogin) control;
            loginControl.onLoginInfo(humanObj);
        }else if(control instanceof ActivityControlLogin2) {
            ActivityControlLogin2 loginControl = (ActivityControlLogin2) control;
            loginControl.onLoginInfo(humanObj);
        }else {
            Log.activity.error("玩家{}获取活动IActivityControl失败type={}", humanObj.id, actType);
        }
    }


    public void on_login_reward(HumanObject humanObj, int actType, int sn, int gear) {
        IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
        if(control instanceof ActivityControlLogin) {
            ActivityControlLogin loginControl = (ActivityControlLogin) control;
            loginControl.onLoginReward(humanObj, sn);
        }else if(control instanceof ActivityControlLogin2) {
            ActivityControlLogin2 loginControl = (ActivityControlLogin2) control;
            loginControl.onLoginReward(humanObj, sn, gear);
        }else {
            Log.activity.error("玩家{}获取活动IActivityControl失败type={}", humanObj.id, actType);
        }
    }

    public void on_act_lucky_cat_info_c2s(HumanObject humanObj, int actType) {
        ActivityControlLuckyCat control = (ActivityControlLuckyCat) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_lucky_cat_info_c2s(humanObj);
    }

    public void on_act_lucky_cat_c2s(HumanObject humanObj, int actType) {
        ActivityControlLuckyCat control = (ActivityControlLuckyCat) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_lucky_cat_c2s(humanObj);
    }

    public void on_act_lucky_cat_report_c2s(HumanObject humanObj, int actType) {
        ActivityControlLuckyCat control = (ActivityControlLuckyCat) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_lucky_cat_report_c2s(humanObj);
    }

    public void on_war_token_info_c2s(HumanObject humanObj, int id) {
        ConfWartoken conf = ConfWartoken.get(id);
        if (conf == null) {
            return;
        }
        ActivityControlWarToken control = (ActivityControlWarToken) ActivityControlTypeFactory.getTypeData(conf.act_type);
        if(control == null) {
            return;
        }
        control.on_war_token_info_c2s(humanObj);
    }

    public void on_war_token_task_reward_c2s(HumanObject humanObj, int id, int taskId) {
        ConfWartoken conf = ConfWartoken.get(id);
        if (conf == null) {
            return;
        }
        ActivityControlWarToken control = (ActivityControlWarToken) ActivityControlTypeFactory.getTypeData(conf.act_type);
        if(control == null) {
            return;
        }
        control.on_war_token_task_reward_c2s(humanObj, taskId);
    }

    public void on_war_token_lev_reward_c2s(HumanObject humanObj, int id, int takeLev, int rewardType) {
        ConfWartoken conf = ConfWartoken.get(id);
        if (conf == null) {
            return;
        }
        ActivityControlWarToken control = (ActivityControlWarToken) ActivityControlTypeFactory.getTypeData(conf.act_type);
        if(control == null) {
            return;
        }
        control.on_war_token_lev_reward_c2s(humanObj, takeLev, rewardType);
    }

    public void on_war_token_add_reward_c2s(HumanObject humanObj, int id) {
        ConfWartoken conf = ConfWartoken.get(id);
        if (conf == null) {
            return;
        }
        ActivityControlWarToken control = (ActivityControlWarToken) ActivityControlTypeFactory.getTypeData(conf.act_type);
        if(control == null) {
            return;
        }
        control.on_war_token_add_reward_c2s(humanObj);
    }

    public void on_war_token_claim_special_reward_c2s(HumanObject humanObj, int id, int cfgId) {
        ConfWartoken conf = ConfWartoken.get(id);
        if (conf == null) {
            return;
        }
        ActivityControlWarToken control = (ActivityControlWarToken) ActivityControlTypeFactory.getTypeData(conf.act_type);
        if(control == null) {
            return;
        }
        control.on_war_token_claim_special_reward_c2s(humanObj, cfgId);
    }

    public void on_war_token_buy_level_c2s(HumanObject humanObj, int id, int num) {
        ConfWartoken conf = ConfWartoken.get(id);
        if (conf == null) {
            return;
        }
        ActivityControlWarToken control = (ActivityControlWarToken) ActivityControlTypeFactory.getTypeData(conf.act_type);
        if(control == null) {
            return;
        }
        control.on_war_token_buy_level_c2s(humanObj, num);
    }

    public void on_act_seven_trial_claim_c2s(HumanObject humanObj, int type) {
        if (type == EActivityType.SEVEN_TRIAL_TYPE_1) {
            ActivityControlSevenTrial control = (ActivityControlSevenTrial) ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_56);
            if (control == null) {
                return;
            }
            control.on_act_seven_trial_claim_c2s(humanObj,type);
        } else if (type == EActivityType.SEVEN_TRIAL_TYPE_2) {
            ActivityControlSevenTrial control = (ActivityControlSevenTrial) ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_84);
            if (control == null) {
                return;
            }
            control.on_act_seven_trial_claim_c2s(humanObj,type);
        }
    }

    public void on_act_seven_trial_info_c2s(HumanObject humanObj, int type) {
        if (type == EActivityType.SEVEN_TRIAL_TYPE_1) {
            ActivityControlSevenTrial control = (ActivityControlSevenTrial) ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_56);
            if (control == null) {
                return;
            }
            control.on_act_seven_trial_info_c2s(humanObj, type);
        } else if (type == EActivityType.SEVEN_TRIAL_TYPE_2) {
            ActivityControlSevenTrial control = (ActivityControlSevenTrial) ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_84);
            if (control == null) {
                return;
            }
            control.on_act_seven_trial_info_c2s(humanObj,type);
        }
    }

    public void on_act_task_update_c2s(HumanObject humanObj, int type) {
        AbstractActivityControl control = (AbstractActivityControl) ActivityControlTypeFactory.getTypeData(type);
        if(control == null) {
            return;
        }
        control.on_act_task_update_c2s(humanObj);
    }

    public void on_act_halloween_match_3_info_c2s(HumanObject humanObj, int actType) {
        ActivityControlMiniGameMatch3 control = (ActivityControlMiniGameMatch3) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_halloween_match_3_info_c2s(humanObj);
    }

    public void on_act_halloween_match_3_result_c2s(HumanObject humanObj, int actType, int result, int chapterId, int step) {
        ActivityControlMiniGameMatch3 control = (ActivityControlMiniGameMatch3) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_halloween_match_3_result_c2s(humanObj, result, chapterId, step);
    }

    public void on_act_mini_game_info_c2s(HumanObject humanObj, int actType) {
        ActivityControlMiniGame control = (ActivityControlMiniGame) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_mini_game_info_c2s(humanObj);
    }

    public void on_act_mini_game_start_c2s(HumanObject humanObj, int actType, int chapterId) {
        ActivityControlMiniGame control = (ActivityControlMiniGame) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_mini_game_start_c2s(humanObj, chapterId);
    }

    public void on_act_mini_game_result_c2s(HumanObject humanObj, int actType, int chapterId, int result) {
        ActivityControlMiniGame control = (ActivityControlMiniGame) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_act_mini_game_result_c2s(humanObj, chapterId, result);
    }

    public void giveWartokenExp(HumanObject humanObj, int itemSn, int num) {
        ConfGoods confGoods = ConfGoods.get(itemSn);
        if (confGoods == null || confGoods.type != ItemConstants.战令值){
            Log.game.error("giveWartokenExp:ConfGoods not found. itemSn={}", itemSn);
            return;
        }
        ConfWartoken confWartoken = ConfWartoken.get(confGoods.effect[0][0]);
        if(confWartoken == null){
            Log.game.error("giveWartokenExp:ConfWartoken not found. itemSn={}", itemSn);
            return;
        }
        IActivityControl control = ActivityControlTypeFactory.getTypeData(confWartoken.act_type);
        if(!(control instanceof ActivityControlWarToken)){
            Log.game.error("giveWartokenExp:ActivityControl not found. itemSn={}", itemSn);
            return;
        }
        ActivityControlWarToken actControl = (ActivityControlWarToken)control;
        actControl.addWartokenExp(humanObj, num);
        ActivityManager.inst().addActivityProgress(humanObj, TaskConditionTypeKey.TASK_TYPE_36, num,itemSn, num);
    }

    public void on_act_autumn_pig_info_c2s(HumanObject humanObj, int actType) {
        IActivityControl iControl = ActivityControlTypeFactory.getTypeData(actType);
        if(iControl == null) {
            return;
        }
        if(iControl instanceof ActivityControlFestivalSearch){
            ((ActivityControlFestivalSearch)iControl).on_act_autumn_pig_info_c2s(humanObj);
        } else {
            Log.game.error("on_act_autumn_pig_info_c2s:actType={} is not ActivityControlFestivalSearch", actType);
        }
    }

    @Listener(EventKey.FUNCTION_OPEN)
    public void unlock(Param params) {
        HumanObject humanObj = params.get("humanObj");
        List<Integer> openList = params.get("openList");
        if (humanObj.openActivityFunSnList.containsAll(openList)) {
            return;
        }
        Set<Integer> setActivitySn = new HashSet<>();
        Set<Integer> setFunSn = new HashSet<>();
        for (int activtySn : humanObj.openActivitySnList) {
            ConfActivityControl confActivity = ConfActivityControl.get(activtySn);
            if (confActivity == null) {
                continue;
            }
            if (humanObj.openActivityFunSnList.contains(confActivity.newfunctionID)) {
                continue;
            }
            if (humanObj.isModUnlock(confActivity.newfunctionID)) {
                setActivitySn.add(activtySn);
                setFunSn.add(confActivity.newfunctionID);
            }
        }
        for (int activtySn : humanObj.openHumanActivitySnList) {
            ConfActivityControl confActivity = ConfActivityControl.get(activtySn);
            if (confActivity == null) {
                continue;
            }
            if (humanObj.openActivityFunSnList.contains(confActivity.newfunctionID)) {
                continue;
            }
            if (humanObj.isModUnlock(confActivity.newfunctionID)) {
                setActivitySn.add(activtySn);
                setFunSn.add(confActivity.newfunctionID);
            }
        }
        if (!setFunSn.isEmpty()) {
            humanObj.openActivityFunSnList.addAll(setFunSn);
        }

        if (!setActivitySn.isEmpty()) {
            handleActivityUnlock(humanObj, setActivitySn);
        }
    }

    public void handleActivityUnlock(HumanObject humanObj, Set<Integer> activitySnSet){
        for (int activityId : activitySnSet) {
            if (!humanObj.openActivitySnList.contains(activityId)) {
                continue;
            }
            ConfActivityControl conf = ConfActivityControl.get(activityId);
            if (!humanObj.isModUnlock(conf.newfunctionID)) {
                continue;
            }
            IActivityControl iActivityControl = ActivityControlTypeFactory.getTypeData(conf.type);
            if (iActivityControl != null) {
                iActivityControl.unlockActivity(humanObj);
                MallManager.inst().sendAndAddActivityConditionPayMallInfo(humanObj, activityId);
            }
        }
    }

    /**
     * 移除活动过期道具
     */
    public void recycleActivityItem(HumanObject humanObj, int actSn, ConfActivityTerm confActivityTerm) {
        if(confActivityTerm == null || confActivityTerm.recycle_goods_id == null || confActivityTerm.recycle_goods_id.length < 1){
            return;
        }

        JSONObject itemJSON = new JSONObject();
        for (int i = 0; i < confActivityTerm.recycle_goods_id.length; ++i){
            ConfGoods confGoods = ConfGoods.get(confActivityTerm.recycle_goods_id[i]);
            if (confGoods == null || confGoods.recycle_price == null || confGoods.recycle_price.length <= 1) {
                continue;
            }
            int recycleSn = confGoods.recycle_price[0];
            int recycleNum = confGoods.recycle_price[1];
            int hasNum = ItemManager.inst().getItemNum(humanObj, confGoods.sn);
            if(hasNum <= 0){
                continue;
            }
            if(ProduceManager.inst().checkAndCostItem(humanObj, confGoods.sn, hasNum, MoneyItemLogKey.回收).success){
                int num = itemJSON.getIntValue(Integer.toString(recycleSn));
                itemJSON.put(Integer.toString(recycleSn), num + hasNum * recycleNum);
            }
        }
        if(!itemJSON.isEmpty()){
            JSONObject jo = new JSONObject();
            JSONObject joTemp1 = new JSONObject();
            joTemp1.put(MailManager.MAIL_K_8, actSn);
            jo.put(MailManager.MAIL_PARAM_1, joTemp1);
            MailManager.inst().sendMail(humanObj.id, MailManager.SYS_SENDER, 10117, "", jo.toJSONString(),itemJSON.toJSONString(), new Param());
        }
    }

    public void on_act_tanabata_flower_history_c2s(HumanObject humanObj, int actType, int type, int page) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlTanabataFlower iControl = (ActivityControlTanabataFlower) ActivityControlTypeFactory.getTypeData(actType);
        if (iControl == null) {
            return;
        }
        iControl.on_act_tanabata_flower_history_c2s(humanObj, type);
    }

    public void on_act_valentine_role_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlTanabataFlower iControl = (ActivityControlTanabataFlower) ActivityControlTypeFactory.getTypeData(actType);
        if (iControl == null) {
            return;
        }
        iControl.on_act_valentine_role_info_c2s(humanObj);
    }

    public void on_act_tanabata_flower_search_c2s(HumanObject humanObj, int actType, String name) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        long now = Port.getTime();
        if (now - humanObj.lastOpTime < Time.SEC * 3) {
            humanObj.sendMsg(MsgError.error_info_s2c.newBuilder().setCode(19));
            return;
        }
        humanObj.lastOpTime = now;
        ActivityControlTanabataFlower iControl = (ActivityControlTanabataFlower) ActivityControlTypeFactory.getTypeData(actType);
        if (iControl == null) {
            return;
        }
        iControl.on_act_tanabata_flower_search_c2s(humanObj, name);
    }

    public void on_act_tanabata_give_flower_c2s(HumanObject humanObj, int actType, long roleId, int flowerId, int num) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlTanabataFlower iControl = (ActivityControlTanabataFlower) ActivityControlTypeFactory.getTypeData(actType);
        if (iControl == null) {
            return;
        }
        iControl.on_act_tanabata_give_flower_c2s(humanObj, roleId, flowerId, num);
    }

    public void receiveFlower(HumanObject humanObj, int actType, long roleId, int flowerSn, int num) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlTanabataFlower iControl = (ActivityControlTanabataFlower) ActivityControlTypeFactory.getTypeData(actType);
        if (iControl == null) {
            return;
        }
        iControl.on_receive_flower(humanObj, roleId, flowerSn, num);
    }

    @Listener(value = EventKey.POCKET_LINE_HANDLE_ONE, subStr = PocketLineEventSubKey.RECEIVE_FLOWER)
    public void pocketLine_RECEIVE_FLOWER(Param param) {
        HumanObject humanObj = param.get("humanObj");
        PocketLine p = param.get("pocketLine");
        JSONObject jo = Utils.toJSONObject(p.getParam());
        int receiveTime = (int)(p.getTimeCreate() / Time.SEC);
        long roleId = jo.getLongValue("roleId");
        int flowerId = jo.getIntValue("flowerId");
        int num = jo.getIntValue("num");
        int actType = jo.getIntValue("actType");
        if (isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlTanabataFlower iControl = (ActivityControlTanabataFlower) ActivityControlTypeFactory.getTypeData(actType);
        if (iControl == null) {
            return;
        }
        ActivityControlObjectData activityData = humanObj.operation.activityControlData.getControlObjectData(actType);
        if (activityData == null) {
            return;
        }
        iControl.addReceiveHistory(activityData, roleId, flowerId, num, receiveTime);
        HumanManager.inst().updateSystemModuleRedNum(humanObj, RedPointConstants.QixiRecord, RedPointConstants.Module2, 0);
    }

    public void on_act_seven_trial_angry_bird_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlMiniGameSevenTrial control = (ActivityControlMiniGameSevenTrial) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_seven_trial_angry_bird_info_c2s(humanObj);
    }

    public void on_act_seven_trial_angry_bird_result_c2s(HumanObject humanObj, int actType, int result, int chapterId, int step) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlMiniGameSevenTrial control = (ActivityControlMiniGameSevenTrial) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_seven_trial_angry_bird_result_c2s(humanObj, result, chapterId, step);
    }

    // region 圣诞献礼相关代码
    public void on_act_halloween_arena_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlPVPClue control = (ActivityControlPVPClue) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_halloween_arena_info_c2s(humanObj);
    }

    public void on_act_halloween_arena_trick_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlPVPClue control = (ActivityControlPVPClue) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_halloween_arena_trick_c2s(humanObj);
    }

    public void on_act_halloween_arena_combat_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlPVPClue control = (ActivityControlPVPClue) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_halloween_arena_combat_c2s(humanObj);
    }

    public void on_act_halloween_arena_result_c2s(HumanObject humanObj, int actType, int result) {
         if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlPVPClue control = (ActivityControlPVPClue) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_halloween_arena_result_c2s(humanObj, result);
    }

    public void on_act_halloween_arena_buy_buff_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlPVPClue control = (ActivityControlPVPClue) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_halloween_arena_buy_buff_c2s(humanObj);
    }

    public void on_act_halloween_arena_choose_buff_c2s(HumanObject humanObj, int actType, int skillSn) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlPVPClue control = (ActivityControlPVPClue) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_halloween_arena_choose_buff_c2s(humanObj, skillSn);
    }

    public void on_act_halloween_arena_buy_pass_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlPVPClue control = (ActivityControlPVPClue) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_halloween_arena_buy_pass_c2s(humanObj);
    }
    // endregion 圣诞献礼相关代码

    public void on_act_box_tower_open_ten_c2s(HumanObject humanObj, int actType) {

        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlBoxTower control = (ActivityControlBoxTower) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null){
            return;
        }
        control.on_act_box_tower_open_ten_c2s(humanObj);
    }

    public void on_act_box_tower_open_c2s(HumanObject humanObj, int actType, int choose) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlBoxTower control = (ActivityControlBoxTower) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null){
            return;
        }
        control.on_act_box_tower_open_c2s(humanObj, choose);
    }

    public void on_act_box_tower_next_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlBoxTower control = (ActivityControlBoxTower) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null){
            return;
        }
        control.on_act_box_tower_next_c2s(humanObj);
    }

    public void on_act_box_tower_auto_next_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlBoxTower control = (ActivityControlBoxTower) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null){
            return;
        }
        control.on_act_box_tower_auto_next_c2s(humanObj);
    }

    public void on_act_box_tower_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlBoxTower control = (ActivityControlBoxTower) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null){
            return;
        }
        control.on_act_box_tower_info_c2s(humanObj);
    }

    public void on_act_box_tower_open_fifty_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlBoxTower control = (ActivityControlBoxTower) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null){
            return;
        }
        control.on_act_box_tower_open_fifty_c2s(humanObj);
    }

    // region 打地鼠相关代码
    public void on_act_tang_mole_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlMiniGameWhackMole control = (ActivityControlMiniGameWhackMole) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_tang_mole_info_c2s(humanObj);
    }

    public void on_act_tang_mole_choose_hammer_c2s(HumanObject humanObj, int actType, int hammerSn) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlMiniGameWhackMole control = (ActivityControlMiniGameWhackMole) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_tang_mole_choose_hammer_c2s(humanObj, hammerSn);
    }

    public void on_act_tang_mole_start_c2s(HumanObject humanObj, int actType, int chapterId) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlMiniGameWhackMole control = (ActivityControlMiniGameWhackMole) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_tang_mole_start_c2s(humanObj, chapterId);
    }

    public void on_act_tang_mole_result_c2s(HumanObject humanObj, int actType, int chapterId, int result, int restHp,
                                            List<Define.p_key_value> killList, int skillCount) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlMiniGameWhackMole control = (ActivityControlMiniGameWhackMole) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_tang_mole_result_c2s(humanObj, chapterId, result, restHp, killList, skillCount);
    }
    // endregion 打地鼠相关代码

    // region 全民赶年兽相关代码
    public void on_act_pillow_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlBoss control = (ActivityControlBoss) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_pillow_info_c2s(humanObj);
    }

    public void on_act_pillow_throw_c2s(HumanObject humanObj, int actType, int times) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlBoss control = (ActivityControlBoss) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_pillow_throw_c2s(humanObj, times);
    }

    /**
     * 公会充值活动信息请求
     */
    public void on_act_guild_pay_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlGuildPay control = (ActivityControlGuildPay) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_guild_pay_info_c2s(humanObj);
    }

    /**
     * 公会充值活动奖励领取请求
     */
    public void on_act_guild_pay_get_reward_c2s(HumanObject humanObj, int actType, int rewardId) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlGuildPay control = (ActivityControlGuildPay) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_guild_pay_get_reward_c2s(humanObj, rewardId);
    }

    public void on_act_pillow_take_c2s(HumanObject humanObj, int actType, int type, int id) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlBoss control = (ActivityControlBoss) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_pillow_take_c2s(humanObj, type, id);
    }
    // endregion 全民赶年兽相关代码

    // region 超值卡
    public void on_act_week_card_info_c2s(HumanObject humanObj) {
        ActivityControlWeekCard control = (ActivityControlWeekCard) ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_2003);
        if (control == null) {
            return;
        }
        control.on_act_week_card_info_c2s(humanObj);
    }

    public void on_act_week_card_reward_c2s(HumanObject humanObj, int sn) {
        ActivityControlWeekCard control = (ActivityControlWeekCard) ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_2003);
        if (control == null) {
            return;
        }
        control.on_act_week_card_reward_c2s(humanObj, sn);
    }
    // endregion 超值卡

    //region 打豆豆
    public void on_act_share_game_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlShareGame control = (ActivityControlShareGame) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_share_game_info_c2s(humanObj);
    }

    public void on_act_share_game_result_c2s(HumanObject humanObj, int actType, int score) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlShareGame control = (ActivityControlShareGame) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_share_game_result_c2s(humanObj, score);
    }
    // endregion 打豆豆

    // region 黄金塔
    public void on_act_golden_tower_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlGoldenTower control = (ActivityControlGoldenTower) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_golden_tower_info_c2s(humanObj);
    }

    public void on_act_golden_tower_draw_c2s(HumanObject humanObj, int actType, int drawType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlGoldenTower control = (ActivityControlGoldenTower) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_golden_tower_draw_c2s(humanObj, drawType);
    }

    public void on_act_golden_tower_choose_c2s(HumanObject humanObj, int actType, int rewardId, int replaceId) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlGoldenTower control = (ActivityControlGoldenTower) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_golden_tower_choose_c2s(humanObj, rewardId, replaceId);
    }

    public void on_act_golden_tower_count_reward_c2s(HumanObject humanObj, int actType, int rewardId) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlGoldenTower control = (ActivityControlGoldenTower) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_golden_tower_count_reward_c2s(humanObj, rewardId);
    }
    // endregion 黄金塔

    // region 全服情人节浇花
    public void on_act_cross_boss_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlWaterTree control = (ActivityControlWaterTree) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_cross_boss_info_c2s(humanObj);
    }

    public void on_act_cross_boss_throw_c2s(HumanObject humanObj, int actType, int num) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlWaterTree control = (ActivityControlWaterTree) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_cross_boss_throw_c2s(humanObj, num);
    }

    public void on_act_cross_boss_claim_c2s(HumanObject humanObj, int actType, int sn) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlWaterTree control = (ActivityControlWaterTree) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_cross_boss_claim_c2s(humanObj, sn);
    }

    public void on_act_cross_boss_buy_c2s(HumanObject humanObj, int actType, int sn) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlWaterTree control = (ActivityControlWaterTree) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_cross_boss_buy_c2s(humanObj, sn);
    }
    // endregion 全服情人节浇花

    // region 限时礼盒
    public void on_act_cross_boss_claim_box_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlLuckyBag control = (ActivityControlLuckyBag) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_cross_boss_claim_box_info_c2s(humanObj);
    }

    public void on_act_cross_boss_claim_box_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlLuckyBag control = (ActivityControlLuckyBag) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_cross_boss_claim_box_c2s(humanObj);
    }
    // endregion 限时礼盒

    public void on_day_pay_reward(HumanObject humanObj, int actType, int index) {
        ActivityControlDayPay control = (ActivityControlDayPay) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.getReward(humanObj, index);
    }

    public void on_day_pay_choose_reward(HumanObject humanObj, int actType, int index) {
        ActivityControlDayPay control = (ActivityControlDayPay) ActivityControlTypeFactory.getTypeData(actType);
        if(control == null) {
            return;
        }
        control.on_day_pay_choose_reward(humanObj, index);
    }

    // region 军团入侵
    public void on_act_legion_info_c2s(HumanObject humanObj, int actType) {
//        if (!isActivityOpen(humanObj, actType)) {
//            return;
//        }
        ActivityControlLegionInvasion control = (ActivityControlLegionInvasion) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_legion_info_c2s(humanObj);
    }

    public void on_act_legion_buff_active_c2s(HumanObject humanObj, int actType, int buffId) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlLegionInvasion control = (ActivityControlLegionInvasion) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_legion_buff_active_c2s(humanObj, buffId);
    }


    public void on_act_legion_merge_c2s(HumanObject humanObj, int actType, List<Define.p_key_value> monsterNumList, List<Define.p_key_value> gridList, int step) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlLegionInvasion control = (ActivityControlLegionInvasion) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_legion_merge_c2s(humanObj, monsterNumList, gridList, step);
    }

    public void on_act_legion_invasion_c2s(HumanObject humanObj, int actType, int gearIndex) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlLegionInvasion control = (ActivityControlLegionInvasion) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_legion_invasion_c2s(humanObj, gearIndex);
    }

    public void on_act_legion_collection_reward_c2s(HumanObject humanObj, int actType, int monsterId) {
//        if (!isActivityOpen(humanObj, actType)) {
//            return;
//        }
        ActivityControlLegionInvasion control = (ActivityControlLegionInvasion) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_legion_collection_reward_c2s(humanObj, monsterId);
    }

    public void on_act_legion_energy_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlLegionInvasion control = (ActivityControlLegionInvasion) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_legion_energy_c2s(humanObj);
    }

    public void on_act_legion_buff_use_c2s(HumanObject humanObj, int actType, int buffId, int num) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlLegionInvasion control = (ActivityControlLegionInvasion) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_legion_buff_use_c2s(humanObj, buffId, num);
    }
    // endregion 军团入侵

    //大富翁
    /**
     * 活动大富翁信息C2S消息
     * @param actType 活动类型
     */
    public void on_act_monopoly_info_c2s(HumanObject humanObj, int actType) {
        ActivityControlMonopoly control = (ActivityControlMonopoly) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_monopoly_info_c2s(humanObj);
    }

    /**
     * 活动大富翁掷骰子C2S消息
     * @param actType 活动类型
     */
    public void on_act_monopoly_dice_c2s(HumanObject humanObj, int actType, int opType, int diceNum) {
        ActivityControlMonopoly control = (ActivityControlMonopoly) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_monopoly_dice_c2s(humanObj, opType, diceNum);
    }

    /**
     * 活动大富翁双倍C2S消息
     * @param actType 活动类型
     */
    public void on_act_monopoly_double_c2s(HumanObject humanObj, int actType) {
    }

    /**
     * 活动大富翁圈数奖励C2S消息
     * @param actType  活动类型
     * @param rewardId 奖励ID
     */
    public void on_act_monopoly_circle_reward_c2s(HumanObject humanObj, int actType, int rewardId) {
        ActivityControlMonopoly control = (ActivityControlMonopoly) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_monopoly_circle_reward_c2s(humanObj, rewardId);
    }
    //大富翁

    /**
     * 获取卡皮巴拉信息C2S消息处理
     * @param actType 活动类型
     */
    public void on_act_slime_info_c2s(HumanObject humanObj, int actType) {
        ActivityControlSlime control = (ActivityControlSlime) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_slime_info_c2s(humanObj);
    }

    /**
     * 进入关卡C2S消息处理
     * @param actType 活动类型
     * @param stageId 关卡ID
     */
    public void on_act_slime_enter_stage_c2s(HumanObject humanObj, int actType, int stageId) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlSlime control = (ActivityControlSlime) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_slime_enter_stage_c2s(humanObj, stageId);
    }

    /**
     * 结束关卡C2S消息处理
     * @param actType 活动类型
     * @param stageId 关卡ID
     * @param result 结果(0失败 1成功)
     */
    public void on_act_slime_finish_stage_c2s(HumanObject humanObj, int actType, int stageId, int result) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlSlime control = (ActivityControlSlime) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_slime_finish_stage_c2s(humanObj, stageId, result);
    }

    /**
     * 关卡进度上报C2S消息处理
     * @param actType 活动类型
     * @param eventInfo 每日信息
     */
    public void on_act_slime_stage_progress_c2s(HumanObject humanObj, int actType, Define.p_slime_event eventInfo) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlSlime control = (ActivityControlSlime) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_slime_stage_progress_c2s(humanObj, eventInfo);
    }

    /**
     * 天赋升级C2S消息处理
     * @param actType 活动类型
     * @param attrType 属性类型
     */
    public void on_act_slime_talent_upgrade_c2s(HumanObject humanObj, int actType, int attrType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlSlime control = (ActivityControlSlime) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_slime_talent_upgrade_c2s(humanObj, attrType);
    }

    /**
     * 大吉转盘抽奖C2S消息处理
     * @param actType 活动类型
     */
    public void on_act_slime_wheel_draw_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlSlime control = (ActivityControlSlime) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_slime_wheel_draw_c2s(humanObj);
    }

    /**
     * 体力值刷新C2S消息处理
     * @param actType 活动类型
     */
    public void on_act_slime_refresh_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlSlime control = (ActivityControlSlime) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_slime_refresh_c2s(humanObj);
    }

    public void on_dungeon_battle_start_c2s(HumanObject humanObj, int type, int level) {
        if(type == InstanceConstants.ACTIVITY_CHAPTER_SLIME_120){
            ActivityControlSlime control = (ActivityControlSlime) ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_4300);
            if (control == null) {
                Log.game.error("on_dungeon_battle_start_c2s:control is null");
                return;
            }
            control.on_dungeon_battle_start_c2s(humanObj, type, level);
        }
    }

    public void on_dungeon_battle_result_c2s(HumanObject humanObj, int type, int dungeonId, int result, List<Define.p_key_value> argsList) {
        if(type == InstanceConstants.ACTIVITY_CHAPTER_SLIME_120){
            ActivityControlSlime control = (ActivityControlSlime) ActivityControlTypeFactory.getTypeData(ActivityControlType.Act_4300);
            if (control == null) {
                Log.game.error("on_dungeon_battle_result_c2s:control is null");
                return;
            }
            control.on_dungeon_battle_result_c2s(humanObj, type, dungeonId, result, argsList);
        }
    }

    public void itemAdd(HumanObject humanObj, List<ProduceVo> voList) {
        if (voList == null || voList.isEmpty()) {
            return;
        }
        for (ProduceVo vo : voList) {
            if (vo == null) {
                continue;
            }
            ConfGoods confGoods = ConfGoods.get(vo.itemSn);
            if (confGoods == null) {
                Log.activity.error("使用了活动道具, 但找不到对应的配置, humanId={}, sn={}", humanObj.getHuman().getId(), vo.itemSn);
                continue;
            }

            for (int i = 0; i < confGoods.effect.length; ++i) {
                int actType = confGoods.effect[i][0];
                if (ActivityManager.inst().isActivityOpen(humanObj, actType)) {
                    IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
                    if (control == null) {
                        Log.activity.error("使用了活动道具, 但找不到对应的处理类, humanId={}, sn={}, actType={}", humanObj.getHuman().getId(), vo.itemSn, actType);
                        return;
                    }
                    control.itemAdd(humanObj, vo.itemSn, vo.num);
                    return;
                }
            }
            Log.activity.error("使用了活动道具, 但没有找到对应的活动类型, humanId={}, sn={}", humanObj.getHuman().getId(), vo.itemSn);
        }
    }

    public void on_act_slime_skill_refine_c2s(HumanObject humanObj, int actType, int pos) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlSlime control = (ActivityControlSlime) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_slime_skill_refine_c2s(humanObj, pos);
    }

    public void on_act_slime_skill_choose_c2s(HumanObject humanObj, int actType, int pos, int isReplace) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlSlime control = (ActivityControlSlime) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_slime_skill_choose_c2s(humanObj, pos, isReplace);
    }
    /**
     * 卡皮巴拉扫荡C2S消息处理
     * @param actType 活动类型
     */
    public void on_act_slime_sweep_c2s(HumanObject humanObj, int actType, int stageId, int count, List<Define.p_key_value> itemList, int bigLuckNum) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlSlime control = (ActivityControlSlime) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_slime_sweep_c2s(humanObj, stageId, count, itemList, bigLuckNum);
    }

    //融合
    public void on_act_slime_mix_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlSlimeMix control = (ActivityControlSlimeMix) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_slime_mix_info_c2s(humanObj);
    }

    public void on_act_slime_mix_c2s(HumanObject humanObj, int actType, int itemSn) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlSlimeMix control = (ActivityControlSlimeMix) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_slime_mix_c2s(humanObj, itemSn);
    }
    //融合

    //大闹魔王城
    public void on_act_castle_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpenOrShow(humanObj, actType)) {
            return;
        }
        ActivityControlCastle control = (ActivityControlCastle) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_castle_info_c2s(humanObj);
    }

    public void act_castle_gamble_c2s(HumanObject humanObj, int actType, int isTen) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlCastle control = (ActivityControlCastle) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_castle_gamble_c2s(humanObj, isTen);
    }

    public void on_act_castle_element_reward_c2s(HumanObject humanObj, int actType, int elementType, int pos) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlCastle control = (ActivityControlCastle) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_castle_element_reward_c2s(humanObj, elementType, pos);
    }

    public void act_stamina_refresh_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        if(control instanceof ActivityControlCastle) {
            ((ActivityControlCastle) control).on_act_stamina_refresh_c2s(humanObj);
        }else if(control instanceof ActivityControlCardEliminate) {
            ((ActivityControlCardEliminate) control).on_act_stamina_refresh_c2s(humanObj);
        }else if(control instanceof ActivityControlFruitMerge) {
            ((ActivityControlFruitMerge) control).on_act_stamina_refresh_c2s(humanObj);
        }
    }

    public void on_act_castle_move_c2s(HumanObject humanObj, int actType, int layer, int pos) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlCastle control = (ActivityControlCastle) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_castle_move_c2s(humanObj, layer, pos);
    }

    public void on_act_castle_race_op_c2s(HumanObject humanObj, int actType, int op) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlCastle control = (ActivityControlCastle) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_castle_race_op_c2s(humanObj, op);
    }

    public void on_act_castle_race_result_c2s(HumanObject humanObj, int actType, int isWin) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlCastle control = (ActivityControlCastle) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_castle_race_result_c2s(humanObj, isWin);
    }

    //大闹魔王城

    //装备打造
    public void on_act_dungeon_craft_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpenOrShow(humanObj, actType)) {
            return;
        }
        ActivityControlDungeonCraft control = (ActivityControlDungeonCraft) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_dungeon_craft_info_c2s(humanObj);
    }

    public void on_act_dungeon_craft_c2s(HumanObject humanObj, int actType, int craftType, int num) {
        if (!isActivityOpenOrShow(humanObj, actType)) {
            return;
        }
        ActivityControlDungeonCraft control = (ActivityControlDungeonCraft) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_dungeon_craft_c2s(humanObj, craftType, num);
    }
    //装备打造

    public void on_act_refresh_gift_info_c2s(HumanObject humanObj, int actType) {
        ActivityControlRefreshGift control = (ActivityControlRefreshGift) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        if (!ActivityManager.inst().isActivityOpen(humanObj, actType)) {
            return;
        }
        control.on_act_refresh_gift_info_c2s(humanObj);
    }

    public void on_act_refresh_gift_refresh_c2s(HumanObject humanObj, int actType, int paySn) {
        ActivityControlRefreshGift control = (ActivityControlRefreshGift) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        if (!ActivityManager.inst().isActivityOpen(humanObj, actType)) {
            return;
        }
        control.on_act_refresh_gift_refresh_c2s(humanObj, paySn);
    }

    public void on_act_refresh_gift_reward_c2s(HumanObject humanObj, int actType, int paySn, int rewardIndex) {
        ActivityControlRefreshGift control = (ActivityControlRefreshGift) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        if (!ActivityManager.inst().isActivityOpen(humanObj, actType)) {
            return;
        }
        control.on_act_refresh_gift_reward_c2s(humanObj, paySn, rewardIndex);
    }

    /**
     * 活动日历信息
     * @param humanObj
     */
    public void sendMsg_calendar_info_s2c(HumanObject humanObj) {
        MsgAct.act_calendar_info_s2c.Builder msg = MsgAct.act_calendar_info_s2c.newBuilder();
        HumanDailyResetInfo freeInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyActCalendarFreeReceiveNum.getType());
        msg.setFreeState(freeInfo.getValue() > 0 ? EActivityType.CALENDAR_FREE_STATE_RECEIVED : EActivityType.CALENDAR_FREE_STATE_CAN_BE_CLAIMED);
        humanObj.sendMsg(msg);
//        Log.activity.info("===活动日历信息 humanId={} msg={}", humanObj.id, msg);
    }

    /**
     * 领取活动日历每日免费礼包
     * @param humanObj
     */
    public void _msg_act_calendar_reward_c2s(HumanObject humanObj) {
        HumanDailyResetInfo freeInfo = humanObj.getDailyResetInfo(DailyResetTypeKey.dailyActCalendarFreeReceiveNum.getType());
        if(freeInfo.getValue() > 0){
            Log.activity.warn("===今日已领取活动日历每日免费礼包 humanId={}", humanObj.id);
            return;
        }
        ConfGlobal conf = ConfGlobal.get(ConfGlobalKey.Activity_Calendar_reward);
        if(conf == null || conf.intArray == null || conf.intArray.length == 0){
            Log.activity.warn("===领取活动日历每日免费礼包 配置不存在");
            return;
        }
        // 给奖励
        int[] rewards = conf.intArray;
        ProduceManager.inst().produceAdd(humanObj, rewards, MoneyItemLogKey.活动日历每日免费礼包);
        InstanceManager.inst().sendMsg_goods_show_s2c(humanObj, InstanceConstants.showType_0, rewards);
        // 标记已领取
        freeInfo.setValue(1);
        humanObj.saveDailyResetRecord();

        MsgAct.act_calendar_reward_s2c.Builder msg = MsgAct.act_calendar_reward_s2c.newBuilder();
        msg.setFreeState(EActivityType.CALENDAR_FREE_STATE_RECEIVED);
        humanObj.sendMsg(msg);
//        Log.activity.info("===领取活动日历每日免费礼包 humanId={}", humanObj.id);
    }

    public ControlDungeonData getDungeonData(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return null;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return null;
        }
        return control.getControlData(humanObj, ControlDungeonData.class);
    }

    /**
     * 转生魔剑信息总览
     * @param humanObj
     * @param actType
     */
    public void _msg_act_dungeon_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_info_c2s(humanObj);
    }

    /**
     * 转生魔剑培养
     * @param humanObj
     * @param actType
     * @param attrType
     */
    public void _msg_act_dungeon_cultivate_c2s(HumanObject humanObj, int actType, int attrType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_cultivate_c2s(humanObj, attrType);
    }

    /**
     * 转生魔剑锻造
     * @param humanObj
     * @param actType
     * @param type
     */
    public void _msg_act_dungeon_forge_c2s(HumanObject humanObj, int actType, int type) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_forge_c2s(humanObj, type);
    }

    /**
     * 转生魔剑装备升级
     * @param humanObj
     * @param actType
     * @param equipId
     * @param costCrystal
     */
    public void _msg_act_dungeon_equip_upgrade_c2s(HumanObject humanObj, int actType, int equipId, int costCrystal) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_equip_upgrade_c2s(humanObj, equipId, costCrystal);
    }

    /**
     * 转生魔剑消耗锻造进度领取矿石
     * @param humanObj
     * @param actType
     */
    public void _msg_act_dungeon_receive_ore_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_receive_ore_c2s(humanObj);
    }

    /**
     * 转生魔剑烹饪
     * @param humanObj
     * @param actType
     * @param foodId
     * @param foodNum
     */
    public void _msg_act_dungeon_cook_c2s(HumanObject humanObj, int actType, int foodId, int foodNum) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_cook_c2s(humanObj, foodId, foodNum);
    }

    /**
     * 转生魔剑扫荡
     * @param humanObj
     * @param actType
     * @param chapterId
     * @param sweepNum
     */
    public void _msg_act_dungeon_sweep_c2s(HumanObject humanObj, int actType, int chapterId, int sweepNum) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_sweep_c2s(humanObj, chapterId, sweepNum);
    }

    /**
     * 转生魔剑进入场景
     * @param humanObj
     * @param actType
     * @param chapterId
     * @param carryInfo
     */
    public void _msg_act_dungeon_enter_scene_c2s(HumanObject humanObj, int actType, int chapterId, List<Integer> carryInfo) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_enter_scene_c2s(humanObj, chapterId, carryInfo);
    }

    /**
     * 转生魔剑探索
     * @param humanObj
     * @param actType
     * @param chapterId
     * @param targetPos
     * @param sourceCavePos
     */
    public void _msg_act_dungeon_explore_c2s(HumanObject humanObj, int actType, int chapterId, int targetPos, int sourceCavePos) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_explore_c2s(humanObj, chapterId, targetPos, sourceCavePos);
    }

    /**
     * 转生魔剑触发事件
     * @param humanObj
     * @param actType
     * @param chapterId
     * @param targetPos
     * @param param
     * @param sourceCavePos
     */
    public void _msg_act_dungeon_trigger_event_c2s(HumanObject humanObj, int actType, int chapterId, int targetPos,
                                                   Define.p_dungeon_event_param param, int sourceCavePos) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_trigger_event_c2s(humanObj, chapterId, targetPos, param, sourceCavePos);
    }

    /**
     * 转生魔剑战斗
     * @param humanObj
     * @param actType
     * @param chapterId
     * @param targetPos
     * @param sourceCavePos
     */
    public void _msg_act_dungeon_battle_c2s(HumanObject humanObj, int actType, int chapterId, int targetPos, int sourceCavePos) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_battle_c2s(humanObj, chapterId, targetPos, sourceCavePos);
    }

    /**
     * 转生魔剑使用物品
     * @param humanObj
     * @param actType
     * @param chapterId
     * @param itemIndex
     * @param targetPos
     */
    public void _msg_act_dungeon_use_item_c2s(HumanObject humanObj, int actType, int chapterId, int itemIndex, int targetPos, int sourceCavePos) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_use_item_c2s(humanObj, chapterId, itemIndex, targetPos, sourceCavePos);
    }

    /**
     * 转生魔剑吞噬魔石增益信息
     * @param humanObj
     * @param actType
     * @param chapterId
     */
    public void _msg_act_dungeon_devour_stone_info_c2s(HumanObject humanObj, int actType, int chapterId) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_devour_stone_info_c2s(humanObj, chapterId);
    }

    /**
     * 转生魔剑吞噬魔石操作
     * @param humanObj
     * @param actType
     * @param chapterId
     * @param type
     * @param chooseIndex
     */
    public void _msg_act_dungeon_devour_stone_op_c2s(HumanObject humanObj, int actType, int chapterId, int type, int chooseIndex) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_devour_stone_op_c2s(humanObj, chapterId, type, chooseIndex);
    }

    /**
     * 转生魔剑主动结算
     * @param humanObj
     * @param actType
     * @param chapterId
     */
    public void _msg_act_dungeon_settle_c2s(HumanObject humanObj, int actType, int chapterId) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDungeon control = (ActivityControlDungeon) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control._msg_act_dungeon_settle_c2s(humanObj, chapterId);
    }

	 // region 阵营对抗
    public void on_act_camp_info_c2s(HumanObject humanObj, int actType) {
        ActivityControlCamp control = (ActivityControlCamp) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_camp_info_c2s(humanObj);
    }

    public void on_act_camp_join_c2s(HumanObject humanObj, int actType, int campId) {
        ActivityControlCamp control = (ActivityControlCamp) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        control.on_act_camp_join_c2s(humanObj, campId);
    }

    // region 合成大西瓜活动
    /**
     * 合成大西瓜活动信息
     */
    public void on_act_fruit_merge_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpenOrShow(humanObj, actType)) {
            return;
        }
        ActivityControlFruitMerge control = (ActivityControlFruitMerge) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_fruit_merge_info_c2s(humanObj);
    }

    /**
     * 合成大西瓜结束游戏
     */
    public void on_act_fruit_merge_end_c2s(HumanObject humanObj, int actType, int currScore) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlFruitMerge control = (ActivityControlFruitMerge) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_fruit_merge_end_c2s(humanObj, currScore);
    }

    /**
     * 合成大西瓜消耗体力
     */
    public void on_act_fruit_merge_stamina_cost_c2s(HumanObject humanObj, int actType, int totalCost) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlFruitMerge control = (ActivityControlFruitMerge) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_fruit_merge_stamina_cost_c2s(humanObj, totalCost);
    }

    /**
     * 合成大西瓜使用道具
     */
    public void on_act_fruit_merge_use_item_c2s(HumanObject humanObj, int actType, int itemSn) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlFruitMerge control = (ActivityControlFruitMerge) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_fruit_merge_use_item_c2s(humanObj, itemSn);
    }

    /**
     * 合成大西瓜上报状态
     */
    public void on_act_fruit_merge_state_c2s(HumanObject humanObj, int actType, List<Integer> mergeFruit,
                                           Define.p_fruit_merge_state state) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlFruitMerge control = (ActivityControlFruitMerge) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_fruit_merge_state_c2s(humanObj, mergeFruit, state);
    }

    /**
     * 合成大西瓜扫荡
     */
    public void on_act_fruit_sweep_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlFruitMerge control = (ActivityControlFruitMerge) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_fruit_sweep_c2s(humanObj);
    }
    // endregion

    public void on_act_camp_support_c2s(HumanObject humanObj, int actType, int cnt) {
        ActivityControlCamp control = (ActivityControlCamp) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        control.on_act_camp_support_c2s(humanObj, cnt);
    }

    public void on_act_camp_reward_c2s(HumanObject humanObj, int actType, int rewardId) {
        ActivityControlCamp control = (ActivityControlCamp) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_camp_reward_c2s(humanObj, rewardId);
    }
    // endregion 阵营对抗

    @Listener(EventKey.FINISH_TASK)
    public void _listener_Finish_Task(Param param) {
        HumanObject humanObj = param.get("humanObj");
        int taskSn = Utils.getParamValue(param, "taskSn", 0);
        handleActivitySkinTry(humanObj, taskSn);
    }

    // region 皮肤试用
    public void handleActivitySkinTry(HumanObject humanObj, int taskSn) {
        if (taskSn == 0) {
            return;
        }
        for (int actType : ActivityControlTypeFactory.skinTrySet) {
            if (!isActivityOpen(humanObj, actType)) {
                continue;
            }
            ActivityControlSkinTry control = (ActivityControlSkinTry) ActivityControlTypeFactory.getTypeData(actType);
            control.checkSkinTryTask(humanObj, taskSn);
        }
    }

    public void on_act_skin_try_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlSkinTry control = (ActivityControlSkinTry) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_skin_try_info_c2s(humanObj);
    }

    public void on_act_skin_try_use_c2s(HumanObject humanObj, int actType, int use) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlSkinTry control = (ActivityControlSkinTry) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_skin_try_use_c2s(humanObj, use);
    }
    // endregion 皮肤试用

    // region 翻牌活动
    public void on_act_card_eliminate_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpenOrShow(humanObj, actType)) {
            return;
        }
        ActivityControlCardEliminate control = (ActivityControlCardEliminate) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_card_eliminate_info_c2s(humanObj);
    }

    public void on_act_card_eliminate_start_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlCardEliminate control = (ActivityControlCardEliminate) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_card_eliminate_start_c2s(humanObj);
    }

    public void on_act_card_eliminate_flip_c2s(HumanObject humanObj, int actType, int pos) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlCardEliminate control = (ActivityControlCardEliminate) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_card_eliminate_flip_c2s(humanObj, pos);
    }

    public void on_act_card_eliminate_round_complete_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlCardEliminate control = (ActivityControlCardEliminate) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_card_eliminate_round_complete_c2s(humanObj);
    }

    public void on_act_card_eliminate_select_buff_c2s(HumanObject humanObj, int actType, int buffId) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlCardEliminate control = (ActivityControlCardEliminate) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_card_eliminate_select_buff_c2s(humanObj, buffId);
    }

    public void on_act_card_eliminate_game_over_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlCardEliminate control = (ActivityControlCardEliminate) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_card_eliminate_game_over_c2s(humanObj);
    }
    public void on_act_card_buff_refresh_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlCardEliminate control = (ActivityControlCardEliminate) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_card_buff_refresh_c2s(humanObj);
    }
    // endregion 翻牌活动

    // region 团购礼包活动
    public void sendAllMsg_act_group_gift_info_s2c(long humanId, List<GroupGift> giftList) {
        int pageNum = 50;// 每50个礼包变动发一个消息
        int pageCount = giftList.size() / pageNum;
        if (giftList.size() % pageNum != 0) {
            pageCount++;
        }
        for (int i = 0; i < pageCount; i++) {
            int startIndex = i * pageNum;
            int finalIndex = Math.min(startIndex + pageNum, giftList.size());
            MsgAct.act_group_gift_s2c.Builder msg = MsgAct.act_group_gift_s2c.newBuilder();
            msg.setActType(giftList.get(startIndex).getActType());
            for (int j = startIndex; j < finalIndex; j++) {
                GroupGift gift = giftList.get(j);
                Define.p_group_gift.Builder pGroupGift = Define.p_group_gift.newBuilder();
                pGroupGift.setId(gift.getId());
                pGroupGift.setGroupSn(gift.getGroupSn());
                pGroupGift.setState(gift.getState());
                pGroupGift.setStartTime(gift.getStartTime());
                pGroupGift.setCloseTime(gift.getCloseTime());
                pGroupGift.setSuccessTime(gift.getSuccessTime());
                List<Long> humanIdList = Utils.strToLongList(gift.getHumanIdList());
                List<String> humanNameList = Utils.strToStringList(gift.getHumanNameList());
                for (int k = 0; k < humanIdList.size(); k++) {
                    pGroupGift.addHumanList(HumanManager.inst().to_p_key_string(humanIdList.get(k), humanNameList.get(k)));
                }
                msg.addGiftList(pGroupGift);
            }
            if (humanId == 0) {
                HumanGlobalServiceProxy.newInstance().sendMsgToAll(null, msg.build());
            } else {
                HumanGlobalServiceProxy.newInstance().sendMsg(humanId, msg.build());
            }
        }
    }

    public void on_act_group_gift_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlGroupGift control = (ActivityControlGroupGift) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_group_gift_c2s(humanObj);
    }

    public void on_act_group_gift_opt_c2s(HumanObject humanObj, int actType, int opt, int sn, long id) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlGroupGift control = (ActivityControlGroupGift) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_group_gift_opt_c2s(humanObj, opt, sn, id);
    }

    public void on_act_group_gift_reward_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlGroupGift control = (ActivityControlGroupGift) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_group_gift_reward_c2s(humanObj);
    }
    // endregion 团购礼包活动

    //加好友活动
    public void addFriend(HumanObject humanObj, long friendId) {
        ActivityControlAddFriend.addTaskProcess(humanObj,friendId);
    }

    public void delFriend(HumanObject humanObj, long friendId) {
        ActivityControlAddFriend.deleteFriend(humanObj,friendId);
    }

    public void addFriendPocket(HumanObject humanObj, long humanId, long friendId) {
        Set<Integer> actTypeSet = ActivityControlTypeFactory.getTypeSet(ActivityControlAddFriend.class);
        for (int actType : actTypeSet) {
            if (!isActivityOpen(humanObj, actType)) {
                continue;
            }
            JSONObject jo = new JSONObject();
            jo.put("friendId", friendId);
            jo.put("add", 1);
            Pocket.add(humanId, PocketLineEventSubKey.UPDATE_FRIEND, jo.toJSONString());
            break;
        }
    }

    public void delFriendPocket(HumanObject humanObj, long humanId, long friendId) {
        Set<Integer> actTypeSet = ActivityControlTypeFactory.getTypeSet(ActivityControlAddFriend.class);
        for (int actType : actTypeSet) {
            if (!isActivityOpen(humanObj, actType)) {
                continue;
            }
            JSONObject jo = new JSONObject();
            jo.put("friendId", friendId);
            jo.put("add", 0);
            Pocket.add(humanId, PocketLineEventSubKey.UPDATE_FRIEND, jo.toJSONString());
            break;
        }
    }

    @Listener(value = EventKey.POCKET_LINE_HANDLE_ONE, subStr = PocketLineEventSubKey.UPDATE_FRIEND)
    public void pocketLine_UPDATE_FRIEND(Param param) {
        HumanObject humanObj = param.get("humanObj");
        PocketLine p = param.get("pocketLine");
        JSONObject jo = Utils.toJSONObject(p.getParam());
        long friendId = jo.getLongValue("roleId");
        int isAdd = jo.getIntValue("add");
        if(isAdd == 1){
            ActivityManager.inst().addFriend(humanObj, friendId);
        }else{
            ActivityManager.inst().delFriend(humanObj, friendId);
        }
    }
    //加好友活动结束

    //抽奖活动开始
    public void on_act_double_draw_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpenOrShow(humanObj, actType)) {
            return;
        }
        ActivityControlDoubleDraw control = (ActivityControlDoubleDraw) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_double_draw_info_c2s(humanObj);
    }

    public void on_act_double_draw_draw_c2s(HumanObject humanObj, int actType, int type) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDoubleDraw control = (ActivityControlDoubleDraw) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_double_draw_draw_c2s(humanObj, type);
    }

    public void on_act_double_draw_count_reward_c2s(HumanObject humanObj, int actType, int cfgId) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDoubleDraw control = (ActivityControlDoubleDraw) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_double_draw_count_reward_c2s(humanObj, cfgId);
    }

    public void on_act_double_draw_choose_c2s(HumanObject humanObj, int actType, int cfgId, int chooseId) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlDoubleDraw control = (ActivityControlDoubleDraw) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_double_draw_choose_c2s(humanObj, cfgId, chooseId);
    }
    //抽奖活动结束

    //全民提交道具开始
    public void on_act_cohesion_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpenOrShow(humanObj, actType)) {
            return;
        }
        ActivityControlCohesion control = (ActivityControlCohesion) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_cohesion_info_c2s(humanObj);

    }

    public void on_act_cohesion_get_reward_c2s(HumanObject humanObj, int actType, int rewardId) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlCohesion control = (ActivityControlCohesion) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_cohesion_get_reward_c2s(humanObj, rewardId);
    }

    public void leaveGuild(long humanId, long guildId) {
        ActivityControlCohesion.leaveGuild(humanId, guildId);
//        ActivityControlGuildPay.leaveGuild(humanId, guildId);
    }
    //全民提交道具结束

    // region 累计积分
    public void on_act_accumulate_score_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlAccumulateScore control = (ActivityControlAccumulateScore) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_accumulate_score_info_c2s(humanObj);
    }
    // endregion 累计积分

    // region 空投礼包
    public void on_act_airdrop_gift_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlAirdropGift control = (ActivityControlAirdropGift) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_airdrop_gift_info_c2s(humanObj);
    }
    public void on_act_airdrop_gift_draw_c2s(HumanObject humanObj, int actType, int drawTye) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlAirdropGift control = (ActivityControlAirdropGift) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_airdrop_gift_draw_c2s(humanObj, drawTye);
    }
    public void on_act_airdrop_gift_open_c2s(HumanObject humanObj, int actType, int pos) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlAirdropGift control = (ActivityControlAirdropGift) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_airdrop_gift_open_c2s(humanObj, pos);
    }
    public void on_act_airdrop_gift_del_c2s(HumanObject humanObj, int actType, int pos) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlAirdropGift control = (ActivityControlAirdropGift) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_airdrop_gift_del_c2s(humanObj, pos);
    }
    // endregion 空投礼包

    public void on_act_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpenOrShow(humanObj, actType)) {
            return;
        }
        IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_info_c2s(humanObj);
    }

    // region 黑市相关
    public void on_act_black_market_buy_c2s(HumanObject humanObj, int actType, int sn, int num) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlBlackMarket control = (ActivityControlBlackMarket) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_black_market_buy_c2s(humanObj, sn, num);
    }

    /**
     * 获取下次预购开奖时间
     */
    public long getNextOpenPreTime(int actSn) {
        ConfActivityControl conf = ConfActivityControl.get(actSn);
        if (conf == null) {
            Log.activity.error("[黑市]获取预购开奖时间, 活动配置不存在, actSn={}", actSn);
            return 0;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(conf.type, actSn);
        if (confTerm == null) {
            Log.activity.error("[黑市]获取预购开奖时间, 活动时间配置不存在, actSn={}", actSn);
            return 0;
        }
        long[] openEndTime = ActivityControlCrossService.getOpenEndTime(conf);
        long currTime = Port.getTime();
        if (openEndTime == null || currTime >= openEndTime[1]) {
            Log.activity.error("[黑市]获取预购开奖时间, 活动已结束, actSn={}", actSn);
            return 0;
        }
        long actEndTime = openEndTime[1];
        int[][] times = Utils.parseIntArray2(confTerm.parameter2);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, times[0][0]);
        calendar.set(Calendar.MINUTE, times[0][1]);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long time1 = calendar.getTimeInMillis();
        calendar.set(Calendar.HOUR_OF_DAY, times[2][0]);
        calendar.set(Calendar.MINUTE, times[2][1]);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long time2 = calendar.getTimeInMillis();
        if (currTime < time1) {
            return Math.min(actEndTime, time1);
        } else if (currTime < time2) {
            return Math.min(actEndTime, time2);
        } else {
            // 取明天的time1
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, times[0][0]);
            calendar.set(Calendar.MINUTE, times[0][1]);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            time1 = calendar.getTimeInMillis();
            return Math.min(actEndTime, time1);
        }
    }

    /**
     * 获取下次刷新时间
     */
    public long getNextFreshTime(int actSn) {
        ConfActivityControl conf = ConfActivityControl.get(actSn);
        if (conf == null) {
            Log.activity.error("[黑市]获取下次刷新时间, 活动配置不存在, actSn={}", actSn);
            return 0;
        }
        ConfActivityTerm confTerm = ActivityManager.inst().getActivityTerm(conf.type, actSn);
        if (confTerm == null) {
            Log.activity.error("[黑市]获取下次刷新时间, 活动时间配置不存在, actSn={}", actSn);
            return 0;
        }
        long[] openEndTime = ActivityControlCrossService.getOpenEndTime(conf);
        long currTime = Port.getTime();
        if (openEndTime == null || currTime >= openEndTime[1]) {
            Log.activity.error("[黑市]获取下次刷新时间, 活动已结束, actSn={}", actSn);
            return 0;
        }
        int[][] times = Utils.parseIntArray2(confTerm.parameter2);
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, times[1][0]);
        calendar.set(Calendar.MINUTE, times[1][1]);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long time1 = calendar.getTimeInMillis();
        if (time1 >= openEndTime[1]) {
            Log.activity.error("[黑市]获取下次刷新时间, 下次刷新时间已在活动结束之后, actSn={}", actSn);
            return 0;
        }
        calendar.set(Calendar.HOUR_OF_DAY, times[3][0]);
        calendar.set(Calendar.MINUTE, times[3][1]);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long time2 = calendar.getTimeInMillis();
        if (time2 >= openEndTime[1]) {
            Log.activity.error("[黑市]获取下次刷新时间, 下次刷新时间已在活动结束之后, actSn={}", actSn);
            return 0;
        }
        if (currTime < time1) {
            return time1;
        } else if (currTime < time2) {
            return time2;
        } else {
            // 取明天的time1
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, times[1][0]);
            calendar.set(Calendar.MINUTE, times[1][1]);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            time1 = calendar.getTimeInMillis();
            if (time1 >= openEndTime[1]) {
                Log.activity.error("[黑市]获取下次刷新时间, 下次刷新时间已在活动结束之后, actSn={}", actSn);
                return 0;
            }
            return time1;
        }
    }
    // endregion 黑市相关

    @Listener(EventKey.GAME_STARTUP_FINISH)
    public void _listener_GAME_STARTUP_FINISH(Param params) {
        try {
            Node node = params.get("node");
            if (NodeType.CROSS.isMatch(node.getNodeType())) {
                // 随机一个port处理
                Port port = node.getRandomPortInfo();
                port.addQueue(new PortPulseQueue() {
                    @Override
                    public void execute(Port port) {
                        handleCrossActivity();
                    }
                });
            }
        } catch (Exception e) {
            throw new SysException(e);
        }
    }

    public void handleCrossActivity() {
        if (!S.isCross)
            return;// 不是跨服不管
        int crossServerId = Config.SERVER_ID - GlobalConfVal.cross_server_id_base;
        if (crossServerId == 0) {
            return;
        }
        boolean isCrossActivityServer = false;
        ConfGlobal confGlobal = ConfGlobal.get(ConfGlobalKey.跨服活动服务器开启区间.SN);
        if (confGlobal != null && confGlobal.intArray != null) {
            Set<Integer> crossList = new HashSet<>(Utils.intArrToList(confGlobal.intArray));
            isCrossActivityServer = isCrossActivityServer || crossList.contains(crossServerId);
        }
        confGlobal = ConfGlobal.get(ConfGlobalKey.跨服黑市服务器开启区间.SN);
        if (confGlobal != null && confGlobal.intArray != null) {
            List<Integer> crossList = Utils.intArrToList(confGlobal.intArray);
            isCrossActivityServer = isCrossActivityServer || crossList.contains(crossServerId);
        }
        if (!isCrossActivityServer) {
            Log.game.error("[跨服活动]当前跨服不是跨服活动服务器, 不处理ActivityControlCrossService, serverId={}", crossServerId);
            return;
        }
        ActivityControlCrossServiceProxy.newInstance().queryServiceControlActType();
    }
    // region 无职联动-好感度活动相关
    /**
     * 无职联动-好感活动信息请求
     */
    public void on_act_wuzhi_love_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpenOrShow(humanObj, actType)) {
            return;
        }
        ActivityControlWuzhiLove control = (ActivityControlWuzhiLove) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_wuzhi_love_info_c2s(humanObj);
    }

    /**
     * 无职联动-选择角色
     */
    public void on_act_wuzhi_love_select_char_c2s(HumanObject humanObj, int actType, int charId) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlWuzhiLove control = (ActivityControlWuzhiLove) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_wuzhi_love_select_char_c2s(humanObj, charId);
    }

    /**
     * 无职联动-合成手办
     */
    public void on_act_wuzhi_love_compose_figure_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlWuzhiLove control = (ActivityControlWuzhiLove) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_wuzhi_love_compose_figure_c2s(humanObj);
    }

    /**
     * 无职联动-赠送礼物
     */
    public void on_act_wuzhi_love_give_gift_c2s(HumanObject humanObj, int actType, int charId, List<Define.p_key_value> itemSnNumList) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlWuzhiLove control = (ActivityControlWuzhiLove) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_wuzhi_love_give_gift_c2s(humanObj, charId, itemSnNumList);
    }

    /**
     * 无职联动-领取好感等级奖励
     */
    public void on_act_wuzhi_love_claim_level_reward_c2s(HumanObject humanObj, int actType, int charId, int level) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        ActivityControlWuzhiLove control = (ActivityControlWuzhiLove) ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        control.on_act_wuzhi_love_claim_level_reward_c2s(humanObj, charId, level);
    }

    // endregion 无职联动-好感度活动相关

    // region 无职联动-手办收集活动相关

    /**
     * 无职手办活动信息请求
     */
    public void on_act_wuzhi_figure_info_c2s(HumanObject humanObj, int actType) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        if (control instanceof ActivityControlFigureCollection) {
            ((ActivityControlFigureCollection) control).on_act_wuzhi_figure_info_c2s(humanObj);
        }
    }

    /**
     * 无职手办制作请求
     */
    public void on_act_wuzhi_figure_make_c2s(HumanObject humanObj, int actType, int makeType, int count) {
        if (!isActivityOpen(humanObj, actType)) {
            return;
        }
        IActivityControl control = ActivityControlTypeFactory.getTypeData(actType);
        if (control == null) {
            return;
        }
        if (control instanceof ActivityControlFigureCollection) {
            ((ActivityControlFigureCollection) control).on_act_wuzhi_figure_make_c2s(humanObj, makeType, count);
        }
    }

    // endregion 无职联动-手办收集活动相关
}

