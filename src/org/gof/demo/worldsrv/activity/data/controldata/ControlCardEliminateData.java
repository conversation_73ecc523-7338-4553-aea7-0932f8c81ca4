package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.TypeReference;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.config.ConfCardEliminateBuff;
import org.gof.demo.worldsrv.config.ConfGlobal;

import java.util.*;

import com.alibaba.fastjson.JSONObject;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.support.Log;

/**
 * 翻牌活动数据
 */
public class ControlCardEliminateData implements IControlData {
    // 门票相关
    public ControlStaminaManager ticketMgr;

    // 游戏状态
    public int stamina;                    // 当前局内体力
    public int round;                      // 当前轮数
    public int score;                      // 当前积分
    public int totalStep;                  // 累计步数
    public int combo;                      // 当前连消次数
    public long maxScore;                  // 历史最高分
    public long sumScore;                  // 累计得分
    public int openCount = 0;            // 开局次数
    public int refreshCount = 0; // buff刷新次数

    // 卡牌数据
    public List<Integer> cards = new ArrayList<>();  // 卡牌元素ID列表
    public List<Integer> cardStatus = new ArrayList<>(); // 卡牌状态列表
    //透视效果位置，步数
    public Map<Integer, Integer> xRayCardMap = new HashMap<>(); // 透视卡牌位置和步数，key:位置, value:剩余步数

    // 任务数据
    public int taskElementId;              // 当前任务元素ID
    public int taskPatience;               // 当前任务耐心值
    public int taskMaxPatience;            // 当前任务最大耐心值
    public int nextTaskElementId;          // 下一个任务元素ID
    public int taskRewardStamina;          // 任务奖励体力
    public List<Integer> taskElementIds = new ArrayList<>();   // 任务元素ID列表
    // Buff数据
    public Map<Integer, Integer> buffs = new HashMap<>(); // 已获得的buff列表，key:buffId, value:星级
    public List<Integer> buffChoices = new ArrayList<>(); // 当前可选的buff列表

    //是否购买特权
    public boolean isBuyMiddle = false;
    public boolean isBuyHigh = false;


    public ControlCardEliminateData(String jsonStr) {
        JSONObject json = Utils.toJSONObject(jsonStr);

        // 从protobuf消息中恢复主要数据
        if (json.containsKey("info")) {
            MsgAct2.act_card_eliminate_info_s2c cardProto = Utils.decompressProtoLZ4(
                    json.getString("info"),
                    MsgAct2.act_card_eliminate_info_s2c.parser()
            );

            // 基础数据
            maxScore = cardProto.getMaxScore();
            sumScore = cardProto.getSumScore();

            // 初始化门票管理器
            ConfGlobal confGlobal = ConfGlobal.get("Card_ticket_recover");
            int maxStamina = confGlobal.intArray[0];
            int recoverCD = confGlobal.intArray[1];
            int recoverAmount = confGlobal.intArray[2];
            ticketMgr = new ControlStaminaManager(recoverCD, recoverAmount, maxStamina);
            ticketMgr.stamina = cardProto.getTicket();
            ticketMgr.nextRecoverTime = cardProto.getNextRecoverTime();

            // 游戏数据
            stamina = cardProto.getStamina();
            round = cardProto.getRound();
            score = cardProto.getScore();
            totalStep = cardProto.getTotalStep();
            combo = cardProto.getCombo();
            refreshCount = cardProto.getRefreshCount();

            // 任务数据
            if (cardProto.hasTask()) {
                Define.p_card_eliminate_task task = cardProto.getTask();
                taskElementId = task.getElementId();
                taskPatience = task.getPatience();
                taskMaxPatience = task.getMaxPatience();
                nextTaskElementId = task.getNextElementId();
                taskRewardStamina = task.getRewardStamina();
            }

            // 卡牌数据
            cards.clear();
            cardStatus.clear();

            for (Define.p_card_eliminate_card card : cardProto.getCardsList()) {
                while (cards.size() < card.getPos()) {
                    cards.add(0);
                    cardStatus.add(0);
                }
                cards.set(card.getPos() - 1, card.getElementId());
                cardStatus.set(card.getPos() - 1, card.getStatus());
            }
            // Buff数据
            buffs.clear();
            for (Define.p_key_value buff : cardProto.getBuffsList()) {
                buffs.put((int) buff.getK(), (int) buff.getV());
            }

            // Buff选择数据
            buffChoices.clear();
            buffChoices.addAll(cardProto.getRandomBuffsList());
        }

        // 恢复透视相关数据
        if (json.containsKey("xRayMap")) {
            xRayCardMap = json.getJSONObject("xRayMap").toJavaObject(new TypeReference<Map<Integer, Integer>>() {
            });
        }

        // 恢复任务元素ID列表
        if (json.containsKey("taskElementIds")) {
            taskElementIds = Utils.strToIntList(json.getString("taskElementIds"));
        }
        // 恢复购买特权状态
        if (json.containsKey("buyM")) {
            isBuyMiddle = true;
        }
        if (json.containsKey("buyH")) {
            isBuyHigh = true;
        }
        if(json.containsKey("oC")){
            openCount = json.getIntValue("oC");
        } else {
            openCount = 0; // 默认值
        }
    }

    /**
     * 获取已翻开但未消除的卡牌位置列表
     */
    public List<Integer> getFlippedCards() {
        List<Integer> flippedCards = new ArrayList<>();
        for (int i = 0; i < cardStatus.size(); i++) {
            if (cardStatus.get(i) == 1) { // 状态1:已翻开
                flippedCards.add(i + 1); // 位置从1开始
            }
        }
        return flippedCards;
    }

    /**
     * 获取未翻开的卡牌位置列表
     */
    public List<Integer> getUnflippedCards() {
        List<Integer> unflippedCards = new ArrayList<>();
        for (int i = 0; i < cardStatus.size(); i++) {
            if (cardStatus.get(i) == 0 || cardStatus.get(i) == 3) { // 状态0:未翻开 或 状态3:透视
                unflippedCards.add(i + 1); // 位置从1开始
            }
        }
        return unflippedCards;
    }

    /**
     * 获取已消除的卡牌数量
     */
    public int getEliminatedCount() {
        int count = 0;
        for (int status : cardStatus) {
            if (status == 2) { // 状态2:已消除
                count++;
            }
        }
        return count;
    }

    /**
     * 检查是否所有卡牌都已消除
     */
    public boolean isAllCardsEliminated() {
        return getEliminatedCount() == cards.size();
    }

    public void setCardStatus(int index, int status) {
        if (index < 0 || index >= cardStatus.size()) {
            Log.activity.error("setCardStatus: Invalid position {}", index);
            return;
        }
        cardStatus.set(index, status);
        if(status != 3){
            xRayCardMap.remove(index+1);
        }
    }

    /**
     * 重置游戏数据
     */
    public void resetGameData() {
        stamina = 0;
        round = 0;
        score = 0;
        totalStep = 0;
        combo = 0;
        cards.clear();
        cardStatus.clear();
        xRayCardMap.clear();
        taskElementId = 0;
        taskPatience = 0;
        taskMaxPatience = 0;
        nextTaskElementId = 0;
        taskRewardStamina = 0;
        buffChoices.clear();
        buffs.clear();
        refreshCount = 0;
    }

    /**
     * 更新透视状态
     * 每次翻牌后需要调用此方法更新透视状态
     */
    public List<Integer> updateXRayStatus() {
        // 遍历xRayCardMap，步数减1
        List<Integer> toRemove = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : xRayCardMap.entrySet()) {
            int pos = entry.getKey();
            int steps = entry.getValue() - 1;
            if (steps <= 0) {
                // 步数用尽，恢复为未翻开状态
                int index = pos - 1;
                if (index >= 0 && index < cardStatus.size() && cardStatus.get(index) == 3) {
                    cardStatus.set(index, 0);
                }
                toRemove.add(pos);
            } else {
                xRayCardMap.put(pos, steps);
            }
        }
        // 移除步数为0的卡牌
        for (int pos : toRemove) {
            xRayCardMap.remove(pos);
        }
        return toRemove;
    }

    public void addXRayCards(int xrayCount, int xraySteps) {
        // 添加透视卡牌
        List<Integer> unflippedCards = new ArrayList<>();
        for (int i = 0; i < cardStatus.size(); i++) {
            if (cardStatus.get(i) == 0) { // 状态0:未翻开 或 状态3:透视
                unflippedCards.add(i + 1); // 位置从1开始
            }
        }
        //随性顺序
        Collections.shuffle(unflippedCards);
        for (int i = 0; i < xrayCount && i < unflippedCards.size(); i++) {
            int pos = unflippedCards.get(i); // 随机位置
            int index = pos - 1;
            xRayCardMap.put(pos, xraySteps);
            cardStatus.set(index, 3); // 设置为透视状态
        }
    }

    /**
     * 获取门票数量
     */
    public int getTicket() {
        return ticketMgr != null ? ticketMgr.stamina : 0;
    }

    /**
     * 获取下次门票恢复时间
     */
    public long getNextRecoverTime() {
        return ticketMgr.nextRecoverTime == 0 ? 0 : (int) (ticketMgr.nextRecoverTime / 1000) + 1;// 转换为秒
    }

    /**
     * 消耗门票
     * @return 是否成功消耗
     */
    public boolean costTicket() {
        if (ticketMgr != null && ticketMgr.stamina > 0) {
            ticketMgr.consumeStamina(1);
            openCount++;
            return true;
        }
        return false;
    }

    public ConfCardEliminateBuff getBuffConfigBy(int buffType) {
        for (Map.Entry<Integer, Integer> entry : buffs.entrySet()) {
            ConfCardEliminateBuff confBuff = ConfCardEliminateBuff.get(entry.getKey());
            if (confBuff == null) {
                Log.activity.error("getBuffStarBy: Invalid buffType {}", entry.getKey());
                continue;
            }
            if (confBuff.buff_type == buffType) {
                return confBuff; // 返回对应的星级
            }
        }
        return null;
    }

    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
        ConfGlobal confGlobal = ConfGlobal.get("Card_ticket_recover");
        int maxStamina = confGlobal.intArray[0];
        int recoverCD = confGlobal.intArray[1];
        int recoverAmount = confGlobal.intArray[2];
        ticketMgr = new ControlStaminaManager(recoverCD, recoverAmount, maxStamina);
        ticketMgr.stamina = ticketMgr.maxStamina;
    }

    @Override
    public String toJSON() {
        JSONObject json = new JSONObject();

        // 构建protobuf消息并存储主要数据
        MsgAct2.act_card_eliminate_info_s2c.Builder builder = MsgAct2.act_card_eliminate_info_s2c.newBuilder();
        builder.setMaxScore(maxScore);
        builder.setSumScore(sumScore);
        builder.setTicket(getTicket());
        builder.setNextRecoverTime(ticketMgr.nextRecoverTime);

        // 游戏进行中的数据
        if (stamina > 0) {
            builder.setStamina(stamina);
            builder.setRound(round);
            builder.setScore(score);
            builder.setTotalStep(totalStep);
            builder.setCombo(combo);
            builder.setRefreshCount(refreshCount);

            // 添加任务信息
            Define.p_card_eliminate_task.Builder taskBuilder = Define.p_card_eliminate_task.newBuilder();
            taskBuilder.setElementId(taskElementId);
            taskBuilder.setPatience(taskPatience);
            taskBuilder.setMaxPatience(taskMaxPatience);
            taskBuilder.setNextElementId(nextTaskElementId);
            taskBuilder.setRewardStamina(taskRewardStamina);
            builder.setTask(taskBuilder);

            // 添加卡牌信息
            for (int i = 0; i < cards.size(); i++) {
                Define.p_card_eliminate_card.Builder cardBuilder = Define.p_card_eliminate_card.newBuilder();
                cardBuilder.setPos(i + 1);
                cardBuilder.setElementId(cards.get(i));
                cardBuilder.setStatus(cardStatus.get(i));
                builder.addCards(cardBuilder);
            }

            // 添加buff信息
            for (Map.Entry<Integer, Integer> entry : buffs.entrySet()) {
                builder.addBuffs(Define.p_key_value.newBuilder()
                    .setK(entry.getKey())
                    .setV(entry.getValue()));
            }
        }

        // 添加buff选择信息
        for (int buffId : buffChoices) {
            builder.addRandomBuffs(buffId);
        }

        // 将protobuf消息压缩存储
        json.put("info", Utils.compressProtoLZ4(builder.build()));
        // 添加透视卡牌信息
        if (!xRayCardMap.isEmpty()) {
            json.put("xRayMap", xRayCardMap);
        }
        if(!taskElementIds.isEmpty()) {
            json.put("taskElementIds", Utils.listToString(taskElementIds));
        }
        // 添加购买特权状态
        if (isBuyMiddle) {
            json.put("buyM", true);
        }
        if (isBuyHigh) {
            json.put("buyH", true);
        }
        if(openCount > 0){
            json.put("oC", openCount);
        }
        return json.toJSONString();
    }
}
