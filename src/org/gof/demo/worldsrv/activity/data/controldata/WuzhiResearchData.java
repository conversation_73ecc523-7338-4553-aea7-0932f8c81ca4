package org.gof.demo.worldsrv.activity.data.controldata;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityVo;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.human.HumanManager;
import org.gof.demo.worldsrv.msg.Define;
import org.gof.demo.worldsrv.msg.MsgAct2;
import org.gof.demo.worldsrv.support.Log;

import java.util.*;

/**
 * 无职研究活动数据
 */
public class WuzhiResearchData implements IControlData {
    
    // 基础状态
    public int state = 0;                           // 阶段 (0：挖矿,1：鉴定,2:摇奖)
    public int currentLayer = 1;                    // 当前层数
    public int prizeCount = 0;                      // 累计活动大奖次数
    public int claimId = 0;                         // 已经领取的最高奖励id
    
    // 挖矿相关
    public Map<Integer, Integer> openTiles = new HashMap<>();  // 已开启格子 k:格子id，v：道具id v=0大奖
    
    // 鉴定相关
    public List<Boolean> appraisalResults = new ArrayList<>();  // 所有鉴定结果
    public int appraisalIndex = 0;                  // 鉴定次数
    public int currentGrandRewardSn = 0;            // 当前大奖奖励配置sn
    public int currentSuccessCount = 0;             // 当前鉴定成功次数
    
    // 摇奖相关
    public int grandRewardId = 0;                   // 摇奖奖励ID
    
    // 一键研究设置
    public int mode = 0;                            // 0:顺序研究 1:随机研究
    public boolean autoAppraisal = false;           // 是否自动完成鉴定
    
    public WuzhiResearchData(String jsonStr) {
        if (jsonStr == null || jsonStr.isEmpty()) {
            return;
        }
        
        JSONObject json = Utils.toJSONObject(jsonStr);
        
        // 从protobuf消息中恢复主要数据
        if (json.containsKey("info")) {
            MsgAct2.act_wuzhi_research_info_s2c researchProto = Utils.decompressProtoLZ4(
                    json.getString("info"),
                    MsgAct2.act_wuzhi_research_info_s2c.parser()
            );
            
            // 基础数据
            state = researchProto.getState();
            currentLayer = researchProto.getLayer();
            prizeCount = researchProto.getPrizeCount();
            claimId = researchProto.getClaimId();
            mode = researchProto.getMode();
            autoAppraisal = researchProto.getAutoAppraisal();
            
            // 已开启格子
            openTiles.clear();
            for (Define.p_key_value tile : researchProto.getOpenTilesList()) {
                openTiles.put((int) tile.getK(), (int) tile.getV());
            }
            
            // 鉴定结果
            appraisalResults.clear();
            appraisalResults.addAll(researchProto.getAppraisalResultsList());
            appraisalIndex = researchProto.getAppraisalIndex();
        }
        
        // 恢复其他数据
        if (json.containsKey("currentGrandRewardSn")) {
            currentGrandRewardSn = json.getIntValue("currentGrandRewardSn");
        }
        if (json.containsKey("currentSuccessCount")) {
            currentSuccessCount = json.getIntValue("currentSuccessCount");
        }
        if (json.containsKey("grandRewardId")) {
            grandRewardId = json.getIntValue("grandRewardId");
        }
    }
    
    /**
     * 重置挖矿状态
     */
    public void resetMiningState() {
        state = 0;
        openTiles.clear();
        appraisalResults.clear();
        appraisalIndex = 0;
        currentGrandRewardSn = 0;
        currentSuccessCount = 0;
        grandRewardId = 0;
    }
    
    /**
     * 开始鉴定阶段
     */
    public void startAppraisalPhase(int rewardSn, int successCount) {
        state = 1;
        currentGrandRewardSn = rewardSn;
        currentSuccessCount = successCount;
        appraisalResults.clear();
        appraisalIndex = 0;
        
        // 生成鉴定结果序列
        generateAppraisalResults(successCount);
    }
    
    /**
     * 生成鉴定结果序列
     */
    private void generateAppraisalResults(int successCount) {
        appraisalResults.clear();
        
        // 添加成功次数
        for (int i = 0; i < successCount; i++) {
            appraisalResults.add(true);
        }
        
        // 添加失败次数
        for (int i = 0; i < 4 - successCount; i++) {
            appraisalResults.add(false);
        }
        
        // 随机打乱顺序
        Collections.shuffle(appraisalResults);
    }
    
    /**
     * 开始摇奖阶段
     */
    public void startDrawPhase(int rewardId) {
        state = 2;
        grandRewardId = rewardId;
    }
    
    /**
     * 完成当前轮次，进入下一层
     */
    public void completeRound() {
        currentLayer++;
        prizeCount++;
        resetMiningState();
    }
    
    /**
     * 检查是否可以使用一键研究
     */
    public boolean canUseOneClick(int autoNeed) {
        return prizeCount >= autoNeed;
    }
    
    /**
     * 获取下一个格子ID（顺序模式）
     */
    public int getNextTileIdSequential(int totalTiles) {
        for (int i = 1; i <= totalTiles; i++) {
            if (!openTiles.containsKey(i)) {
                return i;
            }
        }
        return -1; // 所有格子都已开启
    }
    
    /**
     * 获取下一个格子ID（随机模式）
     */
    public int getNextTileIdRandom(int totalTiles) {
        List<Integer> availableTiles = new ArrayList<>();
        for (int i = 1; i <= totalTiles; i++) {
            if (!openTiles.containsKey(i)) {
                availableTiles.add(i);
            }
        }
        
        if (availableTiles.isEmpty()) {
            return -1;
        }
        
        return availableTiles.get(Utils.random(0, availableTiles.size()));
    }
    
    /**
     * 检查是否是最后一个格子
     */
    public boolean isLastTile(int totalTiles) {
        return openTiles.size() == totalTiles - 1;
    }
    
    @Override
    public void create(HumanObject humanObj, ActivityVo vo) {
        // 初始化默认值
        state = 0;
        currentLayer = 1;
        prizeCount = 0;
        claimId = 0;
        mode = 0;
        autoAppraisal = false;
        openTiles.clear();
        appraisalResults.clear();
        appraisalIndex = 0;
        currentGrandRewardSn = 0;
        currentSuccessCount = 0;
        grandRewardId = 0;
    }
    
    @Override
    public String toJSON() {
        JSONObject json = new JSONObject();
        
        // 构建protobuf消息并存储主要数据
        MsgAct2.act_wuzhi_research_info_s2c.Builder builder = MsgAct2.act_wuzhi_research_info_s2c.newBuilder();
        builder.setState(state);
        builder.setLayer(currentLayer);
        builder.setPrizeCount(prizeCount);
        builder.setClaimId(claimId);
        builder.setMode(mode);
        builder.setAutoAppraisal(autoAppraisal);
        builder.setAppraisalIndex(appraisalIndex);
        
        // 添加已开启格子
        for (Map.Entry<Integer, Integer> entry : openTiles.entrySet()) {
            builder.addOpenTiles(HumanManager.inst().to_p_key_value(entry.getKey(), entry.getValue()));
        }
        
        // 添加鉴定结果
        builder.addAllAppraisalResults(appraisalResults);
        
        // 将protobuf消息压缩存储
        json.put("info", Utils.compressProtoLZ4(builder.build()));
        
        // 添加其他数据
        if (currentGrandRewardSn > 0) {
            json.put("currentGrandRewardSn", currentGrandRewardSn);
        }
        if (currentSuccessCount > 0) {
            json.put("currentSuccessCount", currentSuccessCount);
        }
        if (grandRewardId > 0) {
            json.put("grandRewardId", grandRewardId);
        }
        
        return json.toJSONString();
    }
}
