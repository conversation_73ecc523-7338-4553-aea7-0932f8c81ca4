新建活动类型6203命名WuzhiResearch
1. 系统概述
本活动为“挖矿”或“格子翻牌”式玩法，玩家消耗“灵感”道具来开启棋盘上的格子。
开启格子可随机获得“普通奖励”或触发“大奖”。
触发大奖后，会进入一个名为“灵感鉴定”的流程。服务器会预先确定本次鉴定的“成功次数”，这决定了大奖的最终品质。
若鉴定结果为“完美”（即4次全部成功），会额外触发一个“摇奖机”玩法，获得更高价值的奖励。
成功获得大奖后，玩家会进入更深的“层数”，后续挖矿的奖池会发生变化。
每次触发大奖都会进行累计计数，达到指定次数可领取额外的累计奖励。
2. 玩法规则
1. 研究开奖（开启格子）
玩家每次请求开启格子，服务器需验证并扣除WuzhiResearchConfig_七星研究配置表中cost字段定义的“灵感”道具。期数表的group_id是这个表的sn
服务器需要记录并使用玩家当前的current_layer（当前层数）作为抽奖依据，初始层数为1。
2. 奖励生成逻辑
奖池确定：根据玩家的current_layer，从WuzhiResearchReward_七星研究奖励表中筛选出layer字段一堆数组是个区间左闭右闭范围覆盖当前层数的所有奖励条目。要匹配期数表的group_id是这个表的research_id
加权随机：对筛选出的奖池，根据每个条目的weight字段进行加权随机，确定本次开启格子的奖励项。
奖励类型判断：
若抽中项的grand字段为0，则为“普通奖励”。服务器直接根据output字段（格式：掉落组ID|生成次数）生成奖励并发放。
若grand字段为1，则为“大奖”，进入下方的大奖处理流程。保底最后一个格子必定是大奖,WuzhiResearchConfig_七星研究配置的num:本期格子数
3. 大奖处理（灵感鉴定）
当抽中“大奖”奖励项时，服务器 立即确定 本次鉴定的结果，客户端仅负责动画表现。
成功次数确定：本次鉴定的“成功次数”由抽中奖励项的success字段唯一确定。例如，success为2，则客户端会表现为2次成功、2次失败。随机好这次失败成功的顺序,可以全部告诉客户端
大奖奖励生成：根据抽中项的output字段生成本次大奖的最终奖励。
层数推进：玩家成功获得大奖奖励后，其current_layer计数值+1。
累计计数：玩家的“累计大奖触发次数”+1，用于累计奖励的判断。
4. 完美鉴定摇奖机
当大奖的“成功次数”为4时，会触发此额外机制。
触发后，服务器需要读取触发本次大奖的奖励项中的grand_reward字段。
该字段为一个output表关联ID，服务器需根据此ID生成一份额外的顶级奖励，与大奖奖励一并发放。
5. 累计奖励
服务器需记录玩家的“累计大奖触发次数”。
当此计数值达到WuzhiResearchTimes_0_七星研究累计奖励表中cumulative_times字段的要求时，玩家即可领取对应的reward奖励。客户端发来的id ConfWuzhiResearchTimes_0.get(id, group_id)获取,判断cumulative_times就是获得大奖的次数,给reward奖励
6. 一键研究
解锁条件：玩家需要累计大奖触发次数达到WuzhiResearchConfig_七星研究配置表中auto_need字段要求的次数后，才能使用本功能。
执行逻辑：服务器接收到一键研究请求后，在后台循环执行“研究开奖”逻辑，直到发生以下任一情况：
玩家的“灵感”道具不足。
挖出了“大奖”。
结果返回：
协议已经加好如下:在MsgAct2.proto 和Define.proto
// 七星研究-活动信息
message act_wuzhi_research_info_c2s {
    option (msgid) = 6714;
    uint32 act_type = 1;
}

message act_wuzhi_research_info_s2c {
    option (msgid) = 6715;
    uint32 act_type = 1;
    uint32 state = 2;		//阶段 (0：挖矿,1：鉴定,2:摇奖)
	uint32 layer = 3;		//当前层数
    repeated p_key_value open_tiles = 4;		//k:格子id，v：道具id v=0大奖
    repeated bool appraisal_results = 5;		//所有鉴定结果
    uint32 appraisal_index = 6;					//鉴定次数
    uint32 prize_count = 7;		//累计活动大奖次数
    uint32 claim_id = 8;		//已经领取的最高奖励id
    uint32 mode = 9;//0:顺序研究 1:随机研究
    bool auto_appraisal = 10; // 是否自动完成鉴定
}


// 七星研究-研究(挖矿)
message act_wuzhi_research_dig_c2s {
    option (msgid) = 6716;
    uint32 act_type = 1;
    uint32 tile_id = 2;
}

message act_wuzhi_research_dig_s2c {
    option (msgid) = 6717;
    uint32 act_type = 1;
    uint32 tile_id = 2;
    repeated p_reward reward = 3;//大奖不填
}


// 七星研究-发起单次灵感鉴定
message act_wuzhi_research_appraise_c2s {
    option (msgid) = 6718;
    uint32 act_type = 1;
}

message act_wuzhi_research_appraise_s2c {
    option (msgid) = 6719;
    uint32 act_type = 1;
    repeated bool appraisal_results = 2;
    uint32 appraisal_index = 3;					//鉴定次数
	repeated p_reward reward = 4;				//第四次鉴定获得的奖励
}


// 七星研究-发起摇奖
message act_wuzhi_research_draw_grand_reward_c2s {
    option (msgid) = 6720;
    uint32 act_type = 1;
}

message act_wuzhi_research_draw_grand_reward_s2c {
    option (msgid) = 6721;
    uint32 act_type = 1;
    repeated p_reward reward = 2;
}


// 七星研究-领取累计奖励
message act_wuzhi_research_claim_reward_c2s {
    option (msgid) = 6722;
    uint32 act_type = 1;
    uint32 id = 2;
}

message act_wuzhi_research_claim_reward_s2c {
    option (msgid) = 6723;
    uint32 act_type = 1;
    uint32 claim_id = 2;		//已经领取的最高奖励id
}


// 七星研究-设置一键研究的选项
message act_wuzhi_research_set_options_c2s {
    option (msgid) = 6724;
    uint32 act_type = 1;
    uint32 mode = 2;//0:顺序研究 1:随机研究
    bool auto_appraisal = 3; // 是否自动完成鉴定
}


// 七星研究-执行一键研究
message act_wuzhi_research_one_click_c2s {
    option (msgid) = 6725;
    uint32 act_type = 1;
    uint32 mode = 2;//0:顺序研究 1:随机研究
    bool auto_appraisal = 3; // 是否自动完成鉴定
}

message act_wuzhi_research_one_click_s2c {
    option (msgid) = 6726;
    uint32 act_type = 1;
    bool auto_appraisal = 2; 				// 是否自动完成鉴定
    repeated p_reward_status_list rewards = 3;		/status:格子id,p_reward:空是大奖
    repeated p_reward reward = 4;			//汇总本次获得的所有奖励
    bool stopped_by_grand_prize = 5;     // 是否因为挖到大奖而中止
}

message p_reward {
	int32 gtid = 1;
	int64 num = 2;
}

message p_reward_status_list {
	uint32 status = 1;
	repeated p_reward reward = 2;
}
给掉落例子:Map<Integer, Integer> rewardMap = ProduceManager.inst().produceAddDrop(humanObj, dropNums, MoneyItemLogKey.武魂日常奖励);
扣道具用:ProduceManager.inst().checkAndCostItem

