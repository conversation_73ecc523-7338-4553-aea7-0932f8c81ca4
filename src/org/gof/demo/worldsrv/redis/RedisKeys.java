package org.gof.demo.worldsrv.redis;

/**
 * redis键值列表
 * <AUTHOR>
 * @Date 2024/5/9
 * @Param
 */
public final class RedisKeys {
	
	/**-----------------中心服redisKey,均以admin_开头----------------------- */
	/** 角色具体信息  admin_player:playerId*/
	public static final String admin_player="admin_player:";
	/** 账号-角色id索引ct_accountPlayer:userName  */
	public static final String admin_accountPlayer="admin_accountPlayer:";


	// 服务器组，分配在一起的服务器id集合，key=admin_serverIds_time_group + date + zone + groupIndex（第几组）, value=serverId1,serverId2
 	public static final String admin_server_date_zone_group ="admin_s_t_zone_group:";
 	// serverId对应(服务器组key上面第几组groupIndex）. key=admin_server_group + date + serverId， value=groupIndex
	public static final String admin_server_group ="admin_s_group:";
	// 区对应最大组.  key=admin_server_zone_group_max + date + zone, value = max(groupIndex)
	public static final String admin_server_zone_group_max ="admin_s_zone_group_max:";

	// groupIndex对应跨服id
	public static final String admin_bridge_date_zone ="admin_b_date_zone:";


	// 获取乱斗跨服pvp分配的组当前到哪组, key=admin_pvp_group_now, value=groupIndex
	public static final String admin_pvp_group_max = "admin_pvp_group_max:";
	// 获取乱斗跨服pvp集合 key=admin_pvp_group,  groupIndex组  对象serverId
	public static final String admin_pvp_group = "admin_pvp_group:";
	// key= admin_pvp_group_season_id + group, value = GuildLeagueSeason表的id
//	public static final String admin_pvp_group_season_id = "admin_pvp_group_season_id:";
	// key + server value=groupIndex
	public static final String admin_server_merge_group = "admin_s_merge_group:";


	/**---------------------------------------- */

	/**--------------------bridge服rediskey---------------------*/


	// 乱斗组本周（4个公会内玩家排行）
	// key=bridge_date_group_guild_rank+ matchId(匹配id)
	public static final String bridge_date_group_grade_human_rank ="bridge_date_grade_human_rank:";
	// 乱斗组本周（4个公会排行）
	public static final String bridge_date_group_grade_guild_rank ="bridge_date_grade_guild_rank:";

	public static final String guild_league_remove_list = "guild_league_remove_list:";

	// key= bridge_pvp_group_guild_rank + group, score(工会积分)， 工会id
	public static final String bridge_pvp_group_guild_rank = "bridge_pvp_guild_rank:";
	// key= bridge_pvp_group_human_rank + group, score(玩家积分)，玩家id
	public static final String bridge_pvp_group_human_rank = "bridge_pvp_human_rank:";

	// 赛季的组（集合）
	public static final String bridge_pvp_season_group_list = "bridge_pvp_season_group_list:";


	// key= bridge_pvp_group_top_log, value= top记录 策划要求记录赛季玩家榜第一名玩家
	public static final String bridge_pvp_guild_top_log = "bridge_pvp_guild_top_log:";
	// key= bridge_activity_human_rank（跨服活动玩家排行榜）
	public static final String bridge_activity_human_rank = "bridge_activity_human_rank:";
	// key= bridge_activity_guild_rank（跨服活动家族排行榜）
	public static final String bridge_activity_guild_rank = "bridge_activity_guild_rank:";

	public static final String guild_human_kill = "guild_human_kill:";

	// 个人统计界面 key= guild_human_week_settle + week + humanId, value= 个人周结算信息
	public static final String guild_human_week_settle = "guild_human_week_settle:";


	//=================================跨服战===================================

	// 个人御敌榜 key= cross_war_1028_rank + group, score(积分)， 玩家id
	public static final String cross_war_1028_rank = "cross_war_1028_rank:";
	// 个人杀怪榜 key= cross_war_1042_rank + group, score(积分)， 玩家id
	public static final String cross_war_1042_rank = "cross_war_1042_rank:";
	// 个人积分榜 key= cross_war_1031_rank + group, score(积分)， 玩家id
	public static final String cross_war_1031_rank = "cross_war_1031_rank:";
	// 服务器积分 key= cross_war_1031_rank + group, score(积分), 服务器id
	public static final String cross_war_1032_rank = "cross_war_1032_rank:";

	public static final String cross_war_error="cross_war_error:";

	/** 乱斗，key= guild_league_week_road_log + guildId + week + road, value= 战报记录*/
	public static final String guild_league_week_road_log = "guild_league_week_road_log:";

	/** key = guild_league_cross_log_vid + vid */
	public static final String guild_league_cross_log_vid = "guild_league_cross_log_vid:";

	public static final String guild_league_cross_log = "guild_league_cross_log:";

	public static final String daily_remove_log = "daily_remove_log:";

	public static final String week_remove_log = "week_remove_log:";

	//=================================跨服战===================================
	/**-----------------game服rediskey----------------------- */

	/** key=world_nodeid_addr + 配置游戏服Id, value= serverAddr*/
	public static final String world_nodeid_addr ="world_nodeid_addr:";


	/** 玩家名字集合，加上serverId*/
	public static final String humanNameList="nameList:";

	/** 玩家id对应serverId*/
	public static final String humanIdServer="human_server:";


	/** 玩家战力榜（根据服id） key=humanCombatList+serverId */
	public static final String humanCombatList="combatList:";
	/** 玩家战力 key=humanCombat+humanId */
	public static final String humanCombat="combat:";
	/** 竞技场（根据服id） key=arenaList+serverId 或key=arenalList+zone+groupIndex*/
	public static final String arenaList="arenaList:";
	/** 竞技场 key=arenaRobot+机器人id */
	public static final String arenaRobot="arenaRobot:";// TODO 这个要考虑跨服，还没想好
	/** 玩家自己的对战记录 key = arenaHumanHistoryList + humanId*/

	/** key="arenaOldRank:season:serverId" value=排行榜key*/
	public static final String arenaOldRank = "arenaOldRank:";// 旧的排名key

	public static final String arenaRobotMaxNum="arenaRobotMaxNum:";//服id，最大机器人数
	public static final String arenaHumanHistoryList="arenaHumanHistoryList:";

	/** 竞技场 第几赛季结束了 key=arenaSeason+serverId ， value =赛季结束*/
	public static final String arenaSeason="arenaSeason:";
	/** 定位赛随机机器人信息， key=arenaGrading+humanId*/
	public static final String arenaGrading="arenaGrading";
	/** 竞技场排位赛报名玩家和段位 */
	public static final String arenaBridgeRankEnrollList="arenaBridgeRankEnrollList:";
	public static final String arenaRankedSeason="arenaRankedSeason:";
	/** 竞技场跨服排行榜 */
	public static final String arenaBridgeList="arenaBridgeList:";

	/** 竞技场排位赛跨服段位，房间排名。key=arenaBridgeRankRoomList+grade+room */
	public static final String arenaBridgeRankRoomList="arenaBridgeRankRoomList:";

	/** 玩家id,对应分配段位和房间，key=arenaBridgeRankRoomHuman + humanId, value= grade,room*/
	public static final String arenaBridgeRankRoomHuman="arenaBridgeRankRoomHuman:";
	/** key=arenaBridgeRankRoomKey, value= Map<grade,max(room)> */
	public static final String arenaBridgeRankRoomKey="arenaBridgeRankRoomKey:";
	/** key=arenaBridgeRankGrade + humanId, value= grade段位 */
	public static final String arenaBridgeRankGrade="arenaBridgeRankGrade:";

	/** 跨服排位赛战报记录，key=arenaRankedBridgeHistoryList+humanId, value=jsonArry(String(humanId,winId))*/
	public static final String arenaRankedBridgeHistoryList="arenaRankedBridgeHistoryList:";

	/** 玩家上次积分排名和段位 */
	public static final String arenaRankedHumanOldSocreRankGrade="arenaRankedHumanOldSocreRankGrade:";
	/** 下一期的报名玩家 */
	public static final String arenaBridgeEnrollTimeList="arenaBridgeEnrollTimeList:";

	/** arenaCrossRewardList + 赛季 + serverId， value = 奖励数据， 玩家id*/
	public static final String arenaCrossRewardList="arenaCrossRewardList:";

	/** serverIdTypeKey+服务器id+ vid，value= 战斗记录类型 key|类型*/
	public static final String serverIdVidTypeKey="serverIdTypeKey:";
	/** battleHistoryVideo+vid*/
	public static final String battleHistoryVideo="battleHistoryVideo:";

	public static final String arenaRankedRewardList="arenaRankedRewardList:";
	/** 跨服排位赛战报记录-List */
	public static final String arenaBattleRecord="arenaBattleRecord:";
	/** 排位赛战报索引-List */
	public static final String arenaRankHistory="arenaRankHistory:";
	/** 排位赛(定级)战报索引-List */
	public static final String arenaRankRobotHistory="arenaRankRobotHistory:";

	/** 排位赛最高段位排行List */
	public static final String arenaRankedTopGradeList="arena_ranked_top_grade_list:";

	/** 排位赛巅峰榜最高段位获取赛季*/
	public static final String arenaRankedTopSeason="arena_ranked_top_season:";


	/** 抓捕战报记录，待清除*/
	//	public static final String captureBattleHistoryList="captureBattleHistoryList:";
	/** 农场战报记录，待清除*/
	public static final String farmBattleHistoryList="farmBattleHistoryList:";
	/** 战报proto记录*/
	public static final String farmBattleHistoryProtoList="farmBattleHistoryProtoList:";
	/** 停车场战报记录*/
	public static final String carParkBattleHistoryList="carParkBattleHistoryList:";
	/** 抓捕战报proto记录*/
	public static final String captureBattleHistoryProtoList="captureBattleHistoryProtoList:";
	/** 跨服停车场战报记录*/
	public static final String crossParkBattleHistoryList="crossParkBattleHistoryList:";

	/** solo战报记录*/
	public static final String soloHumanHistoryList="soloHumanHistoryList:";


	/** key=chatChannelLog + channel + humanId + targetId, value= 聊天内容 */
	public static final String chatChannelLog = "chatChannelLog:";
	/** key=chatChannelLog + humanId, value=humanIdList*/
	public static final String chatHumanLog = "chatHumanLog:";
	/** key=chat2HumanUnread + humanId, value= 未读数量*/
	public static final String chat2HumanUnread = "chat2HumanUnread:";

	/** key = guildBossHurt + guildId, score = 公会boss伤害, 玩家id*/
	public static final String guildBoxRank = "guildBoxRank:";

	/** 工会胖头鱼gve 伤害排行*/
	public static final String guildGveHurt = "guildGveHurt:";

	public static final String guildBossHurt = "guildBossHurt:";

	/** 趣味竞答公会总排行榜 */
	public static final String guildQuestionRank = "guildQuestionRank";
	/** 趣味竞答公会内玩家排行榜 */
	public static final String guildQuestionInnerRank = "guildQuestionInnerRank:";


	/** 玩家信息 playerName:playerName*/
	public static final String playerName="playerName:";
	/** 公会即时排名 */
	public static final String guildInstantRank="guildInstantRank";

	// key = guild_serverId + guildId, value = serverId
	public static final String guildServerId="guild_serverId:";
	/** 公会排行榜（定时刷新） */
	public static final String guildRank="guildRank";
	/** 公会排行榜中的公会数据 */
	public static final String guildRankInfo="guildRankInfo";

	/** 排行榜 key=rank_rankSn_list + rankType表sn + serverId */
	public static final String rankSn_list="rankSn_list:";
	/** 排行榜上报记录前缀 rank_report:_actType_serverId:*/
	public static final String rankReport="rank_report:";

	/** 排行榜发奖励锁，防重复发 key=rankSn_list_lock + rankType表sn + serverId */
	public static final String rankSn_list_lock="rankSn_list_lock:";

	// ==========================跨服竞技场=============================
	public static final String arenaCrossSeason="arena_cross_season:";
	public static final String arenaCrossWeek="arena_cross_week:";
	public static final String arenaCrossRobot="arena_cross_robot:";
	public static final String arenaCrossServerIdCombat="arena_cross_serverId_combat:";

	public static final String arenaCrossRobotScore="arena_cross_robot_score:";

	/** 竞技场（根据服id） key=arenaCrossList+season+group+groupIndex*/
	public static final String arenaCrossList="arena_cross_list:";

	public static final String arenaCrossHistory = "arena_cross_history:";

	public static final String arenaCrossHistoryVIdList = "arena_cross_history_vid_list:";

	// ==========================跨服竞技场=============================

	public static final String repTypeDiff="repTypeDiff";

	public static final String worldBoss="worldBoss";

	public static final String worldBossHurtList="worldBoss:";

	public static final String fateDungeon = "fateDungeon";

	public static final int getType_1 = 1; // 获取类型（get）
	public static final int getType_2 = 2; // 获取类型（）
	public static final int getType_3 = 3; // hgetAll
	public static final int getType_4 = 4;  // 获取某个表，部分字段
	public static final int getType_7 = 7;	// 根据部分key获取数据，如mail表。tableKey+humanId+*(查询玩家所有所有邮件)

	public static final int setType_1 = 1; // 设置类型（set）
	public static final int setType_2 = 2; // 设置类型（先get 对比 后 set）
	public static final int setType_3 = 3; // hset方法
	public static final int setType_4 = 4; // hmset方法(慎用，部分redis版本已经废弃)
	public static final int setType_5 = 5; // 设置类型（set）部分字段



	public static final int setType_2_ObjType = 1; // 2类型的子类型


	public static final int selectSqlType_1 = 1;	// 查表格, map<表名， 查询条件json(字段名，值)>
	public static final int selectSqlType_2 = 2; 	// 查所有表

	public static final String tableKey ="table:";
	public static final String tableSelectParamKey ="tabSelect";
	public static final String isSave ="isSave";



	public static final String selectRedisKey ="selectRedisKey"; // 查询redis的key
	public static final String selectRedisType ="selectRedisType"; // 查询redis的返回
	// todo: 新版的redisKey
	/** 活动播报 key=actBroadcast.serverId.type.round */
	public static final String actBroadcast ="actBroadcast";
	/** 在定时器的庄园（根据服id） key="farmInTimer.serverId" */
	public static final String farmInTimer ="farmInTimer";
	/** 玩家登入时间排行榜前100名（根据服id） key="humanLoginTimeSorted.serverId" */
	public static final String humanLoginTimeSorted ="humanLoginTimeSorted";
	/** 抓捕锁（根据玩家id） key="captureLock.humanID" */
	public static final String captureLock ="captureLock";
	/** 抓捕锁（根据玩家id） key="captureCreateLock.humanID" */
	public static final String captureCreateLock ="captureCreateLock";
	/** 私人车位锁（根据玩家id） key="serverParkHumanSet.serverId" */
	public static final String serverParkHumanSet ="serverParkHumanSet";
	/** 公共车位锁（根据玩家id） key="humanParkBest.serverId" */
	public static final String humanParkBest ="humanParkBest";

	/** 公共车位锁（根据玩家id） key="publicParkLock.serverId.pos" */
	public static final String humanParkLock ="humanParkLock";
	/** 公共车位锁（根据玩家id） key="publicParkLock.serverId.pos" */
	public static final String publicParkLock ="publicParkLock";
	/** 跨服车位锁（根据玩家id） key="crossParkLock.id.pos" */
	public static final String crossParkLock ="crossParkLock";
	/** 跨服车位锁（根据玩家id） key="createCrossParkLock.date.zone.groupIndex" */
	public static final String createCrossParkLock ="createCrossParkLock";
	/** 跨服车位排行="crossCarCoinRank.date.zone.groupIndex" */
	public static final String crossCarCoinRank ="crossCarCoinRank";
	/** 跨服车位排行="crossParkAtkRank.date.zone.groupIndex" */
	public static final String crossParkAtkRank ="crossParkAtkRank";
	/** 跨服车位排行="crossParkDefRank.date.zone.groupIndex" */
	public static final String crossParkDefRank ="crossParkDefRank";
	/** 跨服服务器拥有的车位 = "crossServerParkSet.date.serverId" */
	public static final String crossServerParkSet ="crossServerParkSet";
	/** 跨服车位攻击列表 = "crossParkRobList.date.id" */
	public static final String crossParkRobList ="crossParkRobList";
	/** 跨服车位防御列表 = "crossParkGuardList.date.id" */
	public static final String crossParkGuardList ="crossParkGuardList";
	/** 跨服车战斗数据 = "crossParkBattleList.date.id" */
	public static final String crossParkBattleList ="crossParkBattleList";

	/** 消息推送自增uid = "pushMsg" */
	public static final String pushMsg ="pushMsg";
	/** 消息推送玩家id推送时间列表 = "offlinePushList.serverId" */
	public static final String offlinePushList ="offlinePushList";

	public static final String humanIdFillMailIdList ="humanIdFillMailIdList:";



	//=================================武道会开始===================================
	// 武道会排位赛排行榜 key = kung_fu_race_qualifying_rank:seasonName  [玩家id，score(积分)]
	public static final String kung_fu_race_qualifying_rank = "kung_fu_race_qualifying_rank:";
	public static final String kung_fu_race_error="kung_fu_race_error:";
	public static final String kung_fu_race_final_rank="kung_fu_race_final_rank:";
    // 武道会已发结算奖励
    public static final String kung_fu_race_settled_reward="kung_fu_race_settled_reward:";
	//=================================武道会结束===================================

    //=================================乱斗32强开始===================================
    public static final String family32_error="family32_error:";
    public static final String family32_final_rank="family32_final_rank:";
    // 乱斗32强已发结算奖励
    public static final String family32_settled_reward="family32_settled_reward:";
    //=================================乱斗32强结束===================================

	// 今日我已点赞的玩家id集合 charmTodayLikeSet:humanId:date
	public static final String charmTodayLikeSet = "charmTodayLikeSet:";
	// 收藏室的总点赞次数 charmLikeTotalNum:humanId
	public static final String charmLikeTotalNum = "charmLikeTotalNum:";
	// 点赞记录列表 charmLikeRecordList:humanId
	public static final String charmLikeRecordList = "charmLikeRecordList:";

	public static final String act_camp_human_id_set = "act_camp_human_id_set:";// 阵营对抗，记录阵营玩家id（需要合并）
	public static final String arenaRankedDaily = "arena_ranked_daily:";// 记录每日排位赛战斗防守者被挑战次数（策划需求改变准备作废）
	public static final String arenaRankedDailyWin = "arena_ranked_daily_win:";// 记录每日排位赛战斗防守者被挑战者成功次数
	public static final String arenaRankedDailyLose = "arena_ranked_daily_lose:";// 记录每日排位赛战斗防守者被挑战者失败次数

	public static final String act_lucky_lottery_code = "act_lucky_lottery_code:";
}
