package org.gof.demo.worldsrv.family32;

import org.gof.core.support.observer.MsgReceiver;
import org.gof.demo.seam.msg.MsgParam;
import org.gof.demo.worldsrv.character.HumanObject;
import org.gof.demo.worldsrv.guild.GuildManager;
import org.gof.demo.worldsrv.msg.MsgFamily32;
import org.gof.demo.worldsrv.msg.MsgGvg;

public class Family32MsgHandler {
	/**
	 * 信息总览
	 * @param param
	 */
	@MsgReceiver(MsgFamily32.family32_info_c2s.class)
	public void _msg_family32_info_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
		Family32Manager.inst()._msg_family32_info_c2s(humanObj, false);
	}

	/**
	 * 获取公会详情
	 * @param param
	 */
	@MsgReceiver(MsgFamily32.family32_guild_info_c2s.class)
	public void _msg_family32_guild_info_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
		MsgFamily32.family32_guild_info_c2s msg = param.getMsg();
		Family32Manager.inst()._msg_family32_guild_info_c2s(humanObj, msg.getGuildId());
	}

    /**
     * 获取战场信息
     * @param param
     */
    @MsgReceiver(MsgFamily32.family32_gvg_info_c2s.class)
    public void _msg_family32_gvg_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFamily32.family32_gvg_info_c2s msg = param.getMsg();
        Family32Manager.inst()._msg_family32_gvg_info_c2s(humanObj, msg.getBattleIndex(), msg.getCompetitionId());
    }

    /**
     * 获取线路信息
     * @param param
     */
    @MsgReceiver(MsgFamily32.family32_road_info_c2s.class)
    public void _msg_family32_road_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFamily32.family32_road_info_c2s msg = param.getMsg();
        Family32Manager.inst()._msg_family32_road_info_c2s(humanObj, msg.getGuildId(), msg.getBattleIndex(), msg.getRoad());
    }

	/**
	 * 选择线路
	 * @param param
	 */
	@MsgReceiver(MsgFamily32.family32_select_road_c2s.class)
	public void _msg_family32_select_road_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
        MsgFamily32.family32_select_road_c2s msg = param.getMsg();
		Family32Manager.inst()._msg_family32_select_road_c2s(humanObj, msg.getBattleIndex(), msg.getRoad());
	}

    /**
     * 保存分路
     * @param param
     */
    @MsgReceiver(MsgFamily32.family32_road_change_c2s .class)
    public void _msg_family32_road_change_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFamily32.family32_road_change_c2s msg = param.getMsg();
        Family32Manager.inst()._msg_family32_road_change_c2s(humanObj, msg.getBattleIndex(), msg.getRoad(), msg.getRoadInfoList());
    }

    /**
     * 切换分路
     * @param param
     */
    @MsgReceiver(MsgFamily32.family32_road_change_all_c2s.class)
    public void _msg_family32_road_change_all_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFamily32.family32_road_change_all_c2s msg =  param.getMsg();
        Family32Manager.inst()._msg_gvg_road_change_all_c2s(humanObj, msg.getBattleIndex(), msg.getRoad1(), msg.getRoad2());
    }

    /**
     * 获取战斗信息
     * @param param
     */
    @MsgReceiver(MsgFamily32.family32_fight_info_c2s.class)
    public void _msg_family32_fight_info_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFamily32.family32_fight_info_c2s msg = param.getMsg();
        Family32Manager.inst()._msg_family32_fight_info_c2s(humanObj, msg.getCompetitionId(), msg.getRoad());
    }

    /**
     * 获取战报
     * @param param
     */
    @MsgReceiver(MsgFamily32.family32_fight_report_c2s.class)
    public void _msg_family32_fight_report_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFamily32.family32_fight_report_c2s msg = param.getMsg();
        Family32Manager.inst()._msg_family32_fight_report_c2s(humanObj, msg.getCompetitionId(), msg.getRoad());
    }

    /**
     * 播放战斗录像
     * @param param
     */
    @MsgReceiver(MsgFamily32.family32_play_video_c2s.class)
    public void _msg_family32_play_video_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        MsgFamily32.family32_play_video_c2s msg = param.getMsg();
        Family32Manager.inst()._msg_family32_play_video_c2s(humanObj, msg.getCompetitionId(), msg.getVid());
    }

	/**
	 * 获取循环赛信息
	 * @param param
	 */
	@MsgReceiver(MsgFamily32.family32_loop_c2s.class)
	public void _msg_family32_loop_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
		MsgFamily32.family32_loop_c2s msg = param.getMsg();
		Family32Manager.inst()._msg_family32_loop_c2s(humanObj, msg.getGroup(), msg.getRound(), msg.getStage());
	}

	/**
	 * 获取循环赛积分榜
	 * @param param
	 */
	@MsgReceiver(MsgFamily32.family32_loop_rank_c2s.class)
	public void _msg_family32_loop_rank_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
		MsgFamily32.family32_loop_rank_c2s msg = param.getMsg();
		Family32Manager.inst()._msg_family32_loop_rank_c2s(humanObj, msg.getGroup(), msg.getStage());
	}

	/**
	 * 获取循环赛竞猜信息
	 * @param param
	 */
	@MsgReceiver(MsgFamily32.family32_loop_bet_info_c2s.class)
	public void _msg_family32_loop_bet_info_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
		MsgFamily32.family32_loop_bet_info_c2s msg = param.getMsg();
		Family32Manager.inst()._msg_family32_loop_bet_info_c2s(humanObj, msg.getGroup(), msg.getAction(), msg.getStage());
	}

    /**
     * 获取淘汰赛信息
     * @param param
     */
    @MsgReceiver(MsgFamily32.family32_knockout_c2s.class)
    public void _msg_family32_knockout_c2s(MsgParam param) {
        HumanObject humanObj = param.getHumanObject();
        Family32Manager.inst()._msg_family32_knockout_c2s(humanObj);
    }

	/**
	 * 获取名人堂信息
	 * @param param
	 */
	@MsgReceiver(MsgFamily32.family32_champion_history_c2s.class)
	public void _msg_family32_champion_history_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
		MsgFamily32.family32_champion_history_c2s msg = param.getMsg();
		Family32Manager.inst()._msg_family32_champion_history_c2s(humanObj, msg.getSeason());
	}

	/**
	 * 获取庆祝期信息
	 * @param param
	 */
	@MsgReceiver(MsgFamily32.family32_champion_info_c2s.class)
	public void _msg_family32_champion_info_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
		Family32Manager.inst()._msg_family32_champion_info_c2s(humanObj);
	}

	/**
	 * 膜拜冠军
	 * @param param
	 */
	@MsgReceiver(MsgFamily32.family32_worship_c2s.class)
	public void _msg_family32_worship_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
		Family32Manager.inst()._msg_family32_worship_c2s(humanObj);
	}

	/**
	 * 竞猜比赛
	 * @param param
	 */
	@MsgReceiver(MsgFamily32.family32_bet_c2s.class)
	public void _msg_family32_bet_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
		MsgFamily32.family32_bet_c2s msg = param.getMsg();
		Family32Manager.inst()._msg_family32_bet_c2s(humanObj, msg.getStage(), msg.getGuildId(), msg.getBetNum());
	}

	/**
	 * 获取竞猜历史
	 * @param param
	 */
	@MsgReceiver(MsgFamily32.family32_bet_history_c2s.class)
	public void _msg_family32_bet_history_c2s(MsgParam param) {
		HumanObject humanObj = param.getHumanObject();
		Family32Manager.inst()._msg_family32_bet_history_c2s(humanObj);
	}
}
