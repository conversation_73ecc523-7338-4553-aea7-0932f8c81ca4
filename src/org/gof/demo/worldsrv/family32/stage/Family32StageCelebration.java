package org.gof.demo.worldsrv.family32.stage;

import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import org.gof.core.Port;
import org.gof.core.RemoteNode;
import org.gof.core.dbsrv.redis.AsyncActionResult;
import org.gof.core.dbsrv.redis.CrossRedis;
import org.gof.core.dbsrv.redis.EntityManager;
import org.gof.core.dbsrv.redis.RedisTools;
import org.gof.core.support.Utils;
import org.gof.demo.distr.DistrKit;
import org.gof.demo.worldsrv.bridgeEntity.Family32ChampionInfo;
import org.gof.demo.worldsrv.config.ConfFamily32Reward;
import org.gof.demo.worldsrv.config.ConfFamily32Stage;
import org.gof.demo.worldsrv.family32.Family32Const;
import org.gof.demo.worldsrv.family32.vo.Family32RewardVO;
import org.gof.demo.worldsrv.family32.Family32Utils;
import org.gof.demo.worldsrv.family32.Family32Zone;
import org.gof.demo.worldsrv.family32.data.Family32CompetitionData;
import org.gof.demo.worldsrv.family32.data.Family32GuildData;
import org.gof.demo.worldsrv.guild.GuildServiceProxy;
import org.gof.demo.worldsrv.human.HumanGlobalServiceProxy;
import org.gof.demo.worldsrv.redis.RedisKeys;
import org.gof.demo.worldsrv.support.Log;

import java.util.*;

public class Family32StageCelebration extends Family32Stage {
	// 结算奖励VOMap
	private final Map<Integer, Map<Long, Family32RewardVO>> m_serverRewardVOMap = new HashMap<>();

	public Family32StageCelebration(Family32Zone zone, ConfFamily32Stage conf) {
		super(zone, conf);
	}

	@Override
	public void onStart(Handler<AsyncResult<Void>> onComplete) {
		// 计算排名信息
		List<Family32CompetitionData> compList = m_zone.getAllCompetition();
		for(Family32CompetitionData compData : compList){
			m_zone.addFinalRank(compData);
		}
        // 增加冠亚军的最终排名
        m_zone.addTop2FinalRank();
		Map<Integer, Set<Long>> finalRankSetMap = m_zone.getFinalRankSetMap();
		for(Map.Entry<Integer, Set<Long>> entry : finalRankSetMap.entrySet()){
			int battleFlag = entry.getKey();
			Set<Long> rankSet = entry.getValue();
			if(rankSet.size() != battleFlag){
				Log.family32.warn("最终排名信息数量错误 rankSet.size()={} != battleFlag={}", rankSet.size(), battleFlag);
				AsyncActionResult.fail(port, onComplete, new Exception("最终排名信息数量错误"));
				return;
			}
		}
		Set<Long> championSet = finalRankSetMap.getOrDefault(1, new HashSet<>());
		long championGuildId = championSet.isEmpty() ? 0 : championSet.iterator().next();
		if(championGuildId == 0){
			Log.family32.warn("冠军公会id不存在");
			AsyncActionResult.fail(port, onComplete, new Exception("冠军公会id不存在"));
			return;
		}
		Family32GuildData championGuildData = m_zone.getGuild(championGuildId);
		if(championGuildData == null){
			Log.family32.warn("冠军公会信息不存在");
			AsyncActionResult.fail(port, onComplete, new Exception("冠军公会信息不存在"));
			return;
		}
		int season = Family32Utils.getCurrentSeason();
		Family32ChampionInfo championInfo = m_zone.getChampion(season);
		if(championInfo != null){
			if(!championInfo.getGuildName().equals(championGuildData.getGuildName())){
				Log.family32.warn("冠军公会名字检查失败");
				AsyncActionResult.fail(port, onComplete, new Exception("冠军公会名字检查失败"));
				return;
			}
		}
		else{
			// 新一届冠军，加入名人堂
			championInfo = new Family32ChampionInfo();
			championInfo.setId(Port.applyId());
			championInfo.setZoneId(getZoneId());
			championInfo.setSeason(season);
            championInfo.setGuildId(championGuildData.getGuildId());
			championInfo.setGuildName(championGuildData.getGuildName());
            championInfo.setServerId(championGuildData.getGuildServerId());
            championInfo.setTotalCombat(championGuildData.getTotalCombat().toString());
			championInfo.setLeaderId(championGuildData.getLeaderId());
            championInfo.setGuildFlagJson(championGuildData.getFlagJSON());
            championInfo.setZoneMinServerId(m_zone.getMinServerId());
            championInfo.setZoneMaxServerId(m_zone.getMaxServerId());
			championInfo.persist();
			m_zone.addChampionInfo(championInfo);
		}
		Log.family32.info("{} season={} 冠军公会={} 信仰值={}", getZoneName(), season, championGuildId, championInfo.getWorship());
		// 回收竞猜币
		recycleBetCoin();
		AsyncActionResult.success(port, onComplete, null);
	}

	/**
	 * 回收竞猜币
	 */
	private void recycleBetCoin(){
		Set<String> nodes = m_zone.getBroadcastNodes();
		for(String nodeId : nodes){
			HumanGlobalServiceProxy proxy = HumanGlobalServiceProxy.newInstance(nodeId);
			proxy.family32RecycleBetCoin();
		}
		Log.family32.info("==={} 回收竞猜币 广播消息给{}个游戏服", getZoneName(), nodes.size());
	}

    /**
     * 阶段结束
     */
    @Override
    public void onEnd(Handler<AsyncResult<Void>> onComplete) {
        String settledRewardKey = Family32Utils.getSettledRewardKey(getZoneId());
        RedisTools.get(EntityManager.redisClient, settledRewardKey, res->{
            if (res.failed()) {
                Log.family32.warn("==={} 结算赛季奖励失败 settledRewardKey不存在", getZoneName());
                AsyncActionResult.fail(port, onComplete, new Exception("settledRewardKey不存在"));
                return;
            }
            boolean isSettled = Objects.equals(res.result(), "1");
            if(isSettled){
                Log.family32.info("{} 赛季奖励已结算", getZoneName());
            }
            else{
                Log.family32.info("{} 结算赛季奖励", getZoneName());
                settleSeasonReward();
            }
            AsyncActionResult.success(port, onComplete, null);
        });
    }

	/**
	 * 结算赛季奖励
	 */
	public void settleSeasonReward(){
		Set<Long> alreadyCreateRewardSet = new HashSet<>();
		// 创建32强以上的奖励，奖励从高往低发，发过名次高的不再发名次低的
		Map<Integer, Set<Long>> finalRankSetMap = m_zone.getFinalRankSetMap();
		for(Map.Entry<Integer, Set<Long>> entry : finalRankSetMap.entrySet()){
			int battleFlag = entry.getKey();
			Set<Long> rankSet = entry.getValue();
			int confSn = Family32Utils.getCompetitionRank(battleFlag);
            ConfFamily32Reward conf = ConfFamily32Reward.get(confSn);
			if(conf == null){
				Log.family32.error("===ConfFamily32Reward 配置不存在，confSn={}", confSn);
				continue;
			}
			for(long guildId : rankSet){
				Family32GuildData guildData = m_zone.getGuild(guildId);
				if(guildData == null){
					Log.family32.error("===settleSeasonReward guild={}不存在", guildId);
					continue;
				}
				if(alreadyCreateRewardSet.contains(guildId)){
					continue;
				}
				alreadyCreateRewardSet.add(guildId);
				createRewardVO(guildData, confSn, battleFlag);
			}
		}
		// 创建保底奖励
		int confSn = Family32Utils.getCompetitionRank(0);
        ConfFamily32Reward conf = ConfFamily32Reward.get(confSn);
		if(conf == null){
			Log.family32.error("===ConfFamily32Reward 配置不存在，confSn={}", confSn);
			return;
		}
		List<Family32GuildData> allGuildList = m_zone.getAllGuild();
		for(Family32GuildData guildData : allGuildList){
			long guildId = guildData.getGuildId();
			if(alreadyCreateRewardSet.contains(guildId)){
				continue;
			}
			alreadyCreateRewardSet.add(guildId);
			// 999表示未上榜
			createRewardVO(guildData, confSn, 999);
		}
		if(alreadyCreateRewardSet.size() != allGuildList.size()){
			Log.family32.error("{} 创建结算奖励的队伍数量错误 alreadyCreateRewardSet.size()={} != allGuildList.size()={}",
					getZoneName(), alreadyCreateRewardSet.size(), allGuildList.size());
			return;
		}
		Log.family32.info("{} 创建结算奖励的公会数量={}", getZoneName(), alreadyCreateRewardSet.size());
		// 发送奖励到游戏服
		sendRewardToGameServer();
	}

	/**
	 * 创建奖励VO
	 * @param guildData
	 * @param confSn
	 * @param rank
	 */
	private void createRewardVO(Family32GuildData guildData, int confSn, int rank){
        long guildId = guildData.getGuildId();
        int serverId = Utils.getServerIdByHumanId(guildId);
        boolean isAbsent = m_serverRewardVOMap.computeIfAbsent(serverId, k -> new HashMap<>())
                .putIfAbsent(guildId, new Family32RewardVO(guildId, confSn, guildData.getMemberIdSet())) == null;
        if(isAbsent){
            // 在redis记录最终名次，不使用，方便测试查看
            String redisKey = Family32Utils.getFinalRankKey(getZoneId());
            RedisTools.addRank(EntityManager.redisClient, redisKey, guildId, rank);
            RedisTools.expire(EntityManager.redisClient, redisKey, Family32Const.FAMILY32_REDIS_KEY_EXPIRE_TIME);
            Log.family32.info("createRewardVO guildId={}, confSn={}", guildId, confSn);
        }
	}

	/**
	 * 发送奖励到游戏服
	 */
	private void sendRewardToGameServer(){
		int totalRewardNum = 0;
		for(Map.Entry<Integer, Map<Long, Family32RewardVO>> entry : m_serverRewardVOMap.entrySet()){
			int serverId = entry.getKey();
			Map<Long, Family32RewardVO> rewardVOMap = entry.getValue();
			String worldNodeId = DistrKit.getWorldNodeID(serverId);
			RemoteNode rn = Port.getCurrent().getNode().getRemoteNode(worldNodeId);
			if(rn == null) {
				Log.family32.warn("===sendRewardToGameServer fail. worldNodeId={}未连接", worldNodeId);
				// 乱斗32强结算发送失败备份
				saveErrorBackup(false, serverId, rewardVOMap);
				continue;
			}
			try{
                GuildServiceProxy proxy = GuildServiceProxy.newInstance(worldNodeId);
				Log.family32.info("乱斗32强发送奖励到游戏服={} 公会数={}", serverId, rewardVOMap.size());
				totalRewardNum += rewardVOMap.size();
				proxy.sendFamily32Reward(rewardVOMap);
				proxy.listenResult((timeout, results, context) -> {
					boolean bResult = Utils.getParamValue(results, "result", false);
					if (bResult) {
						// 返回正常
						return;
					}
					// 乱斗32强结算发送失败备份
					saveErrorBackup(timeout, serverId, rewardVOMap);
				});
			}catch (Exception e){
				Log.family32.error("===sendRewardToGameServer 失败，serverId={}", serverId, e);
				// 乱斗32强结算发送失败备份
				saveErrorBackup(false, serverId, rewardVOMap);
			}
		}
        // 设置已发结算奖励
        String settledRewardKey = Family32Utils.getSettledRewardKey(getZoneId());
        RedisTools.setAndExpire(EntityManager.redisClient, settledRewardKey, "1", Family32Const.FAMILY32_REDIS_KEY_EXPIRE_TIME);
		Log.family32.info("乱斗32强发送奖励到所有游戏服的总公会数={}", totalRewardNum);
	}

	/**
	 * 乱斗32强结算发送失败备份
	 * @param timeout
	 * @param serverId
	 * @param rewardVOMap
	 */
	private void saveErrorBackup(boolean timeout, int serverId, Map<Long, Family32RewardVO> rewardVOMap){
		Log.family32.warn("===timeout ={}, serverId={}, 乱斗32强结算发送失败备份={}", timeout, serverId, rewardVOMap.size());
		List<String> keyList = new ArrayList<>();
		keyList.add(RedisKeys.family32_error + serverId);
		for(Family32RewardVO vo : rewardVOMap.values()){
			keyList.add(vo.toString());
		}
		CrossRedis.sadd(keyList);
	}

}

