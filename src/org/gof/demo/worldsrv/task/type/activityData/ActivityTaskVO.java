package org.gof.demo.worldsrv.task.type.activityData;

import com.alibaba.fastjson.JSONObject;
import org.gof.core.InputStream;
import org.gof.core.OutputStream;
import org.gof.core.interfaces.ISerilizable;
import org.gof.core.support.Utils;
import org.gof.demo.worldsrv.activity.ActivityControlType;
import org.gof.demo.worldsrv.activity.ActivityControlTypeFactory;
import org.gof.demo.worldsrv.activity.calculator.ActivityControlWarToken;
import org.gof.demo.worldsrv.config.ConfActivityTask;
import org.gof.demo.worldsrv.config.ConfBattlePassTask;
import org.gof.demo.worldsrv.config.ConfWartokenTask;
import org.gof.demo.worldsrv.support.Log;
import org.gof.demo.worldsrv.task.TaskConditionTypeKey;
import org.gof.demo.worldsrv.task.type.TaskVO;

import java.io.IOException;
import java.util.HashMap;
import java.util.Set;


/** 
 * 活动任务
 * <AUTHOR>
 * @Date 2024/7/16
 * @Param 
 */
public class ActivityTaskVO extends TaskVO implements ISerilizable {
	private int activitySn;				//活动sn
	private int activityType;			//活动类型
	private int groupSn;				//活动任务组sn

	public ActivityTaskVO(int activitySn, int activityType, ConfActivityTask confActivityTask, int groupSn) {
		this.activitySn = activitySn;
		this.activityType = activityType;
		taskSn = confActivityTask.sn;
		setType(TaskConditionTypeKey.TASK_活动);
		this.groupSn = groupSn;
		initConf(confActivityTask);
	}

	public ActivityTaskVO(JSONObject jo) {
		taskSn = jo.getIntValue("sn");
		if(jo.containsKey("taskSn")){
			taskSn = jo.getIntValue("taskSn");
		}
		setType(jo.getIntValue("t"));
		setConditionType(jo.getIntValue("ct"));
		setStatus(jo.getIntValue("st"));
		setPlan(jo.getIntValue("pl"));
		activitySn = jo.getIntValue("actSn");
		activityType = jo.getIntValue("actType");
		groupSn = jo.getIntValue("gSn");
		setParam1(jo.getIntValue("p1"));

		Set<Integer> warTokenTypeSet = ActivityControlTypeFactory.getTypeSet(ActivityControlWarToken.class);
		if (activityType == ActivityControlType.Act_2100) {
			ConfBattlePassTask confBattlePassTask = ConfBattlePassTask.get(taskSn);
			if(confBattlePassTask == null){
				Log.temp.error("Wartoken task not found, taskSn={}, activityType={}, activitySn={}" , taskSn, activityType, activitySn);
				return;
			}
			setConditionType(confBattlePassTask.condition[0]);
			if(confBattlePassTask.condition[1]!=0){
				setParam1(confBattlePassTask.condition[1]);
			}
			setSumPlan(confBattlePassTask.condition[2]);
		} else if (warTokenTypeSet.contains(activityType)) {
			ConfWartokenTask confWartokenTask = ConfWartokenTask.get(taskSn);
			if(confWartokenTask == null){
				Log.temp.error("Wartoken task not found, taskSn={}, activityType={}, activitySn={}" , taskSn, activityType, activitySn);
				return;
			}
			setConditionType(confWartokenTask.condition[0]);
			if(confWartokenTask.condition[1]!=0){
				setParam1(confWartokenTask.condition[1]);
			}
			setSumPlan(confWartokenTask.condition[2]);
		} else {
			ConfActivityTask confActivityTask = ConfActivityTask.get(taskSn);
			if(confActivityTask == null){
				Log.temp.error("活动任务找不到 Activity task not found, taskSn={}", taskSn);
				return;
			}
			initConf(confActivityTask);
		}
	}

	public ActivityTaskVO(int activitySn,int activityType, int groupSn, int taskSn, int taskType, int[] condition) {
		super(taskSn, taskType, condition, null, 0);
		this.activitySn = activitySn;
		this.activityType = activityType;
		this.groupSn = groupSn;
	}

	private void initConf(ConfActivityTask confActivityTask){
		if(confActivityTask == null){
			Log.temp.error("活动任务可能删除 Activity task not found");
			return;
		}
		taskSn = confActivityTask.sn;
		setConditionType(confActivityTask.condition[0]);
		if(confActivityTask.condition[1]!=0){
			setParam1(confActivityTask.condition[1]);
		}
		setSumPlan(confActivityTask.condition[2]);
		setAwards(Utils.intArrToIntMap(new HashMap<>(), confActivityTask.reward));
	}

	public ActivityTaskVO() {}

	public int getActivitySn(){
		return activitySn;
	}
	public void setActivitySn(int activitySn) {
		this.activitySn = activitySn;
	}

	public void setActivityType(int activityType) {
		this.activityType = activityType;
	}

	public int getActivityType(){
		return activityType;
	}

	public int getGroupSn() {
		return groupSn;
	}

	public void setGroupSn(int groupSn) {
		this.groupSn = groupSn;
	}

	@Override
	public void writeTo(OutputStream out) throws IOException {
		super.writeTo(out);
	}

	@Override
	public void readFrom(InputStream in) throws IOException {
		super.readFrom(in);
	}

	@Override
	public String toString() {
		JSONObject jo = Utils.toJSONObject(super.toString());
		jo.put("actSn", activitySn);
		jo.put("actType", activityType);
		jo.put("gSn", groupSn);
		jo.put("p1",getParam1());
		return jo.toJSONString();
	}

	public JSONObject toJSONObj() {
		JSONObject jo = Utils.toJSONObject(super.toString());
		Utils.putIfNonZero(jo, "actSn", activitySn);
		Utils.putIfNonZero(jo, "actType", activityType);
		Utils.putIfNonZero(jo, "gSn", groupSn);
		Utils.putIfNonZero(jo, "taskSn", taskSn);
		Utils.putIfNonZero(jo, "pl", getPlan());
		Utils.putIfNonZero(jo, "st", getStatus());
		Utils.putIfNonZero(jo, "t", getType());

		return jo;
	}

}

